import { createTheme } from '@mui/material/styles';
import { customColors } from 'config';

const customTheme = createTheme({
    palette: {
        mode: 'dark',
        primary: {
            main: customColors.accentGold,
            light: customColors.accentLightGold,
            dark: '#e0a930',
            contrastText: '#ffffff'
        },
        secondary: {
            main: customColors.chartBlue,
            light: '#4fc3f7',
            dark: '#0288d1',
            contrastText: '#ffffff'
        },
        background: {
            default: customColors.darkTeal,
            paper: customColors.mediumTeal
        },
        text: {
            primary: '#ffffff',
            secondary: '#b0bec5'
        },
        success: {
            main: customColors.chartGreen
        }
    },
    components: {
        MuiPaper: {
            styleOverrides: {
                root: {
                    backgroundColor: customColors.mediumTeal,
                    backgroundImage: 'none'
                }
            }
        },
        MuiAppBar: {
            styleOverrides: {
                root: {
                    backgroundColor: customColors.darkTeal,
                    backgroundImage: 'none'
                }
            }
        },
        MuiButton: {
            styleOverrides: {
                root: {
                    color: '#ffffff',
                    '&.MuiButton-contained': {
                        backgroundColor: customColors.accentGold,
                        '&:hover': {
                            backgroundColor: '#e0a930'
                        }
                    },
                    '&.MuiButton-outlined': {
                        borderColor: customColors.accentGold,
                        color: customColors.accentGold
                    }
                }
            }
        },
        MuiDrawer: {
            styleOverrides: {
                paper: {
                    backgroundColor: customColors.darkTeal,
                    backgroundImage: 'none'
                }
            }
        }
    }
});

export default customTheme;

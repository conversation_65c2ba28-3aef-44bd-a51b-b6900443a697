import React, { useState } from 'react';
import { Box, Typography, Grid, Divider } from '@mui/material';

const ScheduleMatrix = ({ initialScheduleMatrix, handleScheduleUpdate, editable }) => {
    const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const timeSlots = Array.from({ length: 96 }, (_, i) => {
        const hours = String(Math.floor(i / 4)).padStart(2, '0');
        const minutes = String((i % 4) * 15).padStart(2, '0');
        return { time: `${hours}:${minutes}`, showDivider: i % 4 === 3 }; // Show divider only at the end of each hour
    });

    const isSlotActive = (dayIndex, timeIndex) => {
        return initialScheduleMatrix[dayIndex][timeIndex];
    };

    const handleScheduleSlotClick = (day, timeSlot) => {
        if (!editable) return;
        const updatedMatrix = initialScheduleMatrix.map((row, rowIndex) =>
            row.map((slot, slotIndex) => {
                if (rowIndex === day && slotIndex === timeSlot) {
                    return !slot; // Toggle the slot
                }
                return slot;
            })
        );
        handleScheduleUpdate(updatedMatrix);
    };

    return (
        <Box sx={{ maxHeight: '19vh', overflowY: 'auto', width: '40vh' }}>
            <Grid container sx={{ width: 'auto', minWidth: '100%', boxSizing: 'border-box' }}>
                <Grid item xs={1}>
                    {' '}
                    {/* Empty cell for alignment */}
                </Grid>
                {daysOfWeek.map((day, index) => (
                    <Grid item xs={1.5} key={index}>
                        <Typography align="center" sx={{ fontWeight: 'bold', fontSize: '0.75rem' }}>
                            {day}
                        </Typography>
                    </Grid>
                ))}
                {timeSlots.map((timeSlot, timeIndex) => (
                    <React.Fragment key={timeIndex}>
                        <Grid container item xs={12} spacing={1} style={{ paddingTop: 0 }}>
                            <Grid item xs={1}>
                                <Typography sx={{ fontSize: '0.75rem', padding: '2px 0' }}>{timeSlot.time}</Typography>
                            </Grid>
                            {daysOfWeek.map((_, dayIndex) => (
                                <Grid item xs={1.5} key={dayIndex} style={{ paddingTop: 8 }}>
                                    <Box
                                        sx={{
                                            width: '100%',
                                            height: '15px', // Reduced height
                                            backgroundColor: isSlotActive(dayIndex, timeIndex) ? 'green' : 'grey',
                                            margin: '0px',
                                            padding: '0px',
                                            cursor: editable ? 'pointer' : 'default',
                                            borderRight: dayIndex < 6 ? '1px solid #ccc' : 'none'
                                        }}
                                        onClick={() => handleScheduleSlotClick(dayIndex, timeIndex)}
                                    />
                                </Grid>
                            ))}
                            {timeSlot.showDivider && (
                                <Grid item xs={12} style={{ paddingTop: 0 }}>
                                    <Divider sx={{ marginY: 0.5 }} />
                                </Grid>
                            )}
                        </Grid>
                    </React.Fragment>
                ))}
            </Grid>
        </Box>
    );
};

export default ScheduleMatrix;

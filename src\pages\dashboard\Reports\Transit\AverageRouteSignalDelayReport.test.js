import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import AverageRouteSignalDelayReport from './AverageRouteSignalDelayReport';
import ApiService from '../../../../utils/ApiService';

// Mock the ApiService
jest.mock('../../../../utils/ApiService');

// Mock ApexCharts
jest.mock('react-apexcharts', () => {
    return function MockApexCharts({ options, series, type, height }) {
        return (
            <div data-testid="apex-chart">
                <div>Chart Type: {type}</div>
                <div>Height: {height}</div>
                <div>Series Count: {series?.length || 0}</div>
                <div>Data Points: {series?.[0]?.data?.length || 0}</div>
            </div>
        );
    };
});

const mockProps = {
    routeId: '1S',
    headsign: 'Main Line to Downtown',
    startDate: new Date('2025-05-14T15:00:00.000Z'),
    endDate: new Date('2025-05-14T19:00:00.000Z'),
    routeName: 'Route 1S',
    onLoadComplete: jest.fn()
};

const mockApiResponse = {
    success: true,
    data: {
        headsign: 'Main Line to Downtown',
        routeId: '1S',
        trips: [
            {
                signalTraversalCount: 5,
                tripId: '255292:1130543',
                vehicleId: '3140100871',
                tripStartTimestamp: '2025-05-14T18:21:20.000+00:00',
                totalDelay: 30.0,
                averageDelay: 6.0
            },
            {
                signalTraversalCount: 5,
                tripId: '255240:1128369',
                vehicleId: '3140100871',
                tripStartTimestamp: '2025-05-14T16:55:14.000+00:00',
                totalDelay: 70.0,
                averageDelay: 14.0
            }
        ]
    }
};

describe('AverageRouteSignalDelayReport', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    test('renders loading state initially', () => {
        ApiService.getAverageRouteSignalDelay.mockImplementation(() => new Promise(() => {}));
        
        render(<AverageRouteSignalDelayReport {...mockProps} />);
        
        expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    test('renders chart with data successfully', async () => {
        ApiService.getAverageRouteSignalDelay.mockResolvedValue(mockApiResponse);
        
        render(<AverageRouteSignalDelayReport {...mockProps} />);
        
        await waitFor(() => {
            expect(screen.getByText('Average Route Signal Delay')).toBeInTheDocument();
        });

        expect(screen.getByText('Route 1S - Main Line to Downtown')).toBeInTheDocument();
        expect(screen.getByTestId('apex-chart')).toBeInTheDocument();
        expect(screen.getByText('Chart Type: line')).toBeInTheDocument();
        expect(screen.getByText('Data Points: 2')).toBeInTheDocument();
        
        // Check summary statistics
        expect(screen.getByText('Total Trips: 2')).toBeInTheDocument();
        expect(screen.getByText('Summary Statistics')).toBeInTheDocument();
    });

    test('renders error state when API fails', async () => {
        const errorResponse = {
            success: false,
            errorMessage: 'Failed to fetch data'
        };
        
        ApiService.getAverageRouteSignalDelay.mockResolvedValue(errorResponse);
        
        render(<AverageRouteSignalDelayReport {...mockProps} />);
        
        await waitFor(() => {
            expect(screen.getByText('Failed to fetch data')).toBeInTheDocument();
        });
    });

    test('renders no data message when trips array is empty', async () => {
        const noDataResponse = {
            success: true,
            data: {
                headsign: 'Main Line to Downtown',
                routeId: '1S',
                trips: []
            }
        };
        
        ApiService.getAverageRouteSignalDelay.mockResolvedValue(noDataResponse);
        
        render(<AverageRouteSignalDelayReport {...mockProps} />);
        
        await waitFor(() => {
            expect(screen.getByText('No data available for the selected time range')).toBeInTheDocument();
        });
    });

    test('calls API with correct parameters', async () => {
        ApiService.getAverageRouteSignalDelay.mockResolvedValue(mockApiResponse);
        
        render(<AverageRouteSignalDelayReport {...mockProps} />);
        
        await waitFor(() => {
            expect(ApiService.getAverageRouteSignalDelay).toHaveBeenCalledWith(
                '1S',
                'Main Line to Downtown',
                expect.stringContaining('2025-05-14T15:00:00.000'),
                expect.stringContaining('2025-05-14T19:00:00.000')
            );
        });
    });

    test('calls onLoadComplete when data loads successfully', async () => {
        ApiService.getAverageRouteSignalDelay.mockResolvedValue(mockApiResponse);
        
        render(<AverageRouteSignalDelayReport {...mockProps} />);
        
        await waitFor(() => {
            expect(mockProps.onLoadComplete).toHaveBeenCalled();
        });
    });
});

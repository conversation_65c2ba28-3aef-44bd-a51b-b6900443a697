// ApiService.js
import { getAuthToken, getSessionUser } from 'utils/auth';
import ReconnectingWebSocket from 'reconnecting-websocket';
import SockJS from 'sockjs-client';
import { Stomp } from '@stomp/stompjs';
import { Client } from '@stomp/stompjs';
import axios from 'axios';

let stompClient = null;

const API_ENDPOINT = process.env.REACT_APP_API_ENDPOINT;
const API_WS_ENDPOINT = process.env.REACT_APP_API_WS_ENDPOINT;

let vehicleUpdatesWebSocket = null; // WebSocket object to manage the connection

const ApiService = {
    getRealTimeVehicleData: (callback) => {
        if (!stompClient) {
            const socket = new SockJS(API_WS_ENDPOINT);
            stompClient = Stomp.over(socket);

            // Disable debug logging properly
            stompClient.debug = () => {}; // Use empty function instead of null

            stompClient.connect(
                {},
                function (frame) {
                    stompClient.subscribe('/topic/vehicleUpdate', function (vehicleUpdate) {
                        var vehicles = JSON.parse(vehicleUpdate.body);
                        //console.log(data);
                        // Handle the vehicle data here (update UI, store data, etc.)
                        callback(vehicles);
                    });
                },
                function (error) {
                    console.log('WebSocket connection error: ' + error);
                }
            );
        }
    },

    closeRealTimeVehicleDataConnection: () => {
        if (stompClient) {
            stompClient.disconnect();
            stompClient = null;
        }
    },

    getZones: async () => {
        const apiUrl = `${process.env.REACT_APP_API_ENDPOINT}/api/zones/getAll`;
        const accessToken = getAuthToken();

        if (!accessToken) {
            console.error('No access token available.');
            return { success: false, errorMessage: 'Authentication required' };
        }

        try {
            const response = await axios.get(apiUrl, {
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    Authorization: `Bearer ${accessToken}`
                }
            });

            if (response.status === 200) {
                return { success: true, zones: response.data };
            } else {
                return { success: false, errorMessage: 'Failed to fetch zones' };
            }
        } catch (error) {
            console.error('Error fetching the zones data:', error);
            return { success: false, errorMessage: error.message || 'Error fetching data' };
        }
    },

    saveZone: async (zone) => {
        const apiUrl = `${API_ENDPOINT}/api/zones/save`;
        const accessToken = getAuthToken();

        if (!accessToken) {
            console.error('No access token available.');
            return { success: false, errorMessage: 'Authentication required' };
        }

        try {
            const response = await axios.post(apiUrl, zone, {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${accessToken}`
                }
            });

            if (response.status === 200) {
                return { success: true, message: response.data };
            } else {
                return { success: false, errorMessage: 'Failed to save or update zone' };
            }
        } catch (error) {
            console.error('Error saving or updating zone:', error);
            return { success: false, errorMessage: error.message || 'Error occurred while saving zone' };
        }
    },
    getAllZoneSignalsForZone: async (zoneId) => {
        const apiUrl = `${API_ENDPOINT}/api/zones/${zoneId}/zone-signals`;
        const accessToken = getAuthToken();

        if (!accessToken) {
            console.error('No access token available.');
            return { success: false, errorMessage: 'Authentication required' };
        }

        try {
            const response = await axios.get(apiUrl, {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${accessToken}`
                }
            });

            if (response.status === 200) {
                return { success: true, zoneSignals: response.data };
            } else {
                return { success: false, errorMessage: 'Failed to fetch zone signals' };
            }
        } catch (error) {
            console.error('Error fetching zone signals:', error);
            return { success: false, errorMessage: error.message || 'Error occurred while fetching zone signals' };
        }
    },

    saveZoneSignal: async (zoneSignal) => {
        const apiUrl = `${API_ENDPOINT}/api/zones/zone-signal/save`;
        const accessToken = getAuthToken();

        if (!accessToken) {
            console.error('No access token available.');
            return { success: false, errorMessage: 'Authentication required' };
        }

        try {
            const response = await axios.post(apiUrl, zoneSignal, {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${accessToken}`
                }
            });

            if (response.status === 200) {
                return { success: true, message: response.data };
            } else {
                return { success: false, errorMessage: 'Failed to save or update zone signal' };
            }
        } catch (error) {
            console.error('Error saving or updating zone signal:', error);
            return { success: false, errorMessage: error.message || 'Error occurred while saving zone signal' };
        }
    },

    deleteZone: async (id) => {
        const apiUrl = `${API_ENDPOINT}/api/zones/delete/${id}`;
        const accessToken = getAuthToken();

        if (!accessToken) {
            console.error('No access token available.');
            return { success: false, errorMessage: 'Authentication required' };
        }

        try {
            const response = await axios.delete(apiUrl, {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${accessToken}`
                }
            });

            if (response.status === 200) {
                return { success: true, message: response.data };
            } else {
                return { success: false, errorMessage: 'Failed to delete zone' };
            }
        } catch (error) {
            console.error('Error deleting zone:', error);
            return { success: false, errorMessage: error.message || 'Error occurred while deleting zone' };
        }
    },

    getZonesWithTripHeadsigns: async () => {
        const apiUrl = `${process.env.REACT_APP_API_ENDPOINT}/api/zones/getAllZoneSignals`;
        const accessToken = getAuthToken();

        if (!accessToken) {
            console.error('No access token available.');
            return { success: false, errorMessage: 'Authentication required' };
        }

        try {
            const response = await axios.get(apiUrl, {
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    Authorization: `Bearer ${accessToken}`
                }
            });

            if (response.status === 200) {
                return { success: true, zones: response.data };
            } else {
                return { success: false, errorMessage: 'Failed to fetch zones with trip headsigns' };
            }
        } catch (error) {
            console.error('Error fetching the zones data:', error);
            return { success: false, errorMessage: error.message || 'Error fetching data' };
        }
    },

    getTrafficSignals: async () => {
        const apiUrl = `${process.env.REACT_APP_API_ENDPOINT}/signals`;
        const accessToken = getAuthToken();

        if (!accessToken) {
            console.error('No access token available.');
            return { success: false, errorMessage: 'Authentication required' };
        }

        try {
            const response = await axios.get(apiUrl, {
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    Authorization: `Bearer ${accessToken}`
                }
            });

            if (response.status === 200) {
                // Assuming the response data structure has a 'devices' key
                return { success: true, devices: response.data['devices'] };
            } else {
                return { success: false, errorMessage: 'Failed to fetch data' };
            }
        } catch (error) {
            console.error('Error fetching the traffic signals data:', error);
            return { success: false, errorMessage: error.message || 'Error fetching data' };
        }
    },

    getGtfsRoutes: async () => {
        const apiUrl = `${process.env.REACT_APP_API_ENDPOINT}/api/gtfs/routes/getall`;
        const accessToken = getAuthToken();

        if (!accessToken) {
            console.error('No access token available.');
            return { success: false, errorMessage: 'Authentication required' };
        }

        try {
            const response = await axios.get(apiUrl, {
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    Authorization: `Bearer ${accessToken}`
                }
            });

            if (response.status === 200) {
                return { success: true, routes: response.data };
            } else {
                return { success: false, errorMessage: 'Failed to fetch GTFS routes' };
            }
        } catch (error) {
            console.error('Error fetching the GTFS routes data:', error);
            return { success: false, errorMessage: error.message || 'Error fetching data' };
        }
    },

    getGtfsTrips: async () => {
        const apiUrl = `${process.env.REACT_APP_API_ENDPOINT}/api/gtfs/trips/getall`;
        const accessToken = getAuthToken();

        if (!accessToken) {
            console.error('No access token available.');
            return { success: false, errorMessage: 'Authentication required' };
        }

        try {
            const response = await axios.get(apiUrl, {
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    Authorization: `Bearer ${accessToken}`
                }
            });

            if (response.status === 200) {
                return { success: true, trips: response.data };
            } else {
                return { success: false, errorMessage: 'Failed to fetch GTFS trips' };
            }
        } catch (error) {
            console.error('Error fetching the GTFS trips data:', error);
            return { success: false, errorMessage: error.message || 'Error fetching data' };
        }
    },

    getStopsByTripId: async (tripId) => {
        const apiUrl = `${process.env.REACT_APP_API_ENDPOINT}/api/gtfs/stops/getByTripId/${tripId}`;
        const accessToken = getAuthToken();

        if (!accessToken) {
            console.error('No access token available.');
            return { success: false, errorMessage: 'Authentication required' };
        }

        try {
            const response = await axios.get(apiUrl, {
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    Authorization: `Bearer ${accessToken}`
                }
            });

            if (response.status === 200) {
                return { success: true, stops: response.data };
            } else {
                return { success: false, errorMessage: 'Failed to fetch stops' };
            }
        } catch (error) {
            console.error('Error fetching the stops data:', error);
            return { success: false, errorMessage: error.message || 'Error fetching data' };
        }
    },

    getStopTimesByTripId: async (tripId) => {
        const apiUrl = `${process.env.REACT_APP_API_ENDPOINT}/api/gtfs/stoptimes/getByTripId/${tripId}`;
        const accessToken = getAuthToken();

        if (!accessToken) {
            console.error('No access token available.');
            return { success: false, errorMessage: 'Authentication required' };
        }

        try {
            const response = await axios.get(apiUrl, {
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    Authorization: `Bearer ${accessToken}`
                }
            });

            if (response.status === 200) {
                return { success: true, stopTimes: response.data };
            } else {
                return { success: false, errorMessage: 'Failed to fetch stop times' };
            }
        } catch (error) {
            console.error('Error fetching the stop times data:', error);
            return { success: false, errorMessage: error.message || 'Error fetching data' };
        }
    },

    getShapesByTripId: async (tripId) => {
        const apiUrl = `${process.env.REACT_APP_API_ENDPOINT}/api/gtfs/shapes/getByTripId/${tripId}`;
        const accessToken = getAuthToken();

        if (!accessToken) {
            console.error('No access token available.');
            return { success: false, errorMessage: 'Authentication required' };
        }

        try {
            const response = await axios.get(apiUrl, {
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    Authorization: `Bearer ${accessToken}`
                }
            });

            if (response.status === 200) {
                return { success: true, shapes: response.data };
            } else {
                return { success: false, errorMessage: 'Failed to fetch shapes' };
            }
        } catch (error) {
            console.error('Error fetching the shapes data:', error);
            return { success: false, errorMessage: error.message || 'Error fetching data' };
        }
    },

    getAvgSpeedAndTravelTimeForRouteBetweenTwoDates: async (routeId, startTime, endTime) => {
        const apiUrl = `${
            process.env.REACT_APP_API_ENDPOINT
        }/api/analysis/getAvgSpeedAndTravelTimeForRouteBetweenTwoDates?routeId=${routeId}&startTime=${encodeURIComponent(
            startTime
        )}&endTime=${encodeURIComponent(endTime)}`;
        const accessToken = getAuthToken();

        if (!accessToken) {
            console.error('No access token available.');
            return { success: false, errorMessage: 'Authentication required' };
        }

        try {
            const response = await axios.get(apiUrl, {
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    Authorization: `Bearer ${accessToken}`
                }
            });

            if (response.status === 200) {
                return { success: true, data: response.data };
            } else {
                return { success: false, errorMessage: 'Failed to fetch average speed and travel time' };
            }
        } catch (error) {
            console.error('Error fetching the average speed and travel time data:', error);
            return { success: false, errorMessage: error.message || 'Error fetching data' };
        }
    },

    getTravelTimesByHeadsign: async (routeId, headsign, startTime, endTime) => {
        // Ensure timestamps end with 'Z' for UTC time
        const formattedStartTime = startTime.endsWith('Z') ? startTime : `${startTime}Z`;
        const formattedEndTime = endTime.endsWith('Z') ? endTime : `${endTime}Z`;

        const apiUrl = `${
            process.env.REACT_APP_API_ENDPOINT
        }/api/analysis/getTravelTimesByRouteIdAndHeadsign?routeId=${routeId}&headsign=${encodeURIComponent(
            headsign
        )}&startTime=${encodeURIComponent(formattedStartTime)}&endTime=${encodeURIComponent(formattedEndTime)}`;
        const accessToken = getAuthToken();

        if (!accessToken) {
            console.error('No access token available.');
            return { success: false, errorMessage: 'Authentication required' };
        }

        try {
            const response = await axios.get(apiUrl, {
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    Authorization: `Bearer ${accessToken}`
                }
            });

            if (response.status === 200) {
                return { success: true, data: response.data };
            } else {
                return { success: false, errorMessage: 'Failed to fetch travel times by headsign' };
            }
        } catch (error) {
            console.error('Error fetching the travel times by headsign:', error);
            return { success: false, errorMessage: error.message || 'Error fetching data' };
        }
    },

    getTransitRoutesWithHeadsigns: async () => {
        const apiUrl = `${process.env.REACT_APP_API_ENDPOINT}/api/routes/getalltransitRouteWithHeadsigns`;
        const accessToken = getAuthToken();

        if (!accessToken) {
            console.error('No access token available.');
            return { success: false, errorMessage: 'Authentication required' };
        }

        try {
            const response = await axios.get(apiUrl, {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${accessToken}`
                }
            });

            if (response.status === 200) {
                return { success: true, routes: response.data };
            } else {
                return { success: false, errorMessage: 'Failed to fetch transit routes with headsigns' };
            }
        } catch (error) {
            console.error('Error fetching transit routes with headsigns:', error);
            return { success: false, errorMessage: error.message || 'Error fetching data' };
        }
    },

    getTripDetailsByRouteIdAndHeadsign: async (routeId, headsign, startTime, endTime) => {
        // Ensure timestamps end with 'Z' for UTC time
        const formattedStartTime = startTime.endsWith('Z') ? startTime : `${startTime}Z`;
        const formattedEndTime = endTime.endsWith('Z') ? endTime : `${endTime}Z`;

        const apiUrl = `${
            process.env.REACT_APP_API_ENDPOINT
        }/api/analysis/getTripDetailsByRouteIdAndHeadsign?routeId=${routeId}&headsign=${encodeURIComponent(
            headsign
        )}&startTime=${encodeURIComponent(formattedStartTime)}&endTime=${encodeURIComponent(formattedEndTime)}`;
        const accessToken = getAuthToken();

        if (!accessToken) {
            console.error('No access token available.');
            return { success: false, errorMessage: 'Authentication required' };
        }

        try {
            const response = await axios.get(apiUrl, {
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    Authorization: `Bearer ${accessToken}`
                }
            });

            if (response.status === 200) {
                return { success: true, data: response.data };
            } else {
                return { success: false, errorMessage: 'Failed to fetch trip details by headsign' };
            }
        } catch (error) {
            console.error('Error fetching trip details by headsign:', error);
            return { success: false, errorMessage: error.message || 'Error fetching data' };
        }
    },
    getAvgLatenessByRouteIdAndHeadsign: async (routeId, headsign, startTime, endTime) => {
        const apiUrl = `${
            process.env.REACT_APP_API_ENDPOINT
        }/api/analysis/getAvgLatenessByRouteIdAndHeadsign?routeId=${routeId}&headsign=${encodeURIComponent(
            headsign
        )}&startTime=${encodeURIComponent(startTime)}&endTime=${encodeURIComponent(endTime)}`;
        const accessToken = getAuthToken();

        if (!accessToken) {
            console.error('No access token available.');
            return { success: false, errorMessage: 'Authentication required' };
        }

        try {
            const response = await fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${accessToken}`
                }
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('API error:', errorText);
                return { success: false, errorMessage: errorText || 'Failed to fetch data' };
            }

            const data = await response.json();
            return { success: true, data };
        } catch (error) {
            console.error('Error in API call:', error);
            return { success: false, errorMessage: error.message };
        }
    },
    getSignalTspEvents: async (deviceId, startTime, endTime) => {
        // Ensure timestamps end with 'Z' for UTC time
        const formattedStartTime = startTime.endsWith('Z') ? startTime : `${startTime}Z`;
        const formattedEndTime = endTime.endsWith('Z') ? endTime : `${endTime}Z`;

        const apiUrl = `${process.env.REACT_APP_API_ENDPOINT}/signals/device/${deviceId}/events?startTime=${encodeURIComponent(
            formattedStartTime
        )}&endTime=${encodeURIComponent(formattedEndTime)}`;
        const accessToken = getAuthToken();

        if (!accessToken) {
            console.error('No access token available.');
            return { success: false, errorMessage: 'Authentication required' };
        }

        try {
            const response = await axios.get(apiUrl, {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${accessToken}`
                }
            });

            if (response.status === 200) {
                return { success: true, events: response.data };
            } else {
                return { success: false, errorMessage: 'Failed to fetch TSP events' };
            }
        } catch (error) {
            console.error('Error fetching TSP events:', error);
            return { success: false, errorMessage: error.message || 'Error occurred while fetching TSP events' };
        }
    },

    getAverageRouteSignalDelay: async (routeId, headsign, startTime, endTime) => {
        // Ensure timestamps end with 'Z' for UTC time
        const formattedStartTime = startTime.endsWith('Z') ? startTime : `${startTime}Z`;
        const formattedEndTime = endTime.endsWith('Z') ? endTime : `${endTime}Z`;

        const apiUrl = `${
            process.env.REACT_APP_API_ENDPOINT
        }/api/analysis/getAvgDelayPerVehicleTrip?routeId=${routeId}&headsign=${encodeURIComponent(headsign)}&startTime=${encodeURIComponent(
            formattedStartTime
        )}&endTime=${encodeURIComponent(formattedEndTime)}`;
        const accessToken = getAuthToken();

        if (!accessToken) {
            console.error('No access token available.');
            return { success: false, errorMessage: 'Authentication required' };
        }

        try {
            const response = await axios.get(apiUrl, {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${accessToken}`
                }
            });

            if (response.status === 200) {
                return { success: true, data: response.data };
            } else {
                return { success: false, errorMessage: 'Failed to fetch average route signal delay data' };
            }
        } catch (error) {
            console.error('Error fetching average route signal delay:', error);
            return { success: false, errorMessage: error.message || 'Error occurred while fetching signal delay data' };
        }
    }
    // You can add other API calls here following the same pattern
};

export default ApiService;

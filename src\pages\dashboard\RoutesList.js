import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import MUIDataTable from 'mui-datatables';
import { Box } from '@mui/material';

const RoutesList = ({ routes, onRouteSelectionChange, initiallyLoaded, setInitiallyLoaded, selectedIndices = [] }) => {
    const [tableBodyMaxHeight, setTableBodyMaxHeight] = useState('100%');
    const [selectedRows, setSelectedRows] = useState(selectedIndices);

    // Update selectedRows when selectedIndices changes
    useEffect(() => {
        setSelectedRows(selectedIndices);
    }, [selectedIndices]);

    const handleSelectionChange = (currentRowsSelected, allRowsSelected) => {
        // Update local state for selected rows
        const newSelectedIndices = allRowsSelected.map((row) => row.dataIndex);
        setSelectedRows(newSelectedIndices);

        // Extract all selected row data
        const selected = allRowsSelected?.map((row) => routes[row.dataIndex]).filter((row) => row);

        // Pass both selected items and indices to parent
        onRouteSelectionChange(selected, newSelectedIndices);
        console.log('Selected Routes from Table:', selected);
    };

    const getTableMaxHeight = () => {
        const screenWidth = window.innerWidth;
        if (screenWidth > 1920) {
            return '86vh'; // For smaller screens
        } else if (screenWidth <= 1920 && screenWidth > 1024) {
            return '84.5vh'; // For larger screens
        } else {
            return '70vh'; // For larger screens
        }
    };

    useEffect(() => {
        const handleResize = () => {
            setTableBodyMaxHeight(getTableMaxHeight());
        };
        window.addEventListener('resize', handleResize);
        // Cleanup the event listener
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    // Initial load effect
    useEffect(() => {
        if (!initiallyLoaded && routes.length > 0) {
            // Create an array of indices from 0 to routes.length-1
            const allIndices = Array.from({ length: routes.length }, (_, i) => i);
            setSelectedRows(allIndices);

            // Notify parent component about the selection
            const selected = routes.map((route) => route);
            onRouteSelectionChange(selected, allIndices);

            // Update the parent's state to indicate routes have been initially loaded
            setInitiallyLoaded(true);
        }
    }, [routes, initiallyLoaded, onRouteSelectionChange, setInitiallyLoaded]);

    const cellPadding = '4px';
    const columns = [
        //{ name: 'routeId', label: 'ID' },
        {
            name: 'routeShortName',
            label: 'Name',
            options: {
                setCellProps: () => ({
                    style: {
                        padding: cellPadding
                    }
                })
            }
        },
        {
            name: 'tripHeadsign',
            label: 'Direction',
            options: {
                setCellProps: () => ({
                    style: {
                        padding: cellPadding
                    }
                })
            }
        }
        //,
        // {
        //     name: 'agencyName',
        //     label: 'Agency',
        //     options: {
        //         setCellProps: () => ({
        //             style: {
        //                 padding: '4px 0px 4px 20px' // 4px top, 0px right, 4px bottom, 10px left
        //             }
        //         })
        //     }
        // }
    ];

    const options = {
        filter: true,
        search: true,
        download: false,
        print: false,
        viewColumns: false,
        responsive: 'standard',
        fixedHeader: true,
        pagination: false,
        tableBodyMaxHeight: tableBodyMaxHeight,
        scrollMaxHeight: 'auto',
        scroll: 'auto',
        tableBodyHeight: getTableMaxHeight(),

        selectableRows: 'multiple',
        selectableRowsOnClick: true,
        rowsSelected: selectedRows,
        onRowSelectionChange: handleSelectionChange,
        setTableProps: () => ({ size: 'small' }),
        selectToolbarPlacement: 'none'
    };

    return (
        <Box>
            <MUIDataTable title="Transit Routes" data={routes} columns={columns} options={options} />
        </Box>
    );
};

RoutesList.propTypes = {
    routes: PropTypes.array.isRequired,
    onRouteSelectionChange: PropTypes.func.isRequired,
    initiallyLoaded: PropTypes.bool.isRequired,
    setInitiallyLoaded: PropTypes.func.isRequired,
    selectedIndices: PropTypes.array
};

export default RoutesList;

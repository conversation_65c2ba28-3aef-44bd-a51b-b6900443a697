import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { createRoot } from 'react-dom/client';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { Card, CardContent, Typography, FormControlLabel, Checkbox, Select, MenuItem, Box } from '@mui/material';
import DirectionsBusIcon from '@mui/icons-material/DirectionsBus';
import TrafficLightIcon from '@mui/icons-material/Traffic';
import styles from './MapBoxComponent.module.css';
import { wktToGeoJSON } from '@terraformer/wkt';
import PropTypes from 'prop-types';

// Add map style constants
const MAP_STYLES = {
    DARK: 'mapbox://styles/mapbox/navigation-night-v1',
    SATELLITE: 'mapbox://styles/mapbox/satellite-v9',
    STREETS: 'mapbox://styles/mapbox/streets-v12',
    LIGHT: 'mapbox://styles/mapbox/light-v11',
    OUTDOORS: 'mapbox://styles/mapbox/outdoors-v12',
    SATELLITE_STREETS: 'mapbox://styles/mapbox/satellite-streets-v12'
};

import MapboxDraw from '@mapbox/mapbox-gl-draw';
import 'mapbox-gl/dist/mapbox-gl.css';
import '@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css';

mapboxgl.accessToken = process.env.REACT_APP_MAPBOX_ACCESS_TOKEN;

function MapBoxComponent({
    center,
    zoom,
    isSignalListExpanded,
    vehicles,
    signals,
    selectedSignalIds, // Add this prop
    stops,
    stopTimes,
    shapes,
    dataLoading,
    gtfsRoutes,
    gtfsTrips,
    selectedZone,
    selectedZones,
    zones,
    polygonGeometry,
    onGeometryChange,
    editable
}) {
    const mapContainerRef = useRef(null);
    const [map, setMap] = useState(null);
    const draw = useRef(null);
    const vehicleMarkersRef = useRef(new Map());
    const signalMarkersRef = useRef(new Map());
    const stopMarkersRef = useRef(new Map());
    const shapeLayerRef = useRef(null);
    const popupsRef = useRef([]);
    const zoneLayerRef = useRef(null);
    const zoneLayerRefs = useRef(new Map()); // Ref to store active zone layers
    const [showVehicles, setShowVehicles] = useState(true);
    const [showSignals, setShowSignals] = useState(true);
    const [showZones, setShowZones] = useState(true);
    const [mapStyle, setMapStyle] = useState(MAP_STYLES.DARK); // Default to dark map

    const handleMapStyleChange = (event) => {
        const newStyle = event.target.value;

        // Store the current map state before changing styles
        if (map) {
            map.setStyle(newStyle);

            // Re-add markers and layers after style change is complete
            map.once('style.load', () => {
                // Force re-render of vehicles and signals by clearing refs
                vehicleMarkersRef.current.forEach((marker) => marker.remove());
                vehicleMarkersRef.current.clear();

                signalMarkersRef.current.forEach((marker) => marker.remove());
                signalMarkersRef.current.clear();

                stopMarkersRef.current.forEach((marker) => marker.remove());
                stopMarkersRef.current.clear();

                // Reset shape layer ref to force re-render
                if (shapeLayerRef.current) {
                    shapeLayerRef.current = false;
                }

                // Reset zone layers to force re-render
                zoneLayerRefs.current.clear();

                // Force re-render by toggling state variables
                if (showSignals) {
                    setShowSignals(false);
                    setTimeout(() => setShowSignals(true), 50);
                }

                if (showVehicles) {
                    setShowVehicles(false);
                    setTimeout(() => setShowVehicles(true), 50);
                }

                if (showZones) {
                    setShowZones(false);
                    setTimeout(() => setShowZones(true), 50);
                }
            });
        }

        setMapStyle(newStyle);
    };

    const getColorByVehicleLateness = (lateness) => {
        if (lateness < 60) {
            return 'green';
        } else if (lateness >= 60 && lateness < 180) {
            return 'yellow';
        } else if (lateness >= 180 && lateness < 300) {
            return 'orange';
        } else {
            return 'red';
        }
    };

    const formatTimestamp = (timestamp) => {
        const date = new Date(timestamp);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };

    const memoizedVehicles = useMemo(() => {
        return vehicles.map((vehicle) => ({
            ...vehicle,
            color: getColorByVehicleLateness(vehicle.lateness),
            route: gtfsRoutes.find((route) => route.routeId === vehicle.routeId),
            trip: gtfsTrips.find((trip) => trip.tripId === vehicle.tripId),
            formattedTimestamp: formatTimestamp(vehicle.timestamp)
        }));
    }, [vehicles, gtfsRoutes, gtfsTrips]);

    useEffect(() => {
        // Initialize Mapbox map only once
        const mapInstance = new mapboxgl.Map({
            container: mapContainerRef.current,
            style: mapStyle,
            center: [-81.007666, 46.486551], // Initial center
            zoom: 11
        });

        mapInstance.addControl(new mapboxgl.NavigationControl(), 'top-right');
        mapInstance.on('load', () => setMap(mapInstance));

        // Add the draw control to the map for polygon editing
        if (editable) {
            draw.current = new MapboxDraw({
                displayControlsDefault: false,
                controls: {
                    polygon: true,
                    trash: true
                },
                defaultMode: 'draw_polygon'
            });

            mapInstance.addControl(draw.current);
        }

        return () => {
            if (mapInstance) mapInstance.remove();
        };
    }, []); // Remove mapStyle dependency to avoid recreating the map

    useEffect(() => {
        if (map) {
            // Add or remove the draw control based on the editable prop
            if (editable) {
                draw.current = new MapboxDraw({
                    displayControlsDefault: false,
                    controls: {
                        polygon: true,
                        trash: true
                    }
                });
                map.addControl(draw.current);
            } else if (draw.current) {
                map.removeControl(draw.current);
                draw.current = null;
            }
        }
    }, [map, editable]);

    useEffect(() => {
        if (map && editable) {
            // Clear existing features from Draw
            draw.current?.deleteAll();

            if (polygonGeometry) {
                // If we have a valid polygonGeometry, add it
                const feature = {
                    type: 'Feature',
                    geometry: polygonGeometry
                };
                draw.current.add(feature);
            }
            // If polygonGeometry is null, we do nothing special
            // The Draw control will still be ready for the user
            // to draw a new polygon if needed.
        }
    }, [map, editable, polygonGeometry]);

    useEffect(() => {
        if (map && draw.current) {
            // Event listener to capture changes in the polygon geometry
            const updateHandler = (e) => {
                const updatedGeometry = e.features[0].geometry;
                if (onGeometryChange) {
                    onGeometryChange(updatedGeometry);
                }
            };

            map.on('draw.update', updateHandler);
            map.on('draw.create', updateHandler);

            map.on('draw.delete', () => {
                if (onGeometryChange) {
                    onGeometryChange(null); // Clear the geometry when deleted
                }
            });

            return () => {
                map.off('draw.update', updateHandler);
                map.off('draw.create', updateHandler);
            };
        }
    }, [map, onGeometryChange]);

    const getColorByVehicleId = useCallback((vehicleId) => {
        const hash = vehicleId.split('').reduce((acc, char) => char.charCodeAt(0) + ((acc << 5) - acc), 0);
        return `hsl(${hash % 360}, 100%, 75%)`;
    }, []);

    useEffect(() => {
        if (map) {
            if (center && !isNaN(center.latitude) && !isNaN(center.longitude) && !isNaN(zoom)) {
                map.flyTo({ center: [center.longitude, center.latitude], zoom: zoom });
            } else {
                console.error('Invalid center or zoom prop:', center, zoom);
            }
        }
    }, [center, zoom]);

    useEffect(() => {
        if (map) {
            map.resize();
        }
    }, [isSignalListExpanded, map]);

    // Revert to the original vehicle update logic
    useEffect(() => {
        if (map && showVehicles) {
            vehicles.forEach((vehicle) => {
                let marker = vehicleMarkersRef.current.get(vehicle.vehicleId);
                const lateness = vehicle.lateness;
                const color = getColorByVehicleLateness(lateness);

                const route = gtfsRoutes.find((route) => route.routeId === vehicle.routeId);
                const routeLongName = route ? route.routeLongName : 'Unknown Route';

                const trip = gtfsTrips.find((trip) => trip.tripId === vehicle.tripId);
                const tripHeadsign = trip ? trip.tripHeadsign : 'Unknown Trip Headsign';

                const formattedTimestamp = formatTimestamp(vehicle.timestamp);

                if (marker) {
                    marker.setLngLat([vehicle.longitude, vehicle.latitude]);
                    const popupContent = `
                        <div style="background-color: white; color: black; padding: 10px; border-radius: 5px; width: fit-content ">
                            <h2>${vehicle.routeId} - ${tripHeadsign}</h2>
                            <p style="margin: 0;">Vehicle ID: ${vehicle.vehicleId}</p>
                            <p style="margin: 0;">Timestamp: ${formattedTimestamp}</p>
                            <p style="margin: 0;">Lat: ${vehicle.latitude}</p>
                            <p style="margin: 0;">Long: ${vehicle.longitude}</p>
                            <p style="margin: 0;">Heading: ${vehicle.bearing}</p>
                            <p style="margin: 0;">Speed: ${vehicle.speed}</p>
                            <p style="margin: 0;">Lateness: ${lateness} sec</p>
                        </div>
                    `;
                    marker.getPopup().setHTML(popupContent);
                    if (marker.getElement() && marker.getElement().firstChild) {
                        marker.getElement().firstChild.style.color = color;
                    }
                } else {
                    const el = document.createElement('div');
                    const root = createRoot(el);
                    root.render(<DirectionsBusIcon style={{ fontSize: '30px', color: color }} />);
                    el.className = styles.marker;

                    const popup = new mapboxgl.Popup({ offset: 25 })
                        .setLngLat([vehicle.longitude, vehicle.latitude])
                        .setHTML(
                            `<div style="background-color: white; color: black; padding: 10px; border-radius: 5px; width: fit-content">
                            <h2>${vehicle.routeId} - ${tripHeadsign}</h2>
                                <p style="margin: 0;">Vehicle ID: ${vehicle.vehicleId}</p>
                                <p style="margin: 0;">Timestamp: ${formattedTimestamp}</p>
                                <p style="margin: 0;">Lat: ${vehicle.latitude}</p>
                                <p style="margin: 0;">Long: ${vehicle.longitude}</p>
                                <p style="margin: 0;">Heading: ${vehicle.bearing}</p>
                                <p style="margin: 0;">Speed: ${vehicle.speed}</p>
                            <p style="margin: 0;">Lateness: ${lateness} sec</p>
                            </div>`
                        )
                        .addTo(map);

                    marker = new mapboxgl.Marker(el).setLngLat([vehicle.longitude, vehicle.latitude]).setPopup(popup).addTo(map);
                    vehicleMarkersRef.current.set(vehicle.vehicleId, marker);
                }
            });

            const currentVehicleIds = new Set(vehicles.map((vehicle) => vehicle.vehicleId));
            vehicleMarkersRef.current.forEach((marker, id) => {
                if (!currentVehicleIds.has(id)) {
                    marker.remove();
                    vehicleMarkersRef.current.delete(id);
                }
            });
        } else {
            vehicleMarkersRef.current.forEach((marker) => {
                marker.remove();
            });
            vehicleMarkersRef.current.clear();
        }
    }, [map, vehicles, getColorByVehicleLateness, gtfsRoutes, gtfsTrips, showVehicles]);

    useEffect(() => {
        if (map) {
            // Clear all existing signal markers first
            signalMarkersRef.current.forEach((marker) => {
                marker.remove();
            });
            signalMarkersRef.current.clear();

            // Only proceed to add signals if showSignals is true
            if (showSignals) {
                // Determine which signals to show based on selectedSignalIds
                let signalsToShow = [];

                if (selectedSignalIds !== null && selectedSignalIds !== undefined) {
                    // If selectedSignalIds is an empty array, show no signals
                    // If it has items, show only those signals
                    signalsToShow = selectedSignalIds.length > 0 ? signals.filter((signal) => selectedSignalIds.includes(signal.id)) : [];
                } else {
                    // If selectedSignalIds is null/undefined, show all signals
                    signalsToShow = signals;
                }

                // Add markers for signals that should be shown
                signalsToShow.forEach((signal) => {
                    const el = document.createElement('div');
                    el.className = styles.signalMarker;

                    const popup = new mapboxgl.Popup({ offset: 5 }).setLngLat([signal.longitude, signal.latitude]).setHTML(
                        `
                        <div class="${styles.popup}">
                            <h2>${signal.name}</h2>
                            <p>Primary Road: ${signal.primaryRoad}</p>
                            <p>Cross Road: ${signal.crossRoad}</p>
                        </div>
                        `
                    );

                    const marker = new mapboxgl.Marker(el).setLngLat([signal.longitude, signal.latitude]).setPopup(popup).addTo(map);
                    signalMarkersRef.current.set(signal.id, marker);
                });
            }
        }
    }, [map, signals, showSignals, selectedSignalIds]);

    useEffect(() => {
        if (map && !dataLoading) {
            (stops || []).forEach((stop) => {
                let marker = stopMarkersRef.current.get(stop.stopId);
                if (!marker) {
                    const el = document.createElement('div');
                    el.className = styles.stopMarker;

                    const stopTime = stopTimes.find((st) => st.stopId === stop.stopId) || {};
                    const popup = new mapboxgl.Popup({ offset: 25 })
                        .setLngLat([stop.stopLon, stop.stopLat])
                        .setHTML(
                            `<div class="${styles.popup}">
                                <h2>${stop.stopName}</h2>
                                <p>Stop ID: ${stop.stopId}</p>
                                <p>Arrival Time: ${stopTime.arrivalTime || 'N/A'}</p>
                                <p>Lat: ${stop.stopLat}</p>
                                <p>Long: ${stop.stopLon}</p>
                                <p>Sequence: ${stopTime.stopSequence || 'N/A'}</p>
                            </div>`
                        )
                        .addTo(map);

                    marker = new mapboxgl.Marker(el).setLngLat([stop.stopLon, stop.stopLat]).setPopup(popup).addTo(map);
                    stopMarkersRef.current.set(stop.stopId, marker);
                }
            });

            const currentStopIds = new Set(stops.map((stop) => stop.stopId));
            stopMarkersRef.current.forEach((marker, id) => {
                if (!currentStopIds.has(id)) {
                    marker.remove();
                    stopMarkersRef.current.delete(id);
                }
            });
        }
    }, [map, stops, stopTimes, dataLoading]);

    useEffect(() => {
        if (map && shapes.length > 0 && !dataLoading) {
            if (shapeLayerRef.current) {
                // Check if layer and source exist before removing
                if (map.getLayer('shapeLayer')) {
                    map.removeLayer('shapeLayer');
                }
                if (map.getSource('shapeSource')) {
                    map.removeSource('shapeSource');
                }
            }

            try {
                const shapeCoords = shapes.map((shape) => [shape.shapePtLon, shape.shapePtLat]);

                map.addSource('shapeSource', {
                    type: 'geojson',
                    data: {
                        type: 'Feature',
                        geometry: {
                            type: 'LineString',
                            coordinates: shapeCoords
                        }
                    }
                });

                map.addLayer({
                    id: 'shapeLayer',
                    type: 'line',
                    source: 'shapeSource',
                    layout: {
                        'line-join': 'round',
                        'line-cap': 'round'
                    },
                    paint: {
                        'line-color': 'aqua',
                        'line-width': 6
                    }
                });

                shapeLayerRef.current = true;
            } catch (error) {
                console.error('Error adding shape layer:', error);
            }
        }
    }, [map, shapes, dataLoading]);

    // Display only selected zones on the map
    useEffect(() => {
        if (map && showZones && Array.isArray(selectedZones)) {
            // Remove existing zone layers
            zoneLayerRefs.current.forEach((_, zoneId) => {
                // Check if layer exists before removing
                if (map.getLayer(`zoneLayer-${zoneId}`)) {
                    map.removeLayer(`zoneLayer-${zoneId}`);
                }
                if (map.getLayer(`zoneOutline-${zoneId}`)) {
                    map.removeLayer(`zoneOutline-${zoneId}`);
                }
                // Check if source exists before removing
                if (map.getSource(`zoneSource-${zoneId}`)) {
                    map.removeSource(`zoneSource-${zoneId}`);
                }
            });
            zoneLayerRefs.current.clear();

            // Add only the selected zones
            selectedZones.forEach((zone) => {
                try {
                    const zoneGeoJSON = wktToGeoJSON(zone.geometry); // Use geometry directly from selectedZones

                    // Add zone source and layer
                    map.addSource(`zoneSource-${zone.id}`, {
                        type: 'geojson',
                        data: {
                            type: 'Feature',
                            geometry: zoneGeoJSON,
                            properties: {
                                id: zone.id,
                                name: zone.name,
                                type: zone.type === 1 ? 'TSP' : zone.type === 2 ? 'EVP' : 'Unknown'
                            }
                        }
                    });

                    map.addLayer({
                        id: `zoneLayer-${zone.id}`,
                        type: 'fill',
                        source: `zoneSource-${zone.id}`,
                        layout: {},
                        paint: {
                            'fill-color': '#0080ff',
                            'fill-opacity': 0.5
                        }
                    });

                    map.addLayer({
                        id: `zoneOutline-${zone.id}`,
                        type: 'line',
                        source: `zoneSource-${zone.id}`,
                        layout: {},
                        paint: {
                            'line-color': '#000',
                            'line-width': 3
                        }
                    });

                    // Add click event for the zone
                    map.on('click', `zoneLayer-${zone.id}`, (e) => {
                        const features = map.queryRenderedFeatures(e.point, { layers: [`zoneLayer-${zone.id}`] });

                        if (features.length > 0) {
                            const { properties } = features[0];

                            // Create popup content
                            const popupContent = `
                                <div class="${styles.popup}">
                                    <h2>${properties.name || `Zone ${properties.id}`}</h2>
                                    <p>Zone ID: ${properties.id}</p>
                                    <p>Type: ${properties.type}</p>
                                    ${zone.description ? `<p>Description: ${zone.description}</p>` : ''}
                                </div>
                            `;

                            // Create and add popup
                            const popup = new mapboxgl.Popup({
                                offset: [0, -5],
                                className: styles.mapboxglPopupContent // Add the class for consistent styling
                            })
                                .setLngLat(e.lngLat)
                                .setHTML(popupContent)
                                .addTo(map);

                            // Store the popup reference to remove it later if needed
                            popupsRef.current.push(popup);
                        }
                    });

                    // Change cursor to pointer when hovering over zone
                    map.on('mouseenter', `zoneLayer-${zone.id}`, () => {
                        map.getCanvas().style.cursor = 'pointer';
                    });

                    // Change cursor back when leaving zone
                    map.on('mouseleave', `zoneLayer-${zone.id}`, () => {
                        map.getCanvas().style.cursor = '';
                    });

                    // Keep track of added zone layers
                    zoneLayerRefs.current.set(zone.id, true);
                } catch (error) {
                    console.error('Error adding zone to map:', error);
                }
            });
        }
    }, [map, selectedZones, showZones]);

    // Remove the zone layer when showZones is false
    useEffect(() => {
        if (map && !showZones && zoneLayerRefs.current) {
            zoneLayerRefs.current.forEach((_, zoneId) => {
                // Check if layer exists before removing
                if (map.getLayer(`zoneLayer-${zoneId}`)) {
                    map.removeLayer(`zoneLayer-${zoneId}`);
                }
                if (map.getLayer(`zoneOutline-${zoneId}`)) {
                    map.removeLayer(`zoneOutline-${zoneId}`);
                }
                // Check if source exists before removing
                if (map.getSource(`zoneSource-${zoneId}`)) {
                    map.removeSource(`zoneSource-${zoneId}`);
                }
            });
            zoneLayerRefs.current.clear();
        }
    }, [map, showZones]);

    // Add a cleanup effect to remove popups when component unmounts or when zones change
    useEffect(() => {
        return () => {
            // Remove all popups
            popupsRef.current.forEach((popup) => {
                if (popup) popup.remove();
            });
            popupsRef.current = [];
        };
    }, [selectedZones]);

    return (
        <div style={{ position: 'relative', width: '100%', height: '100%' }}>
            <div ref={mapContainerRef} style={{ width: '100%', height: '100%' }}></div>
            <div className={styles.layerControl}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Select
                        value={mapStyle}
                        onChange={handleMapStyleChange}
                        size="small"
                        sx={{
                            bgcolor: 'rgba(0, 0, 0, 0.6)',
                            color: 'white',
                            width: '180px',
                            mb: 1,
                            '& .MuiSelect-icon': { color: 'white' }
                        }}
                    >
                        <MenuItem value={MAP_STYLES.DARK}>Dark Map</MenuItem>
                        <MenuItem value={MAP_STYLES.LIGHT}>Light Map</MenuItem>
                        <MenuItem value={MAP_STYLES.STREETS}>Streets</MenuItem>
                        <MenuItem value={MAP_STYLES.OUTDOORS}>Outdoors</MenuItem>
                        <MenuItem value={MAP_STYLES.SATELLITE}>Satellite</MenuItem>
                        <MenuItem value={MAP_STYLES.SATELLITE_STREETS}>Satellite Streets</MenuItem>
                    </Select>
                    <FormControlLabel
                        control={<Checkbox checked={showVehicles} onChange={(e) => setShowVehicles(e.target.checked)} color="primary" />}
                        label="Vehicles"
                    />
                    <FormControlLabel
                        control={<Checkbox checked={showSignals} onChange={(e) => setShowSignals(e.target.checked)} color="primary" />}
                        label="Traffic Signals"
                    />
                    <FormControlLabel
                        control={<Checkbox checked={showZones} onChange={(e) => setShowZones(e.target.checked)} color="primary" />}
                        label="Zones"
                    />
                </Box>
            </div>
        </div>
    );
}

MapBoxComponent.propTypes = {
    // Existing propTypes...
    selectedSignalIds: PropTypes.array
};

export default React.memo(MapBoxComponent);

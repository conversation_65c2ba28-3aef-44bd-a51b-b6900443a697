import React, { useEffect, useState } from 'react';
import ApexCharts from 'react-apexcharts';
import ApiService from '../../../../utils/ApiService';
import { Box, CircularProgress, Typography } from '@mui/material';

// Utility function to convert date to UTC format
const convertToUTC = (date) => {
    return date.toISOString();
};

// Utility function to format date for display
const formatDateDisplay = (date) => {
    return date.toLocaleString('en-US', {
        month: 'short',
        day: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const AverageRouteSignalDelayReport = ({ routeId, headsign, startDate, endDate, routeName, onLoadComplete }) => {
    const [series, setSeries] = useState([]);
    const [options, setOptions] = useState({
        chart: {
            height: 350,
            type: 'area',
            zoom: {
                enabled: true,
                type: 'xy',
                autoScaleYaxis: true
            },
            toolbar: {
                show: true,
                tools: {
                    download: true,
                    selection: true,
                    zoom: true,
                    zoomin: true,
                    zoomout: true,
                    pan: true,
                    reset: true
                },
                autoSelected: 'zoom',
                style: {
                    backgroundColor: 'transparent',
                    color: '#a9a9a9',
                    borderRadius: '6px'
                }
            },
            background: 'transparent',
            foreColor: '#a9a9a9'
        },
        theme: {
            mode: 'dark',
            palette: 'palette1'
        },
        dataLabels: {
            enabled: false
        },
        markers: {
            size: 0
        },
        stroke: {
            curve: 'smooth',
            width: 3,
            lineCap: 'round'
        },
        fill: {
            type: 'gradient',
            gradient: {
                shadeIntensity: 1,
                inverseColors: false,
                opacityFrom: 0.5,
                opacityTo: 0.1,
                stops: [0, 90, 100]
            }
        },
        grid: {
            xaxis: {
                lines: {
                    show: true,
                    color: '#555555'
                }
            },
            yaxis: {
                lines: {
                    show: true,
                    color: '#555555'
                }
            },
            borderColor: '#555555',
            strokeDashArray: 0
        },
        xaxis: {
            type: 'datetime',
            labels: {
                style: {
                    colors: '#a9a9a9'
                },
                datetimeUTC: false,
                format: 'HH:mm'
            }
        },
        yaxis: {
            title: {
                text: 'Average Delay (seconds)',
                style: {
                    color: '#a9a9a9',
                    fontSize: '14px',
                    fontWeight: 600,
                    fontFamily: "'Roboto', sans-serif"
                }
            },
            labels: {
                style: {
                    colors: '#a9a9a9',
                    fontSize: '12px',
                    fontFamily: "'Roboto', sans-serif"
                },
                formatter: function (val) {
                    return val.toFixed(0);
                }
            },
            min: 0,
            max: undefined,
            tickAmount: 6,
            forceNiceScale: true
        },
        tooltip: {
            theme: 'dark',
            shared: false,
            x: {
                format: 'MMM dd, yyyy HH:mm:ss'
            },
            custom: function ({ series, seriesIndex, dataPointIndex, w }) {
                const point = w.config.series[seriesIndex].data[dataPointIndex];
                const time = new Date(point.x).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                const averageDelay = point.y;
                const tripId = point.tripId || 'Unknown';

                return `
                    <div class="apexcharts-tooltip-box" style="padding: 8px; background: #222; color: #fff; border: 1px solid #444;">
                        <div style="font-weight: bold; margin-bottom: 5px;">${time}</div>
                        <div style="margin-bottom: 5px;">Trip: ${tripId}</div>
                        <div style="margin-top: 5px;">
                            <strong>Average Delay: ${averageDelay.toFixed(0)} seconds</strong>
                        </div>
                    </div>
                `;
            },
            y: {
                formatter: function (val) {
                    return val.toFixed(0) + ' seconds';
                }
            }
        },
        legend: {
            position: 'top',
            labels: {
                colors: '#a9a9a9'
            }
        }
    });
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        if (routeId && headsign) {
            fetchData();
        }
    }, [routeId, headsign, startDate, endDate]);

    const fetchData = async () => {
        setLoading(true);
        setError(null);

        try {
            // Convert dates to UTC format
            const formattedStartDate = convertToUTC(startDate);
            const formattedEndDate = convertToUTC(endDate);

            // Use the new API method for average route signal delay
            const result = await ApiService.getAverageRouteSignalDelay(routeId, headsign, formattedStartDate, formattedEndDate);

            if (result.success) {
                if (!result.data || !result.data.trips || result.data.trips.length === 0) {
                    setError('No data available for the selected time range');
                    setLoading(false);
                    return;
                }

                // Process the data for the line chart
                // Sort trips by timestamp first to ensure proper line connections
                const sortedTrips = result.data.trips.sort(
                    (a, b) => new Date(a.tripStartTimestamp).getTime() - new Date(b.tripStartTimestamp).getTime()
                );

                const dataSeries = [
                    {
                        name: `${routeId} - ${headsign} - Average Signal Delay`,
                        data: sortedTrips.map((trip) => ({
                            x: new Date(trip.tripStartTimestamp).getTime(),
                            y: trip.averageDelay,
                            tripId: trip.tripId,
                            vehicleId: trip.vehicleId,
                            totalDelay: trip.totalDelay,
                            signalTraversalCount: trip.signalTraversalCount
                        }))
                    }
                ];

                setSeries(dataSeries);

                if (onLoadComplete) {
                    onLoadComplete();
                }
            } else {
                setError(result.errorMessage || 'Failed to fetch data');
            }
        } catch (err) {
            console.error('Error fetching average route signal delay data:', err);
            setError('An error occurred while fetching data');
        } finally {
            setLoading(false);
        }
    };

    return (
        <Box sx={{ width: '100%', height: '100%', backgroundColor: '#424242', borderRadius: 1 }}>
            <Typography variant="h6" sx={{ textAlign: 'center', mb: 0.5 }}>
                Average Route Signal Delay
            </Typography>
            <Typography variant="subtitle2" sx={{ textAlign: 'center', mb: 0.5 }}>
                {routeName || `${routeId} - ${headsign}`}
            </Typography>
            <Typography variant="caption" sx={{ display: 'block', color: '#aaa', textAlign: 'center', mb: 1 }}>
                {formatDateDisplay(startDate)} - {formatDateDisplay(endDate)}
            </Typography>
            <Box sx={{ width: '100%', height: '410px' }}>
                {loading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <CircularProgress />
                    </Box>
                ) : error ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <Typography color="error">{error}</Typography>
                    </Box>
                ) : series.length > 0 ? (
                    <ApexCharts options={options} series={series} type="area" height="100%" width="100%" />
                ) : (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <Typography>No data available for the selected time range</Typography>
                    </Box>
                )}
            </Box>
        </Box>
    );
};

export default AverageRouteSignalDelayReport;

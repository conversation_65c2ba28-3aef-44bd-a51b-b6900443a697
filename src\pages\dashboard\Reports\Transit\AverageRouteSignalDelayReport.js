import React, { useEffect, useState } from 'react';
import ApexCharts from 'react-apexcharts';
import ApiService from '../../../../utils/ApiService';
import { Box, CircularProgress, Typography } from '@mui/material';

// Utility function to convert date to UTC format
const convertToUTC = (date) => {
    if (!date) return null;
    const utcDate = new Date(date.getTime() - date.getTimezoneOffset() * 60000);
    return utcDate.toISOString().slice(0, -1); // Remove the 'Z' at the end
};

// Utility function to format date for display
const formatDateDisplay = (date) => {
    if (!date) return '';
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

const AverageRouteSignalDelayReport = ({ routeId, headsign, startDate, endDate, routeName, onLoadComplete }) => {
    const [series, setSeries] = useState([]);
    const [chartOptions, setChartOptions] = useState({
        chart: {
            type: 'line',
            height: 400,
            background: '#424242',
            foreColor: '#ffffff',
            toolbar: {
                show: true,
                tools: {
                    download: true,
                    selection: true,
                    zoom: true,
                    zoomin: true,
                    zoomout: true,
                    pan: true,
                    reset: true
                }
            },
            zoom: {
                enabled: true,
                type: 'x',
                autoScaleYaxis: true
            }
        },
        stroke: {
            curve: 'smooth',
            width: 2
        },
        colors: ['#00E396'],
        xaxis: {
            type: 'datetime',
            labels: {
                style: {
                    colors: '#a9a9a9'
                },
                datetimeUTC: false,
                format: 'HH:mm'
            },
            title: {
                text: 'Trip Start Time',
                style: {
                    color: '#a9a9a9'
                }
            }
        },
        yaxis: {
            title: {
                text: 'Average Delay (seconds)',
                style: {
                    color: '#a9a9a9'
                }
            },
            labels: {
                style: {
                    colors: '#a9a9a9'
                },
                formatter: function (val) {
                    return val ? val.toFixed(1) + 's' : '0s';
                }
            },
            min: 0
        },
        grid: {
            borderColor: '#555',
            strokeDashArray: 3
        },
        markers: {
            size: 4,
            colors: ['#00E396'],
            strokeColors: '#fff',
            strokeWidth: 2,
            hover: {
                size: 6
            }
        },
        tooltip: {
            theme: 'dark',
            x: {
                format: 'MMM dd, yyyy HH:mm'
            },
            y: {
                formatter: function (val) {
                    return val ? val.toFixed(1) + ' seconds' : '0 seconds';
                }
            },
            custom: function ({ series, seriesIndex, dataPointIndex, w }) {
                const data = w.globals.initialSeries[seriesIndex].data[dataPointIndex];
                if (data && data.tripId && data.vehicleId) {
                    return `
                        <div style="padding: 10px; background: #333; border-radius: 4px;">
                            <div><strong>Trip ID:</strong> ${data.tripId}</div>
                            <div><strong>Vehicle ID:</strong> ${data.vehicleId}</div>
                            <div><strong>Average Delay:</strong> ${data.y ? data.y.toFixed(1) : '0'} seconds</div>
                            <div><strong>Total Delay:</strong> ${data.totalDelay ? data.totalDelay.toFixed(1) : '0'} seconds</div>
                            <div><strong>Signals Traversed:</strong> ${data.signalTraversalCount || 0}</div>
                            <div><strong>Time:</strong> ${new Date(data.x).toLocaleString()}</div>
                        </div>
                    `;
                }
                return '';
            }
        },
        legend: {
            position: 'top',
            labels: {
                colors: '#a9a9a9'
            }
        }
    });
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        if (routeId && headsign) {
            fetchData();
        }
    }, [routeId, headsign, startDate, endDate]);

    const fetchData = async () => {
        setLoading(true);
        setError(null);

        try {
            // Convert dates to UTC format
            const formattedStartDate = convertToUTC(startDate);
            const formattedEndDate = convertToUTC(endDate);

            // Use the new API method for average route signal delay
            const result = await ApiService.getAverageRouteSignalDelay(routeId, headsign, formattedStartDate, formattedEndDate);

            if (result.success) {
                if (!result.data || !result.data.trips || result.data.trips.length === 0) {
                    setError('No data available for the selected time range');
                    setLoading(false);
                    return;
                }

                // Process the data for the line chart
                // Sort trips by timestamp first to ensure proper line connections
                const sortedTrips = result.data.trips.sort(
                    (a, b) => new Date(a.tripStartTimestamp).getTime() - new Date(b.tripStartTimestamp).getTime()
                );

                const dataSeries = [
                    {
                        name: `${routeId} - ${headsign} - Average Signal Delay`,
                        data: sortedTrips.map((trip) => ({
                            x: new Date(trip.tripStartTimestamp).getTime(),
                            y: trip.averageDelay,
                            tripId: trip.tripId,
                            vehicleId: trip.vehicleId,
                            totalDelay: trip.totalDelay,
                            signalTraversalCount: trip.signalTraversalCount
                        }))
                    }
                ];

                setSeries(dataSeries);

                if (onLoadComplete) {
                    onLoadComplete();
                }
            } else {
                setError(result.errorMessage || 'Failed to fetch data');
            }
        } catch (err) {
            console.error('Error fetching average route signal delay data:', err);
            setError('An error occurred while fetching data');
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
                <Typography variant="body1" color="error">
                    {error}
                </Typography>
            </Box>
        );
    }

    return (
        <Box sx={{ height: '100%', width: '100%', p: 2, backgroundColor: '#424242', borderRadius: 1 }}>
            {/* Header content */}
            <Typography variant="h6" sx={{ textAlign: 'center', mb: 0.5 }}>
                Average Route Signal Delay
            </Typography>
            <Typography variant="subtitle2" sx={{ textAlign: 'center', mb: 0.5 }}>
                Route {routeId} - {headsign}
            </Typography>
            <Typography variant="caption" sx={{ display: 'block', color: '#aaa', textAlign: 'center', mb: 1 }}>
                {formatDateDisplay(startDate)} - {formatDateDisplay(endDate)}
            </Typography>

            {/* Chart */}
            <Box sx={{ height: '400px' }}>
                <ApexCharts options={chartOptions} series={series} type="line" height="100%" />
            </Box>

            {/* Summary Statistics */}
            {series.length > 0 && series[0].data.length > 0 && (
                <Box sx={{ mt: 2, p: 2, backgroundColor: '#333', borderRadius: 1 }}>
                    <Typography variant="subtitle2" sx={{ mb: 1, color: '#fff' }}>
                        Summary Statistics
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
                        <Typography variant="body2" sx={{ color: '#aaa' }}>
                            Total Trips: {series[0].data.length}
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#aaa' }}>
                            Avg Delay: {(series[0].data.reduce((sum, trip) => sum + trip.y, 0) / series[0].data.length).toFixed(1)}s
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#aaa' }}>
                            Max Delay: {Math.max(...series[0].data.map((trip) => trip.y)).toFixed(1)}s
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#aaa' }}>
                            Min Delay: {Math.min(...series[0].data.map((trip) => trip.y)).toFixed(1)}s
                        </Typography>
                    </Box>
                </Box>
            )}
        </Box>
    );
};

export default AverageRouteSignalDelayReport;

import { lazy } from 'react';

// project import
import Loadable from 'components/Loadable';
import MinimalLayout from 'layout/MinimalLayout';

// render - login
import SignIn from 'pages/auth/Login';
const AuthRegister = Loadable(lazy(() => import('pages/auth/SignUp')));
//const AuthRegister = Loadable(lazy(() => import('pages/authentication/auth-forms/AuthRegister')));

// ==============================|| AUTH ROUTING ||============================== //

const LoginRoutes = {
    path: '/',
    element: <MinimalLayout />,
    children: [
        {
            path: '/',
            element: <SignIn />
        },
        {
            path: 'register',
            element: <AuthRegister />
        }
    ]
};

export default LoginRoutes;

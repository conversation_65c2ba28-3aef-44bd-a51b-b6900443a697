import { useState, useEffect } from 'react';

// material-ui
import { Typography, Grid, Box, IconButton } from '@mui/material';

// project import
import axios from 'axios';
import SignalList from 'pages/dashboard/SignalsList';
import MainCard from 'components/MainCard';
import MapBoxComponent from 'pages/dashboard/MapBoxComponent';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import { getAuthToken } from 'utils/auth';

// ==============================|| DASHBOARD - DEFAULT ||============================== //

const DashboardDefault = () => {
    const [devices, setDevices] = useState([]);
    const [mapCenter, setMapCenter] = useState({ latitude: 37.7577, longitude: -122.4376 }); // initial center
    const [mapZoom, setMapZoom] = useState(9); // initial zoom level
    const handleDeviceSelect = (device) => {
        setMapCenter({ latitude: device.latitude, longitude: device.longitude });
        console.log('Here2=' + device.latitude);
        setMapZoom(14);
    };
    const [isSignalListExpanded, setSignalListExpanded] = useState(true);

    const handleToggleSignalList = () => {
        setSignalListExpanded((prevState) => !prevState);
    };

    useEffect(() => {
        const apiUrl = `${process.env.REACT_APP_API_ENDPOINT}/signals`;
        const accessToken = getAuthToken();

        if (accessToken) {
            axios
                .get(apiUrl, {
                    headers: {
                        'Content-type': 'application/json;charset=UTF-8',
                        Authorization: 'Bearer ' + accessToken
                    }
                })
                .then((response) => {
                    setDevices(response.data.devices);
                })
                .catch((error) => console.error('Error fetching the devices data', error));
        }
    }, []);

    return (
        <Box sx={{ display: 'flex', flexDirection: 'column', height: '86vh', overflow: 'hidden' }}>
            <Box sx={{ flex: 1, overflow: 'hidden' }}>
                <MainCard content={false} sx={{ flex: 1, height: '100%' }}>
                    <MapBoxComponent center={mapCenter} zoom={mapZoom} isSignalListExpanded={isSignalListExpanded} />
                </MainCard>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', paddingRight: '1rem' }}>
                <IconButton onClick={handleToggleSignalList}>{isSignalListExpanded ? <ExpandMoreIcon /> : <ExpandLessIcon />}</IconButton>
            </Box>
            {isSignalListExpanded && (
                <Box sx={{ flex: 1, height: '100%' }}>
                    <MainCard content={false} sx={{ flex: 1, height: 'auto', boxSizing: 'content-box' }}>
                        <SignalList onDeviceSelect={handleDeviceSelect} />
                    </MainCard>
                </Box>
            )}
        </Box>
    );
};

export default DashboardDefault;

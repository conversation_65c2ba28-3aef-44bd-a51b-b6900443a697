import React, { useState, useEffect } from 'react';
import {
    Grid,
    Box,
    Stack,
    TextField,
    MenuItem,
    Select,
    InputLabel,
    FormControl,
    Button,
    CircularProgress,
    IconButton,
    Typography
} from '@mui/material';
import Tab from '@mui/material/Tab';
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import { RichTreeView } from '@mui/x-tree-view/RichTreeView';
import ApiService from 'utils/ApiService';
import DirectionsBusIcon from '@mui/icons-material/DirectionsBus';
import RouteIcon from '@mui/icons-material/Route';
import TrafficIcon from '@mui/icons-material/Traffic';
import FireTruckIcon from '@mui/icons-material/FireTruck';
import PolylineIcon from '@mui/icons-material/Polyline';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { DatePicker, TimePicker } from '@mui/x-date-pickers';
import { ReportsTab } from './Reports/ReportsTab';
import { wktToGeoJSON } from '@terraformer/wkt';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';

import MyLocationIcon from '@mui/icons-material/MyLocation';
// Component imports
import MapBoxComponent from 'pages/dashboard/MapBoxComponent';
import MainCard from 'components/MainCard';
import SignalList from 'pages/dashboard/SignalsList';
import RoutesList from 'pages/dashboard/RoutesList';
import ZonesList from 'pages/dashboard/ZonesList';
import DetailsTabs from 'pages/dashboard/DetailsTabs';
import AverageTravelTimeChart from './DashboardMetrics/AverageTravelTimeChart';
import AverageSpeedChart from './DashboardMetrics/AverageSpeedChart';
import TravelTimeReliabilityChart from './DashboardMetrics/TravelTimeReliabilityChart';

// Create a dark theme
const darkTheme = createTheme({
    palette: {
        mode: 'dark'
    }
});

const DashboardDefault = () => {
    // State variables
    const [vehicles, setVehicles] = useState([]);
    const [gtfsRoutes, setGtfsRoutes] = useState([]);
    const [transitRoutes, setTransitRoutes] = useState([]);
    const [gtfsTrips, setGtfsTrips] = useState([]);
    const [stops, setStops] = useState([]);
    const [stopTimes, setStopTimes] = useState([]);
    const [shapes, setShapes] = useState([]);
    const [zones, setZones] = useState([]);
    const [loading, setLoading] = useState(true);
    const [mapCenter, setMapCenter] = useState({ latitude: 37.7577, longitude: -122.4376 });
    const [mapZoom, setMapZoom] = useState(9);
    const [isSignalListExpanded, setSignalListExpanded] = useState(true);
    const [signals, setSignals] = useState([]);
    const [signalLoading, setSignalLoading] = useState(true);
    const [dataLoading, setDataLoading] = useState(false);
    const [chartLoading, setChartLoading] = useState(false);
    const [error, setError] = useState('');
    const [value, setValue] = useState('1');
    const [selectedRoute, setSelectedRoute] = useState(null);
    const [selectedVehicle, setSelectedVehicle] = useState(null);
    const [compareOption, setCompareOption] = useState('Realtime');
    const [compareToOption, setCompareToOption] = useState('Yesterday Same Time');
    const [startDate, setStartDate] = useState(new Date());
    const [endDate, setEndDate] = useState(new Date());
    const [startTime, setStartTime] = useState(new Date());
    const [endTime, setEndTime] = useState(new Date());
    const [travelTimeData, setTravelTimeData] = useState([]);
    const [speedData, setSpeedData] = useState([]);
    const [selectedZone, setSelectedZone] = useState(null); // State to track selected zone
    const [selectedZones, setSelectedZones] = useState([]); // Track selected zones
    const [zonesInitiallyLoaded, setZonesInitiallyLoaded] = useState(false);
    const [selectedRoutes, setSelectedRoutes] = useState([]); // Track selected Routes
    const [selectedVehicles, setSelectedVehicles] = useState([]); // Track selected vehicles
    const [selectedVehicleIds, setSelectedVehicleIds] = useState([]);
    const [detailsTab, setDetailsTab] = useState(0); // 0: Vehicle List, 1: Properties. Default to "Vehicle List" tab
    const [polygonGeometry, setPolygonGeometry] = useState(null); // Track the polygon geometry
    const [isEditingGeometry, setIsEditingGeometry] = useState(false); // Track if editing mode is enabled
    const [routesInitiallyLoaded, setRoutesInitiallyLoaded] = useState(false); // Track if routes have been initially loaded
    const [selectedRouteIndices, setSelectedRouteIndices] = useState([]); // Track selected row indices
    const [selectedZoneIndices, setSelectedZoneIndices] = useState([]);
    const [selectedSignalIds, setSelectedSignalIds] = useState([]);
    const [selectedSignals, setSelectedSignals] = useState([]);
    const [metricsExpanded, setMetricsExpanded] = useState(true);

    const handleVehicleSelect = (vehicleId) => {
        const selectedVehicle = vehicles.find((v) => v.vehicleId === vehicleId);
        if (selectedVehicle) {
            setMapCenter({ latitude: selectedVehicle.latitude, longitude: selectedVehicle.longitude });
            setMapZoom(16);
            setDataLoading(true);

            const tripId = String(selectedVehicle.tripId);
            Promise.all([
                ApiService.getStopsByTripId(tripId),
                ApiService.getStopTimesByTripId(tripId),
                ApiService.getShapesByTripId(tripId)
            ])
                .then(([stopsData, stopTimesData, shapesData]) => {
                    setStops(stopsData.success ? stopsData.stops : []);
                    setStopTimes(stopTimesData.success ? stopTimesData.stopTimes : []);
                    setShapes(shapesData.success ? shapesData.shapes : []);
                })
                .catch((error) => {
                    console.error('Error fetching vehicle data:', error);
                    setStops([]);
                    setStopTimes([]);
                    setShapes([]);
                })
                .finally(() => {
                    setDataLoading(false);
                });
        } else {
            console.error('Vehicle not found:', vehicleId);
        }
    };

    const handleDeviceSelect = (device) => {
        setMapCenter({ latitude: device.latitude, longitude: device.longitude });
        setMapZoom(16);
    };

    const handleAddZone = () => {
        const newZone = {
            id: null,
            name: '',
            type: 1, // TSP by default
            geometry: null
        };
        setSelectedZone(newZone);
        setDetailsTab(1); // Switch to the Properties tab
    };

    // Function to enable editing mode for the selected zone
    const handleEditGeometry = (zone) => {
        console.log('Current geometry type:', typeof zone.geometry);
        console.log('Current geometry value:', zone.geometry);

        if (!zone.geometry) {
            // Geometry is null, so just set up empty geometry or handle accordingly
            // For example, if editableZone expects GeoJSON, set it to null or an empty feature
            setPolygonGeometry(null);
            setIsEditingGeometry(true);
            return;
        }
        setSelectedZone(zone);

        if (zone.geometry && typeof zone.geometry === 'object' && zone.geometry.type) {
            setPolygonGeometry(zone.geometry);
        } else {
            setPolygonGeometry(wktToGeoJSON(zone.geometry));
        }

        //setPolygonGeometry(wktToGeoJSON(zone.geometry)); // Convert the geometry to GeoJSON and set it
        setIsEditingGeometry(true); // Enable editing mode
    };

    // Function to save the edited geometry
    const handleSaveGeometry = (updatedGeometry) => {
        setPolygonGeometry(updatedGeometry);
    };

    const handleLocateClick = () => {
        if (selectedVehicle) {
            const vehicle = vehicles.find((v) => v.vehicleId === selectedVehicle);
            if (vehicle) {
                setMapCenter({ latitude: vehicle.latitude, longitude: vehicle.longitude });
                setMapZoom(14);
                setDataLoading(true);

                const tripId = String(vehicle.tripId);
                Promise.all([
                    ApiService.getStopsByTripId(tripId),
                    ApiService.getStopTimesByTripId(tripId),
                    ApiService.getShapesByTripId(tripId)
                ])
                    .then(([stopsData, stopTimesData, shapesData]) => {
                        setStops(stopsData.success ? stopsData.stops : []);
                        setStopTimes(stopTimesData.success ? stopTimesData.stopTimes : []);
                        setShapes(shapesData.success ? shapesData.shapes : []);
                    })
                    .catch((error) => {
                        console.error('Error fetching vehicle data:', error);
                        setStops([]);
                        setStopTimes([]);
                        setShapes([]);
                    })
                    .finally(() => {
                        setDataLoading(false);
                    });
            } else {
                console.error('Vehicle not found:', selectedVehicle);
            }
        } else if (selectedRoute) {
            const routeVehicles = vehicles.filter((vehicle) => vehicle.routeId === selectedRoute);
            if (routeVehicles.length > 0) {
                setMapCenter({ latitude: routeVehicles[0].latitude, longitude: routeVehicles[0].longitude });
                setMapZoom(12);
            } else {
                console.error('No vehicles found for route:', selectedRoute);
            }
        } else if (selectedZone) {
            console.log('Locating zone:', selectedZone);
            const zone = zones.find((zone) => zone.id === parseInt(selectedZone.id));
            if (zone) {
                const coordinates = zone.geometry.match(/-?\d+(\.\d+)?/g).map(Number);
                const longitudes = coordinates.filter((_, index) => index % 2 === 0);
                const latitudes = coordinates.filter((_, index) => index % 2 !== 0);
                const avgLat = latitudes.reduce((acc, lat) => acc + lat, 0) / latitudes.length;
                const avgLng = longitudes.reduce((acc, lng) => acc + lng, 0) / longitudes.length;
                setMapCenter({ latitude: avgLat, longitude: avgLng });
                setMapZoom(17);
            } else {
                console.error('Zone not found:', selectedZone);
            }
        } else {
            console.log('No item selected for locating');
        }
    };

    const handleChange = (event, newValue) => {
        // Store the current tab value before changing it
        const previousValue = value;

        // Update the tab value
        setValue(newValue);

        // Only select all routes on initial load, not when switching back to the tab
        // This allows users to maintain their custom selections

        console.log(`Tab switched from ${previousValue} to ${newValue}`);
    };

    const handleSelectionChange = (event, selectedItems) => {
        console.log('Selected Items:', selectedItems);

        if (Array.isArray(selectedItems)) {
            if (selectedItems.length === 1) {
                const selectedItem = selectedItems[0];
                handleSelection(selectedItem);
            }
        } else if (typeof selectedItems === 'string') {
            handleSelection(selectedItems);
        } else {
            console.error('Unexpected selectedItems format:', selectedItems);
        }
    };

    const handleSelection = (selectedItem) => {
        const [type, actualId] = selectedItem.split('-');
        if (type === 'route') {
            console.log('Route selected:', actualId);
            setSelectedRoute(actualId);
            setSelectedVehicle(null);
            setSelectedZone(null);
        } else if (type === 'zone') {
            console.log('Zone selected:', actualId);
            setSelectedZone(zones.find((zone) => zone.id === parseInt(actualId)));
            setSelectedRoute(null);
            setSelectedVehicle(null);
        } else if (type === 'vehicle') {
            console.log('Vehicle selected:', actualId);
            setSelectedVehicle(actualId);
            setSelectedRoute(null);
            setSelectedZone(null);
        }
    };

    useEffect(() => {
        setLoading(true);

        // Create a debounced update function
        const debouncedUpdate = _.debounce((newData) => {
            // Process data in a web worker or use requestAnimationFrame for smoother UI
            requestAnimationFrame(() => {
                const filteredData = newData.filter((vehicle) => vehicle && vehicle.routeId && vehicle.routeId.trim() !== '');
                setVehicles(filteredData);
                setLoading(false);
            });
        }, 100); // 100ms debounce time

        const handleNewVehicleData = (newData) => {
            debouncedUpdate(newData);
        };

        ApiService.getRealTimeVehicleData(handleNewVehicleData);

        return () => {
            debouncedUpdate.cancel(); // Cancel any pending debounced calls
            ApiService.closeRealTimeVehicleDataConnection();
        };
    }, []);

    useEffect(() => {
        const fetchTrafficSignals = async () => {
            setSignalListExpanded(true);
            const result = await ApiService.getTrafficSignals();
            if (result.success) {
                setSignals(result.devices);
                setSignalListExpanded(false);
            } else {
                setError(result.errorMessage);
                setSignalListExpanded(false);
            }
        };

        fetchTrafficSignals();
    }, []);

    useEffect(() => {
        const fetchGtfsRoutes = async () => {
            const result = await ApiService.getGtfsRoutes();
            if (result.success) {
                setGtfsRoutes(result.routes);
            } else {
                setError(result.errorMessage);
            }
        };

        const fetchGtfsTrips = async () => {
            const result = await ApiService.getGtfsTrips();
            if (result.success) {
                setGtfsTrips(result.trips);
            } else {
                setError(result.errorMessage);
            }
        };

        const fetchZones = async () => {
            const result = await ApiService.getZones();
            if (result.success) {
                setZones(result.zones);
            } else {
                console.error('Error fetching zones:', result.errorMessage);
            }
        };

        const fetchRoutesWithHeadsigns = async () => {
            const result = await ApiService.getTransitRoutesWithHeadsigns();
            if (result.success) {
                setTransitRoutes(result.routes); // Assuming the API returns routes in the expected format
            } else {
                setError(result.errorMessage);
            }
        };

        fetchRoutesWithHeadsigns();
        fetchGtfsRoutes();
        fetchGtfsTrips();
        fetchZones();
    }, []);

    const refreshZones = async () => {
        setIsEditingGeometry(false);
        try {
            const zonesResponse = await ApiService.getZones();
            if (zonesResponse.success) {
                setZones(zonesResponse.zones);
            } else {
                console.error('Failed to fetch zones:', zonesResponse.errorMessage);
            }
        } catch (error) {
            console.error('Error fetching zones:', error);
        }
    };

    // const getTreeItemsFromRoutes = () => {
    //     // Group routes by agencyName
    //     const groupedRoutes = transitRoutes.reduce((acc, route) => {
    //         if (!acc[route.agencyName]) {
    //             acc[route.agencyName] = [];
    //         }
    //         acc[route.agencyName].push(route);
    //         return acc;
    //     }, {});

    //     return Object.keys(groupedRoutes).map((agencyName) => {
    //         const agencyRoutes = groupedRoutes[agencyName];
    //         return {
    //             id: `agency-${agencyName}`,
    //             label: `${agencyName} - Routes`,
    //             children: agencyRoutes.map((route, index) => {
    //                 // Ensure unique ID by including the index
    //                 const routeLabel = `${route.routeShortName} - ${route.tripHeadsign}`;
    //                 const routeId = `route-${route.routeId}-${route.tripHeadsign}-${index}`; // Added index for uniqueness

    //                 return {
    //                     id: routeId,
    //                     label: routeLabel,
    //                     icon: RouteIcon
    //                 };
    //             })
    //         };
    //     });
    // };

    const handleRouteSelectionChange = (selectedItems, selectedIndices) => {
        setSelectedRoutes(selectedItems); // Store the selected routes
        setSelectedRouteIndices(selectedIndices); // Store the selected indices

        let selectedVehicleIds = []; // Initialize as an array

        // Process route selection
        selectedItems.forEach((route) => {
            const routeVehicles = vehicles.filter((vehicle) => {
                const trip = gtfsTrips.find((trip) => trip.tripId === vehicle.tripId);
                // Add null check before accessing tripHeadsign
                return vehicle.routeId === route.routeId && trip && route.tripHeadsign === trip.tripHeadsign;
            });

            // Use map to get the array of vehicle IDs
            const newSelectedVehicleIds = routeVehicles.map((vehicle) => {
                return `vehicle-${vehicle.vehicleId}-${vehicle.routeId}-${vehicle.tripId}`;
            });

            // Properly concatenate arrays using the spread operator
            selectedVehicleIds = [...selectedVehicleIds, ...newSelectedVehicleIds];
        });

        setSelectedVehicleIds(selectedVehicleIds);
    };

    const handleZoneSelectionChange = (selectedZones, selectedIndices) => {
        setSelectedZones(selectedZones); // Store the selected zones
        if (selectedIndices) {
            setSelectedZoneIndices(selectedIndices); // Store the selected indices
        }
        console.log('Zones selected:', selectedZones);
        console.log('Zone indices selected:', selectedIndices);
    };

    // useEffect to handle dynamic vehicle updates and maintain selectedVehicleIds
    useEffect(() => {
        let updatedSelectedVehicleIds = [...selectedVehicleIds]; // Copy the existing selected vehicle IDs

        // Remove vehicle IDs that no longer exist in the vehicles array
        updatedSelectedVehicleIds = updatedSelectedVehicleIds.filter((vehicleId) => {
            return vehicles.some((vehicle) => {
                const currentVehicleId = `vehicle-${vehicle.vehicleId}-${vehicle.routeId}-${vehicle.tripId}`;
                return currentVehicleId === vehicleId;
            });
        });

        // Automatically select new vehicles if their route is already selected
        selectedRoutes.forEach((route) => {
            const routeVehicles = vehicles.filter((vehicle) => {
                const trip = gtfsTrips.find((trip) => trip.tripId === vehicle.tripId);
                // Add null check before accessing tripHeadsign
                return vehicle.routeId === route.routeId && trip && route.tripHeadsign === trip.tripHeadsign;
            });

            const newSelectedVehicleIds = routeVehicles.map((vehicle) => {
                return `vehicle-${vehicle.vehicleId}-${vehicle.routeId}-${vehicle.tripId}`;
            });

            newSelectedVehicleIds.forEach((vehicleId) => {
                if (!updatedSelectedVehicleIds.includes(vehicleId)) {
                    updatedSelectedVehicleIds.push(vehicleId); // Add new vehicle IDs to the list
                }
            });
        });

        // Set the updated selectedVehicleIds state
        if (updatedSelectedVehicleIds.length !== selectedVehicleIds.length) {
            setSelectedVehicleIds(updatedSelectedVehicleIds);
        }

        // Filter vehicles based on the updated selectedVehicleIds
        const updatedFilteredVehicles = vehicles.filter((vehicle) => {
            const vehicleId = `vehicle-${vehicle.vehicleId}-${vehicle.routeId}-${vehicle.tripId}`;
            return updatedSelectedVehicleIds.includes(vehicleId);
        });

        // Update the selected vehicles with the filtered result
        setSelectedVehicles(updatedFilteredVehicles);
        //console.log('Updated Filtered Vehicles:', updatedFilteredVehicles);
    }, [vehicles, selectedRoutes, selectedVehicleIds, gtfsTrips]); // Re-run when vehicles, selectedRoutes, selectedVehicleIds, or gtfsTrips change

    const handleZoneSelection = (event, selectedItems) => {
        let newSelectedZones = [...selectedZones];

        // Get the selected parent nodes (TSP, EVP) and their children zones
        const isParentTSPSelected = selectedItems.includes('TSP');
        const isParentEVPSelected = selectedItems.includes('EVP');

        let allSelectionFlag = false;
        let individualZoneSelections = [];

        const tspZones = zones.filter((zone) => zone.type === 1).map((zone) => `zone-${zone.id}`);
        const evpZones = zones.filter((zone) => zone.type === 2).map((zone) => `zone-${zone.id}`);

        // Handle TSP parent node selection
        if (isParentTSPSelected && !newSelectedZones.includes('TSP')) {
            // If TSP is selected, add TSP and all its child zones to the selected zones
            newSelectedZones = [...new Set([...newSelectedZones, 'TSP', ...tspZones])];
            allSelectionFlag = true;
        } else if (!isParentTSPSelected && newSelectedZones.includes('TSP')) {
            // If TSP is deselected, remove TSP and all its child zones
            newSelectedZones = newSelectedZones.filter((zone) => !tspZones.includes(zone) && zone !== 'TSP');
            allSelectionFlag = true;
        }

        // Handle EVP parent node selection
        if (isParentEVPSelected && !newSelectedZones.includes('EVP')) {
            // If EVP is selected, add EVP and all its child zones to the selected zones
            newSelectedZones = [...new Set([...newSelectedZones, 'EVP', ...evpZones])];
            allSelectionFlag = true;
        } else if (!isParentEVPSelected && newSelectedZones.includes('EVP')) {
            // If EVP is deselected, remove EVP and all its child zones
            newSelectedZones = newSelectedZones.filter((zone) => !evpZones.includes(zone) && zone !== 'EVP');
            allSelectionFlag = true;
        }

        // Handle individual zone selections
        if (!allSelectionFlag) {
            individualZoneSelections = selectedItems.filter((item) => item.startsWith('zone-'));

            // To support deselecting individual zones:
            // If a zone is in selectedItems but not in newSelectedZones, add it
            // If a zone is in newSelectedZones but not in selectedItems, remove it
            newSelectedZones = newSelectedZones.filter((zone) => {
                // Keep zones that are not being deselected individually
                return individualZoneSelections.includes(zone) || !zone.startsWith('zone-');
            });
            // Add the new individual selections
            newSelectedZones = [...new Set([...newSelectedZones, ...individualZoneSelections])];
        }

        // Update the state with the new selected zones
        setSelectedZones(newSelectedZones);
    };

    // Generate tree items with checkboxes for TSP and EVP zones
    const getTreeItemsFromZones = () => {
        const tspZones = zones.filter((zone) => zone.type === 1);
        const evpZones = zones.filter((zone) => zone.type === 2);

        const getChildren = (zones) =>
            zones.map((zone) => ({
                id: `zone-${zone.id}`,
                label: zone.name
                // children: zone.priorities.map((priority) => ({
                //     id: `priority-${priority.id}`,
                //     label: priority.name,
                //     icon: <DirectionsBusIcon />
                // }))
            }));

        return [
            {
                id: 'TSP',
                label: 'TSP Zones',
                children: getChildren(tspZones)
            },
            {
                id: 'EVP',
                label: 'EVP Zones',
                children: getChildren(evpZones)
            }
        ];
    };

    // const filteredVehicles = selectedRoute
    //     ? vehicles.filter((vehicle) => vehicle.routeId === selectedRoute)
    //     : selectedVehicle
    //     ? vehicles.filter((vehicle) => vehicle.vehicleId === selectedVehicle)
    //     : [];

    const handleCompareOptionChange = (event) => {
        setCompareOption(event.target.value);
        let newCompareToOption = '';
        if (event.target.value === 'Realtime') {
            newCompareToOption = 'Yesterday Same Time';
        } else if (event.target.value === 'Yesterday') {
            newCompareToOption = 'Last week Same Day';
        } else if (event.target.value === 'Period-1') {
            newCompareToOption = 'Period-2';
        }
        setCompareToOption(newCompareToOption);
    };

    const handleCompareToOptionChange = (event) => {
        setCompareToOption(event.target.value);
    };

    const formatDateTime = (date, time) => {
        const dateStr = date.toISOString().split('T')[0];
        const timeStr = time.toTimeString().split(' ')[0];
        return `${dateStr}T${timeStr}Z`;
    };

    const fetchComparisonData = async () => {
        setChartLoading(true);

        const adjustTimeByHours = (date, hours) => {
            return new Date(date.getTime() - hours * 60 * 60 * 1000);
        };

        let start1, end1, start2, end2;
        const now = new Date();

        if (compareOption === 'Realtime') {
            end1 = new Date();
            start1 = new Date(end1.getTime() - 60 * 60 * 1000); // 1 hour before end1

            if (compareToOption === 'Yesterday Same Time') {
                start2 = new Date(start1.getTime() - 24 * 60 * 60 * 1000); // 1 day before start1
                end2 = new Date(end1.getTime() - 24 * 60 * 60 * 1000); // 1 day before end1
            } else if (compareToOption === 'Last week Same Day') {
                start2 = new Date(start1.getTime() - 7 * 24 * 60 * 60 * 1000); // 1 week before start1
                end2 = new Date(end1.getTime() - 7 * 24 * 60 * 60 * 1000); // 1 week before end1
            }
        } else if (compareOption === 'Yesterday') {
            start1 = new Date(now.getTime() - 24 * 60 * 60 * 1000); // yesterday start
            end1 = new Date(); // now

            if (compareToOption === 'Last week Same Day') {
                start2 = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000); // 1 week before now
                end2 = new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000); // 6 days before now
            }
        } else if (compareOption === 'Period-1') {
            start1 = new Date(startDate);
            end1 = new Date(startDate);
            start2 = new Date(endDate);
            end2 = new Date(endDate);
            start1.setHours(startTime.getHours(), startTime.getMinutes(), startTime.getSeconds());
            end1.setHours(endTime.getHours(), endTime.getMinutes(), endTime.getSeconds());
            start2.setHours(startTime.getHours(), startTime.getMinutes(), startTime.getSeconds());
            end2.setHours(endTime.getHours(), endTime.getMinutes(), endTime.getSeconds());
        }

        // Adjust times by subtracting 4 hours
        start1 = adjustTimeByHours(start1, 4);
        end1 = adjustTimeByHours(end1, 4);
        start2 = adjustTimeByHours(start2, 4);
        end2 = adjustTimeByHours(end2, 4);

        try {
            const [data1, data2] = await Promise.all([
                ApiService.getAvgSpeedAndTravelTimeForRouteBetweenTwoDates(selectedRoute, start1.toISOString(), end1.toISOString()),
                ApiService.getAvgSpeedAndTravelTimeForRouteBetweenTwoDates(selectedRoute, start2.toISOString(), end2.toISOString())
            ]);

            if (data1.success && data2.success) {
                const travelTime1 = (data1.data.averageTravelTime / 60).toFixed(1);
                const travelTime2 = (data2.data.averageTravelTime / 60).toFixed(1);
                const speed1 = data1.data.averageSpeed.toFixed(1);
                const speed2 = data2.data.averageSpeed.toFixed(1);

                setTravelTimeData([
                    { name: 'Avg. Travel Time', [compareOption]: parseFloat(travelTime1), [compareToOption]: parseFloat(travelTime2) }
                ]);
                setSpeedData([{ name: 'Avg. Speed', [compareOption]: parseFloat(speed1), [compareToOption]: parseFloat(speed2) }]);
            } else {
                console.error('API response not successful:', data1, data2);
            }
        } catch (error) {
            console.error('Error fetching comparison data:', error);
        }

        setChartLoading(false);
    };

    const handleApply = () => {
        if (compareOption !== 'Realtime' && endTime <= startTime) {
            alert('End time must be later than start time');
            return;
        }
        if (selectedRoute) {
            fetchComparisonData();
        }
    };

    const [mapTabValue, setMapTabValue] = useState('1');

    const handleMapTabChange = (event, newValue) => {
        setMapTabValue(newValue);
    };

    const getAllRouteItemIds = (items) => {
        const ids = [];

        const collectIds = (nodes) => {
            nodes.forEach((node) => {
                ids.push(node.id); // Add parent node ID
                // if (node.children) {
                //     collectIds(node.children); // Recursively add child node IDs
                // }
            });
        };

        collectIds(items);
        return ids;
    };

    const handleZoneEdit = (zone) => {
        setSelectedZone(zone);
        setDetailsTab(1); // Switch to the "Properties" tab
    };

    const handleZoneDelete = () => {
        setSelectedZone(null);
        refreshZones();
    };

    const handleSignalSelectionChange = (signalIds, signalsData) => {
        console.log('Signal selection changed:', signalIds, signalsData);
        // Ensure signalIds is always an array (even if empty)
        setSelectedSignalIds(signalIds || []);
        setSelectedSignals(signalsData || []);
    };

    // Add this useEffect to select all transit routes by default when the component mounts
    useEffect(() => {
        if (transitRoutes.length > 0 && selectedRoutes.length === 0) {
            handleRouteSelectionChange(transitRoutes);
        }
    }, [transitRoutes]);

    // Add this useEffect to select all zones by default when the component mounts
    useEffect(() => {
        if (zones.length > 0 && selectedZones.length === 0 && !zonesInitiallyLoaded) {
            const allZones = zones.map((zone) => zone);
            const allIndices = Array.from({ length: zones.length }, (_, i) => i);
            handleZoneSelectionChange(allZones, allIndices);
            setZonesInitiallyLoaded(true);
        }
    }, [zones, zonesInitiallyLoaded]);

    const toggleMetricsPanel = () => {
        setMetricsExpanded(!metricsExpanded);

        // Trigger a resize event after a short delay to ensure components update
        setTimeout(() => {
            window.dispatchEvent(new Event('resize'));
        }, 300); // Match this with the transition duration
    };

    return (
        <ThemeProvider theme={darkTheme}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
                <Box sx={{ display: 'flex', flexDirection: 'column', height: '91vh', overflow: 'hidden' }}>
                    <Grid container spacing={3} sx={{ flex: 1, overflow: 'hidden', height: '100%' }}>
                        <Grid item xs={12} md={2}>
                            <TabContext value={value}>
                                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                                    <TabList onChange={handleChange} aria-label="lab API tabs example">
                                        <Tab icon={<TrafficIcon />} value="1" title="Signals" />
                                        <Tab icon={<DirectionsBusIcon />} value="2" title="Transit" />
                                        <Tab icon={<FireTruckIcon />} value="3" title="Emergency" />
                                        <Tab icon={<PolylineIcon />} value="4" title="Zones" />
                                    </TabList>
                                </Box>
                                <TabPanel value="1" sx={{ padding: 0 }}>
                                    {!loading && (
                                        <Box sx={{ display: 'flex', flexDirection: 'row', height: '100%', width: '100%' }}>
                                            <MainCard content={false} sx={{ flex: 1, boxSizing: 'border-box', width: '100%' }}>
                                                <SignalList
                                                    onDeviceSelect={handleDeviceSelect}
                                                    signals={signals}
                                                    onSignalSelectionChange={handleSignalSelectionChange}
                                                    selectedSignalIds={selectedSignalIds}
                                                    sx={{ height: '100%' }}
                                                />
                                            </MainCard>
                                        </Box>
                                    )}
                                </TabPanel>
                                <TabPanel value="2" sx={{ padding: 0 }}>
                                    <RoutesList
                                        routes={transitRoutes}
                                        onRouteSelectionChange={handleRouteSelectionChange}
                                        initiallyLoaded={routesInitiallyLoaded}
                                        setInitiallyLoaded={setRoutesInitiallyLoaded}
                                        selectedIndices={selectedRouteIndices}
                                    />
                                </TabPanel>
                                <TabPanel value="3" sx={{ padding: 0 }}>
                                    <Typography variant="h6" sx={{ marginTop: 2 }}>
                                        No Emergency Vehicles
                                    </Typography>
                                </TabPanel>
                                <TabPanel value="4" sx={{ padding: 0 }}>
                                    <div>
                                        <ZonesList
                                            zones={zones}
                                            onZoneSelectionChange={handleZoneSelectionChange}
                                            onZoneEdit={handleZoneEdit}
                                            onAddZone={handleAddZone}
                                            initiallyLoaded={zonesInitiallyLoaded}
                                            setInitiallyLoaded={setZonesInitiallyLoaded}
                                            selectedIndices={selectedZoneIndices}
                                        />
                                    </div>
                                </TabPanel>
                            </TabContext>
                        </Grid>
                        {!loading && (
                            <Grid
                                item
                                xs={12}
                                md={isSignalListExpanded ? 3.9 : metricsExpanded ? 8 : 9.5}
                                sx={{
                                    height: { xs: '50%', md: '100%' },
                                    transition: 'all 0.3s ease'
                                }}
                            >
                                <TabContext value={mapTabValue}>
                                    <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                                        <TabList onChange={handleMapTabChange}>
                                            <Tab label="Map" value="1" />
                                            <Tab label="Report" value="2" />
                                        </TabList>
                                    </Box>
                                    {/* Both TabPanels are always rendered, but only one is visible at a time */}
                                    <Box sx={{ position: 'relative', height: '100%', width: '100%' }}>
                                        <Box
                                            sx={{
                                                position: 'absolute',
                                                top: 0,
                                                left: 0,
                                                width: '100%',
                                                height: '100%',
                                                display: mapTabValue === '1' ? 'flex' : 'none',
                                                flexDirection: 'column'
                                            }}
                                        >
                                            {/* Map content */}
                                            <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', height: '100%' }}>
                                                <Box sx={{ flexGrow: 1 }}>
                                                    <MainCard content={false} sx={{ width: '100%', height: '100%' }}>
                                                        <MapBoxComponent
                                                            center={mapCenter}
                                                            zoom={mapZoom}
                                                            isSignalListExpanded={isSignalListExpanded}
                                                            vehicles={selectedVehicles}
                                                            signals={signals}
                                                            selectedSignalIds={selectedSignalIds}
                                                            stops={stops}
                                                            stopTimes={stopTimes}
                                                            shapes={shapes}
                                                            dataLoading={dataLoading}
                                                            gtfsRoutes={gtfsRoutes}
                                                            gtfsTrips={gtfsTrips}
                                                            selectedZone={selectedZone}
                                                            selectedZones={selectedZones}
                                                            zones={zones}
                                                            polygonGeometry={polygonGeometry}
                                                            onGeometryChange={handleSaveGeometry}
                                                            editable={isEditingGeometry}
                                                            sx={{ width: '100%' }}
                                                        />
                                                    </MainCard>
                                                </Box>
                                                <Box sx={{ flexShrink: 0, height: '30vh', mt: 2 }}>
                                                    <DetailsTabs
                                                        vehicles={selectedVehicles}
                                                        onVehicleSelect={handleVehicleSelect}
                                                        selectedZone={selectedZone}
                                                        onEditGeometry={handleEditGeometry}
                                                        onGeometryChange={handleSaveGeometry}
                                                        updatedGeometry={polygonGeometry}
                                                        onZoneUpdate={refreshZones}
                                                        defaultTab={detailsTab}
                                                        onZoneDelete={handleZoneDelete}
                                                    />
                                                </Box>
                                            </Box>
                                        </Box>
                                        <Box
                                            sx={{
                                                position: 'absolute',
                                                top: 0,
                                                left: 0,
                                                width: '100%',
                                                height: '100%',
                                                display: mapTabValue === '2' ? 'flex' : 'none',
                                                flexDirection: 'column',
                                                overflow: 'auto'
                                            }}
                                        >
                                            {/* Report content */}
                                            <ReportsTab
                                                currentTab={value}
                                                selectedRoutes={selectedRoutes}
                                                selectedSignals={selectedSignals}
                                            />
                                        </Box>
                                    </Box>
                                </TabContext>
                            </Grid>
                        )}
                        <Grid
                            item
                            xs={12}
                            md={metricsExpanded ? 2 : 0.3}
                            sx={{
                                transition: 'all 0.3s ease',
                                overflow: 'hidden',
                                display: 'flex',
                                flexDirection: 'column'
                            }}
                        >
                            <MainCard
                                title={
                                    <Box
                                        sx={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'flex-start',
                                            minWidth: metricsExpanded ? 'auto' : '40px',
                                            maxWidth: metricsExpanded ? 'auto' : '10px'
                                        }}
                                    >
                                        <IconButton
                                            onClick={toggleMetricsPanel}
                                            size="small"
                                            sx={{
                                                mr: metricsExpanded ? 1 : 0,

                                                transition: 'transform 0.3s ease'
                                            }}
                                            aria-label={metricsExpanded ? 'Collapse metrics panel' : 'Expand metrics panel'}
                                        >
                                            {metricsExpanded ? <ChevronRightIcon /> : <ChevronLeftIcon />}
                                        </IconButton>
                                        <Typography
                                            variant="h6"
                                            component="div"
                                            sx={{
                                                opacity: metricsExpanded ? 1 : 0,
                                                transition: 'opacity 0.3s ease',
                                                whiteSpace: 'nowrap'
                                            }}
                                        >
                                            Performance Metrics
                                        </Typography>
                                    </Box>
                                }
                                sx={{
                                    overflow: 'hidden',
                                    height: '100%',
                                    '& .MuiCardContent-root': {
                                        display: metricsExpanded ? 'block' : 'none',
                                        transition: 'all 0.3s ease'
                                    }
                                }}
                            >
                                {metricsExpanded && (
                                    <Stack spacing={3} sx={{ paddingRight: '15px' }}>
                                        <Grid container spacing={2}>
                                            <Grid item xs={6}>
                                                <FormControl fullWidth>
                                                    <InputLabel id="compare-label">Compare</InputLabel>
                                                    <Select
                                                        labelId="compare-label"
                                                        value={compareOption}
                                                        label="Compare"
                                                        onChange={handleCompareOptionChange}
                                                    >
                                                        <MenuItem value="Realtime">Last Hour</MenuItem>
                                                        <MenuItem value="Yesterday">Yesterday</MenuItem>
                                                        <MenuItem value="Period-1">Period-1</MenuItem>
                                                    </Select>
                                                </FormControl>
                                            </Grid>
                                            <Grid item xs={6}>
                                                <FormControl fullWidth>
                                                    <InputLabel id="compare-to-label">to</InputLabel>
                                                    <Select
                                                        labelId="compare-to-label"
                                                        value={compareToOption}
                                                        label="to"
                                                        onChange={handleCompareToOptionChange}
                                                    >
                                                        {compareOption === 'Realtime' ? (
                                                            [
                                                                <MenuItem key="Yesterday Same Time" value="Yesterday Same Time">
                                                                    Yesterday Same Time
                                                                </MenuItem>,
                                                                <MenuItem key="Last week Same Day" value="Last week Same Day">
                                                                    Last week Same Day
                                                                </MenuItem>
                                                            ]
                                                        ) : compareOption === 'Yesterday' ? (
                                                            <MenuItem value="Last week Same Day">Last week Same Day</MenuItem>
                                                        ) : compareOption === 'Period-1' ? (
                                                            <MenuItem value="Period-2">Period-2</MenuItem>
                                                        ) : null}
                                                    </Select>
                                                </FormControl>
                                            </Grid>
                                        </Grid>

                                        {compareOption === 'Period-1' && (
                                            <Grid container spacing={2} sx={{ paddingRight: '15px' }}>
                                                <Grid item xs={6}>
                                                    <DatePicker
                                                        label="Period-1"
                                                        value={startDate}
                                                        onChange={(newValue) => setStartDate(newValue)}
                                                        renderInput={(params) => <TextField {...params} />}
                                                    />
                                                </Grid>
                                                <Grid item xs={6}>
                                                    <DatePicker
                                                        label="Period-2"
                                                        value={endDate}
                                                        onChange={(newValue) => setEndDate(newValue)}
                                                        renderInput={(params) => <TextField {...params} />}
                                                    />
                                                </Grid>
                                            </Grid>
                                        )}
                                        {compareOption === 'Period-1' && (
                                            <Grid container spacing={2} sx={{ paddingRight: '15px' }}>
                                                <Grid item xs={6}>
                                                    <TimePicker
                                                        label="Start Time"
                                                        value={startTime}
                                                        onChange={(newValue) => setStartTime(newValue)}
                                                        renderInput={(params) => <TextField {...params} />}
                                                    />
                                                </Grid>
                                                <Grid item xs={6}>
                                                    <TimePicker
                                                        label="End Time"
                                                        value={endTime}
                                                        onChange={(newValue) => setEndTime(newValue)}
                                                        renderInput={(params) => <TextField {...params} />}
                                                    />
                                                </Grid>
                                            </Grid>
                                        )}
                                        <Button variant="outlined" color="primary" onClick={handleApply}>
                                            Apply
                                        </Button>
                                        {chartLoading ? (
                                            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '350px' }}>
                                                <CircularProgress />
                                            </Box>
                                        ) : (
                                            <>
                                                <AverageTravelTimeChart
                                                    data={travelTimeData}
                                                    compareOption={compareOption}
                                                    compareToOption={compareToOption}
                                                />
                                                <AverageSpeedChart
                                                    data={speedData}
                                                    compareOption={compareOption}
                                                    compareToOption={compareToOption}
                                                />
                                                <TravelTimeReliabilityChart
                                                    data={speedData}
                                                    compareOption={compareOption}
                                                    compareToOption={compareToOption}
                                                />
                                            </>
                                        )}
                                    </Stack>
                                )}
                            </MainCard>
                        </Grid>
                    </Grid>
                </Box>
            </LocalizationProvider>
        </ThemeProvider>
    );
};

export default DashboardDefault;

import React, { useEffect, useState } from 'react';
import ApexCharts from 'react-apexcharts';
import ApiService from '../../../../utils/ApiService';
import { Box, CircularProgress, Typography } from '@mui/material';

const TripTravelTimeReport = ({ routeId, headsign, startDate, endDate, routeName, onLoadComplete }) => {
    const [series, setSeries] = useState([]);
    const [options, setOptions] = useState({
        chart: {
            height: 350,
            type: 'area',
            zoom: {
                enabled: true,
                type: 'xy',
                autoScaleYaxis: true
            },
            toolbar: {
                show: true,
                tools: {
                    download: true,
                    selection: true,
                    zoom: true,
                    zoomin: true,
                    zoomout: true,
                    pan: true,
                    reset: true
                },
                autoSelected: 'zoom',
                style: {
                    backgroundColor: 'transparent',
                    color: '#a9a9a9',
                    borderRadius: '6px'
                }
            },
            background: 'transparent',
            foreColor: '#a9a9a9'
        },
        theme: {
            mode: 'dark',
            palette: 'palette1'
        },
        dataLabels: {
            enabled: false
        },
        markers: {
            size: 0
        },
        stroke: {
            curve: 'smooth',
            width: 3,
            lineCap: 'round'
        },
        fill: {
            type: 'gradient',
            gradient: {
                shadeIntensity: 1,
                inverseColors: false,
                opacityFrom: 0.5,
                opacityTo: 0.1,
                stops: [0, 90, 100]
            }
        },
        //title: {
        //    text: 'Trip Travel Time Analysis',
        //    align: 'center',
        //    style: {
        //        color: '#a9a9a9'
        //    }
        // },
        //subtitle: {
        //    text: '',
        //    align: 'center',
        //    style: {
        //        color: '#a9a9a9'
        //    }
        //},
        grid: {
            xaxis: {
                lines: {
                    show: true,
                    color: '#555555' // Lighter color for x-axis grid lines
                }
            },
            yaxis: {
                lines: {
                    show: true,
                    color: '#555555' // Lighter color for y-axis grid lines
                }
            },
            borderColor: '#555555', // Lighter border color
            strokeDashArray: 0
        },
        xaxis: {
            type: 'datetime',
            labels: {
                style: {
                    colors: '#a9a9a9'
                },
                datetimeUTC: false,
                format: 'HH:mm'
            }
        },
        yaxis: {
            title: {
                text: 'Travel Time (seconds)',
                style: {
                    color: '#a9a9a9',
                    fontSize: '14px',
                    fontWeight: 600,
                    fontFamily: "'Roboto', sans-serif"
                }
            },
            labels: {
                style: {
                    colors: '#a9a9a9',
                    fontSize: '12px',
                    fontFamily: "'Roboto', sans-serif"
                },
                formatter: function (val) {
                    return val.toFixed(0);
                }
            },
            min: 0,
            max: undefined,
            tickAmount: 6,
            forceNiceScale: true
        },
        tooltip: {
            theme: 'dark',
            shared: false,
            x: {
                format: 'MMM dd, yyyy HH:mm:ss'
            },
            custom: function ({ series, seriesIndex, dataPointIndex, w }) {
                const point = w.config.series[seriesIndex].data[dataPointIndex];
                const time = new Date(point.x).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                const travelTime = point.y;
                const tripId = point.tripId || 'Unknown';

                return `
                    <div class="apexcharts-tooltip-box" style="padding: 8px; background: #222; color: #fff; border: 1px solid #444;">
                        <div style="font-weight: bold; margin-bottom: 5px;">${time}</div>
                        <div style="margin-bottom: 5px;">Trip: ${tripId}</div>
                        <div style="margin-top: 5px;">
                            <strong>Travel Time: ${travelTime.toFixed(0)} seconds</strong>
                        </div>
                    </div>
                `;
            },
            y: {
                formatter: function (val) {
                    return val.toFixed(0) + ' seconds';
                }
            }
        },
        legend: {
            position: 'top',
            labels: {
                colors: '#a9a9a9'
            }
        }
    });
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        if (routeId && headsign) {
            fetchData();
        }
    }, [routeId, headsign, startDate, endDate]);

    const fetchData = async () => {
        setLoading(true);
        setError(null);

        try {
            // Convert dates to UTC format
            const formattedStartDate = convertToUTC(startDate);
            const formattedEndDate = convertToUTC(endDate);

            // Use the updated API method with headsign parameter
            const result = await ApiService.getTravelTimesByHeadsign(routeId, headsign, formattedStartDate, formattedEndDate);

            if (result.success) {
                if (!result.data || result.data.length === 0) {
                    setError('No data available for the selected time range');
                    setLoading(false);
                    return;
                }

                // Store the raw data for tooltip access
                const tripData = result.data;

                // The API returns an array of trip data objects
                const dataSeries = [
                    {
                        name: `${routeId} - ${headsign}`,
                        data: result.data.map((trip, index) => ({
                            x: new Date(trip.startTime).getTime(),
                            y: trip.travelTime,
                            tripId: trip.tripId || 'Unknown',
                            tripIndex: index
                        }))
                    }
                ];

                setSeries(dataSeries);

                // Update options to use the embedded trip data
                setOptions((prevOptions) => ({
                    ...prevOptions,
                    tooltip: {
                        ...prevOptions.tooltip,
                        custom: function ({ series, seriesIndex, dataPointIndex, w }) {
                            const point = w.config.series[seriesIndex].data[dataPointIndex];
                            const time = new Date(point.x).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                            const travelTime = point.y;
                            const tripId = point.tripId || 'Unknown';

                            return `
                                <div class="apexcharts-tooltip-box" style="padding: 8px; background: #222; color: #fff; border: 1px solid #444;">
                                    <div style="font-weight: bold; margin-bottom: 5px;">${time}</div>
                                    <div style="margin-bottom: 5px;">Trip: ${tripId}</div>
                                    <div style="margin-top: 5px;">
                                        <strong>Travel Time: ${travelTime.toFixed(0)} seconds</strong>
                                    </div>
                                </div>
                            `;
                        }
                    }
                }));

                if (onLoadComplete) {
                    onLoadComplete();
                }
            } else {
                setError(result.errorMessage || 'Failed to fetch data');
            }
        } catch (err) {
            console.error('Error fetching travel time data:', err);
            setError('An error occurred while fetching data');
        } finally {
            setLoading(false);
        }
    };

    // Convert local date to UTC ISO string
    const convertToUTC = (date) => {
        return date.toISOString();
    };

    // This function is no longer needed as we're using toISOString() directly
    // Keeping it for reference in case we need to modify the format
    // const formatDate = (date) => {
    //     const tzOffset = -date.getTimezoneOffset() * 60000;
    //     const localISOTime = new Date(date.getTime() + tzOffset).toISOString().slice(0, 19);
    //     return localISOTime;
    // };

    const formatDateDisplay = (date) => {
        return date.toLocaleString('en-US', {
            month: 'short',
            day: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return (
        <Box sx={{ width: '100%', height: '100%', backgroundColor: '#424242', borderRadius: 1 }}>
            <Typography variant="h6" sx={{ textAlign: 'center', mb: 0.5 }}>
                Trip Travel Time
            </Typography>
            <Typography variant="subtitle2" sx={{ textAlign: 'center', mb: 0.5 }}>
                {routeName || `${routeId} - ${headsign}`}
            </Typography>
            <Typography variant="caption" sx={{ display: 'block', color: '#aaa', textAlign: 'center', mb: 1 }}>
                {formatDateDisplay(startDate)} - {formatDateDisplay(endDate)}
            </Typography>
            <Box sx={{ width: '100%', height: '410px' }}>
                {loading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <CircularProgress />
                    </Box>
                ) : error ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <Typography color="error">{error}</Typography>
                    </Box>
                ) : series.length > 0 ? (
                    <ApexCharts options={options} series={series} type="area" height="100%" width="100%" />
                ) : (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <Typography>No data available for the selected time range</Typography>
                    </Box>
                )}
            </Box>
        </Box>
    );
};

export default TripTravelTimeReport;

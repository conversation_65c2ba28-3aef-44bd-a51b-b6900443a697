"use strict";(self.webpackChunkmantis_free_react_admin_template=self.webpackChunkmantis_free_react_admin_template||[]).push([[356],{14356:(e,t,r)=>{r.r(t),r.d(t,{default:()=>M});var o=r(4139),s=r(93230),n=r(9950),a=r(33344),l=r(37598),c=r(96583),i=r(58168),u=r(98587),p=r(72004),d=r(88465),h=r(87233),m=r(59254),y=r(18463),f=r(23025),x=r(5536),A=r(44414);const j=["className","id"],g=(0,m.Ay)(h.A,{name:"MuiDialogTitle",slot:"Root",overridesResolver:(e,t)=>t.root})({padding:"16px 24px",flex:"0 0 auto"}),v=n.forwardRef((function(e,t){const r=(0,y.b)({props:e,name:"MuiDialogTitle"}),{className:o,id:s}=r,a=(0,u.A)(r,j),l=r,c=(e=>{const{classes:t}=e;return(0,d.A)({root:["root"]},f.t,t)})(l),{titleId:h=s}=n.useContext(x.A);return(0,A.jsx)(g,(0,i.A)({component:"h2",className:(0,p.A)(c.root,o),ownerState:l,ref:t,variant:"h6",id:null!=s?s:h},a))}));var C=r(28170),S=r(19608),T=r(1763),b=r(423);function k(e){return(0,b.Ay)("MuiDialogContentText",e)}(0,T.A)("MuiDialogContentText",["root"]);const w=["children","className"],E=(0,m.Ay)(h.A,{shouldForwardProp:e=>(0,S.A)(e)||"classes"===e,name:"MuiDialogContentText",slot:"Root",overridesResolver:(e,t)=>t.root})({}),D=n.forwardRef((function(e,t){const r=(0,y.b)({props:e,name:"MuiDialogContentText"}),{className:o}=r,s=(0,u.A)(r,w),n=(e=>{const{classes:t}=e,r=(0,d.A)({root:["root"]},k,t);return(0,i.A)({},t,r)})(s);return(0,A.jsx)(E,(0,i.A)({component:"p",variant:"body1",color:"text.secondary",ref:t,ownerState:s,className:(0,p.A)(n.root,o)},r,{classes:n}))}));var N=r(79739),P=r(41225);const $=()=>{const[e,t]=(0,n.useState)([]),[r,o]=(0,n.useState)(!1),s=(0,P.c4)();(0,n.useEffect)((()=>{fetch("http://*************:8085/systemProperties/all",{method:"GET",headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"}}).then((e=>e.json())).then((e=>{t(e)})).catch((e=>{console.error("Error fetching system properties",e)}))}),[s]);const i=[{name:"name",label:"Name"},{name:"value",label:"Value",options:{customBodyRender:(e,t,r)=>"runDbCheckCron"===t.rowData[0]?e:(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)("input",{type:"text",value:e,onChange:e=>r(e.target.value)}),(0,A.jsx)(l.A,{onClick:()=>{return e={name:t.rowData[0],value:t.rowData[1]},void fetch("http://*************:8085/systemProperties/update",{method:"PUT",headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"},body:JSON.stringify(e)}).then((e=>{e.ok?(console.log("Property updated successfully"),o(!0)):console.error("Error updating property",e.statusText)})).catch((e=>{console.error("Error updating property",e)}));var e},children:"Update"})]})}},{name:"description",label:"Description"}];return(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)(a.Ay,{title:"System Properties",data:e,columns:i,options:{selectableRows:!1,responsive:"standard"}}),(0,A.jsxs)(c.A,{open:r,onClose:()=>o(!1),children:[(0,A.jsx)(v,{children:"Update Successful"}),(0,A.jsx)(C.A,{children:(0,A.jsx)(D,{children:"The system property was updated successfully."})}),(0,A.jsx)(N.A,{children:(0,A.jsx)(l.A,{onClick:()=>o(!1),color:"primary",children:"Okay"})})]})]})};var R=r(52983);const _=()=>{const[e,t]=(0,n.useState)("interval"),[r,o]=(0,n.useState)(""),[s,a]=(0,n.useState)(""),[c,i]=(0,n.useState)(""),[u,p]=(0,n.useState)(!0),d=(0,P.c4)();(0,n.useEffect)((()=>{fetch("http://*************:8085/systemProperties/getByName?name=runGtfsCron",{method:"GET",headers:{Authorization:`Bearer ${d}`,"Content-Type":"application/json"}}).then((e=>e.json())).then((e=>{i(e.value),p(!1)})).catch((e=>{console.error("Error fetching current cron:",e)}))}),[u]);return(0,A.jsxs)("div",{children:[(0,A.jsx)(R.A,{label:"Select Time",type:"time",value:s,onChange:e=>a(e.target.value),InputLabelProps:{shrink:!0},inputProps:{step:300}}),(0,A.jsx)(l.A,{variant:"contained",color:"primary",onClick:()=>{let e="";const[t,r]=s.split(":");e=`0 ${r} ${t} * * ?`,fetch("http://*************:8085/job/rescheduleGtfs",{method:"POST",headers:{Authorization:`Bearer ${d}`,"Content-Type":"application/json"},body:JSON.stringify(e)}).then((e=>{e.ok?(console.log("Schedule updated successfully"),p(!0)):console.error("Error updating schedule",e.statusText)})).catch((e=>{console.error("Error updating schedule:",e)}))},children:"Apply"}),(0,A.jsxs)(h.A,{variant:"body1",style:{marginLeft:20},children:["Current Schedule: ",(e=>{if(!e)return"";const t=e.split(" ");if(t[1].includes("/")){return`Every ${t[1].split("/")[1]} minutes`}return`Everyday At ${t[2]}:${t[1]}`})(c)]})]})};var B=r(55515);const M=()=>(0,A.jsx)(o.Ay,{container:!0,spacing:3,children:(0,A.jsx)(o.Ay,{item:!0,xs:12,lg:12,children:(0,A.jsxs)(s.A,{spacing:3,children:[(0,A.jsx)(B.A,{title:"GTFS Feed Update Schedule",children:(0,A.jsx)(_,{})}),(0,A.jsx)($,{})]})})})}}]);
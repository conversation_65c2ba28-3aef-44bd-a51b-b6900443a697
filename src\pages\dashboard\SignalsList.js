import React, { useState, useEffect, useCallback, Fragment } from 'react';
import PropTypes from 'prop-types';
import { getAuthToken } from 'utils/auth';
import MUIDataTable from 'components/mui-datatable';
import { Button, Modal, Typography, IconButton, Checkbox } from '@mui/material';
import FileList from './FileList';
import GpsFixedIcon from '@mui/icons-material/GpsFixed';
import TrafficIcon from '@mui/icons-material/Traffic';
import DownloadIcon from '@mui/icons-material/Download';
import UploadIcon from '@mui/icons-material/Upload';
import SummarizeIcon from '@mui/icons-material/Summarize';
import Tooltip from '@mui/material/Tooltip';

function SignalsList({ onDeviceSelect, signals, onSignalSelectionChange, selectedSignalIds = [] }) {
    const [responsive, setResponsive] = useState('vertical');
    const [tableBodyHeight, setTableBodyHeight] = useState('auto');
    const [tableBodyMaxHeight, setTableBodyMaxHeight] = useState('100%');
    const [transitionTime, setTransitionTime] = useState(300);
    const [selectableRows, setSelectableRows] = useState('multiple');
    const [isRowSelected, setIsRowSelected] = useState(false);
    const [fileList, setFileList] = useState([]);
    const [fileListHeader, setFileListHeader] = useState('');
    const [selectedDeviceId, setSelectedDeviceId] = useState(null);
    const [selectedRowIndex, setSelectedRowIndex] = useState(null);
    const [initiallyLoaded, setInitiallyLoaded] = useState(false);

    // Convert selectedSignalIds to row indices for MUIDataTable
    const selectedRows = selectedSignalIds.map((id) => signals.findIndex((signal) => signal.id === id)).filter((index) => index !== -1);

    // Initial load effect - select all signals by default
    useEffect(() => {
        if (!initiallyLoaded && signals.length > 0 && selectedSignalIds.length === 0) {
            // Create an array of all signal IDs
            const allSignalIds = signals.map((signal) => signal.id);

            // Create an array of indices from 0 to signals.length-1
            const allIndices = Array.from({ length: signals.length }, (_, i) => i);

            // Notify parent component about the selection
            if (onSignalSelectionChange) {
                onSignalSelectionChange(allSignalIds, signals);
            }

            // Mark as initially loaded
            setInitiallyLoaded(true);
            console.log('Initially selected all signals:', allSignalIds.length);
        }
    }, [signals, initiallyLoaded, selectedSignalIds, onSignalSelectionChange]);

    // Handle selection changes
    const handleSelectionChange = (currentRowsSelected, allRowsSelected) => {
        console.log('Selection changed:', currentRowsSelected, allRowsSelected);

        const selectedIndices = allRowsSelected.map((row) => row.dataIndex);
        const selectedSignals = selectedIndices.map((index) => signals[index]);

        // Extract just the IDs for passing to parent
        const selectedIds = selectedSignals.map((signal) => signal.id);

        console.log('Selected signal IDs:', selectedIds);

        // Call the parent's handler with the selected signals
        if (onSignalSelectionChange) {
            onSignalSelectionChange(selectedIds, selectedSignals);
        }
    };

    const getTableMaxHeight = () => {
        const screenWidth = window.innerWidth;
        if (screenWidth > 1920) {
            return '86vh'; // For smaller screens
        } else if (screenWidth <= 1920 && screenWidth > 1024) {
            return '84.5vh'; // For larger screens
        } else {
            return '70vh'; // For larger screens
        }
    };

    useEffect(() => {
        const handleResize = () => {
            setTableBodyMaxHeight(getTableMaxHeight());
        };
        window.addEventListener('resize', handleResize);
        // Cleanup the event listener
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    const handleOpen = () => {
        if (selectedRowIndex !== null) {
            const selectedDevice = signals[selectedRowIndex];
            setSelectedDeviceId(selectedDevice.id);
            setOpen(true);
        }
    };

    const handleClose = () => setOpen(false);

    const handleDbListModalOpen = () => setDbListModalOpen(true);
    const handleDbListModalClose = () => setDbListModalOpen(false);

    const handleUploadModalOpen = () => {
        if (selectedRowIndex !== null) {
            const selectedDevice = signals[selectedRowIndex];
            setSelectedDeviceId(selectedDevice.id); // Set the device ID when opening the modal
            setUploadModalOpen(true);
        }
    };
    const handleUploadModalClose = () => setUploadModalOpen(false);

    const cellPadding = '2px'; // Reduced from 4px
    const columns = [
        /*  {
            name: 'id',
            label: 'Signal ID',
            options: {
                filter: true,
                sort: true
            }
        }, */
        {
            name: 'name',
            label: 'Name',
            options: {
                filter: true,
                sort: true,
                setCellProps: () => ({
                    style: {
                        padding: '2px 0px 2px 10px', // Reduced padding
                        fontSize: '0.875rem' // Smaller font size
                    }
                })
            }
        },
        {
            name: 'primaryRoad',
            label: 'Main St.',
            options: {
                filter: true,
                sort: true,
                setCellProps: () => ({
                    style: {
                        padding: cellPadding,
                        fontSize: '0.875rem' // Smaller font size
                    }
                })
            }
        },
        {
            name: 'crossRoad',
            label: 'Side St.',
            options: {
                filter: true,
                sort: true,
                setCellProps: () => ({
                    style: {
                        padding: cellPadding,
                        fontSize: '0.875rem' // Smaller font size
                    }
                })
            }
        } /*,
        {
            name: 'hasDb',
            label: 'DB Avail.',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value, tableMeta, updateValue) => {
                    // value is the value of hasDb field
                    // you can return a JSX component based on the value
                    return value === 1 ? 'Y' : 'N';
                }
            }
        } */
    ];

    customBodyRender: (value, tableMeta, updateValue) => {
        const dotStyle = {
            height: '10px',
            width: '10px',
            borderRadius: '50%',
            display: 'inline-block'
            //marginLeft: '10px' // To center the dot, adjust as needed
        };

        return <div style={{ ...dotStyle, backgroundColor: value === 1 ? 'green' : 'red' }} />;
    };

    const staticOptions = {
        filter: true,
        search: true,
        download: false,
        print: false,
        //search: true,
        viewColumns: false,
        searchOpen: false,
        searchAlwaysOpen: false,
        filterType: 'dropdown',
        responsive,
        fixedHeader: true,
        tableBodyMaxHeight: '100%',
        scrollMaxHeight: 'auto',
        scroll: 'auto',
        tableBodyHeight: getTableMaxHeight(),
        pagination: false,
        draggableColumns: {
            enabled: true,
            transitionTime
        },
        responsive: 'standard',
        selectableRows: selectableRows,
        setRowProps: (row, dataIndex) => {
            // Check if the row index matches the selected row index
            if (dataIndex === selectedRowIndex) {
                return {
                    style: {
                        backgroundColor: 'gray',
                        height: '30px' // Reduced row height
                    }
                };
            }
            return {
                style: {
                    height: '30px' // Reduced row height for all rows
                }
            };
        },
        setTableProps: () => ({
            size: 'small', // Use small size for the table
            padding: 'none' // Remove default padding
        }),
        onRowClick: (rowData, rowMeta) => {
            setIsRowSelected(true);
            setSelectedRowIndex(rowMeta.dataIndex);
        },
        customToolbar: () => {
            return (
                <div style={{ display: 'inline-flex' }}>
                    <Tooltip title="Locate Device" arrow>
                        <span>
                            <IconButton
                                disabled={!isRowSelected}
                                onClick={() => {
                                    const device = signals[selectedRowIndex];
                                    onDeviceSelect(device);
                                }}
                            >
                                <GpsFixedIcon />
                            </IconButton>
                        </span>
                    </Tooltip>
                </div>
            );
        }
    };

    const options = {
        ...staticOptions,
        tableBodyMaxHeight: tableBodyMaxHeight,
        selectableRows: 'multiple', // Enable multiple selection
        rowsSelected: selectedRows, // Set initially selected rows
        onRowSelectionChange: handleSelectionChange, // Handle selection changes
        selectToolbarPlacement: 'none' // Hide the selection toolbar
    };

    const handleConfirmClose = () => {
        setOpen(false); // Open the confirmation dialog
    };

    const fetchFilesForDevice = async (deviceId) => {
        const accessToken = getAuthToken();
        if (accessToken) {
            try {
                const response = await fetch(`${process.env.REACT_APP_API_ENDPOINT}/signals/getlistofdbfiles/${deviceId}`, {
                    method: 'GET',
                    headers: {
                        'Content-type': 'application/json;charset=UTF-8',
                        Authorization: 'Bearer ' + accessToken
                    }
                });
                if (!response.ok) {
                    setFileListHeader('No Files are Available');
                    //throw new Error('No database was for found for this device');
                } else {
                    setFileListHeader('Available Files');
                    const data = await response.json();
                    setFileList(data);
                }
            } catch (error) {
                console.error(error);
            }
        }
    };

    const downloadFile = async (deviceId, timestamp, fileName) => {
        const url = `${process.env.REACT_APP_API_ENDPOINT}/signals/getdatabasefile/${deviceId}/${formatTimestamp(timestamp)}`;
        const token = getAuthToken();

        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const blob = await response.blob();
            const objectURL = window.URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = objectURL;
            a.download = fileName + '.db'; // You can set a filename if the server doesn't specify one
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        } catch (error) {
            console.error('There was an error downloading the file:', error);
        }
    };
    return (
        <Fragment>
            <MUIDataTable title={'Intersections'} data={signals} columns={columns} options={options} />
        </Fragment>
    );
}

SignalsList.propTypes = {
    onDeviceSelect: PropTypes.func.isRequired,
    signals: PropTypes.array.isRequired,
    onSignalSelectionChange: PropTypes.func,
    selectedSignalIds: PropTypes.array
};

function formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

export default SignalsList;

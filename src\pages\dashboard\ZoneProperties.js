import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Box, Typography, TextField, Button, Select, MenuItem, Grid, IconButton } from '@mui/material';
import ApiService from 'utils/ApiService';
import { geojsonToWKT } from '@terraformer/wkt';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import DeleteIcon from '@mui/icons-material/Delete';
import CancelIcon from '@mui/icons-material/Cancel';
import ScheduleMatrix from 'pages/dashboard/ScheduleMatrix';

const ZoneProperties = ({ zone, onZoneUpdate, onEditGeometry, updatedGeometry, onZoneDelete }) => {
    //const [editableZone, setEditableZone] = useState(zone);
    const [zoneSignals, setZoneSignals] = useState([]);
    const [signals, setSignals] = useState([]);
    const [routes, setRoutes] = useState([]);
    const [routeHeadsigns, setRouteHeadsigns] = useState([]);
    const [selectedSignalId, setSelectedSignalId] = useState(null);
    const [signalSchedules, setSignalSchedules] = useState({});

    const [scheduleMatrix, setScheduleMatrix] = useState(() => createEmptyScheduleMatrix());
    const [zoneSignalSchedules, setZoneSignalSchedules] = useState({});
    // const [editableZone, setEditableZone] = useState(
    //     () =>
    //         zone && zone.id
    //             ? { ...zone } // Existing zone
    //             : { id: null, name: '', type: 1, geometry: null } // New zone
    // );
    const [editableZone, setEditableZone] = useState(zone);
    const [isEditable, setIsEditable] = useState(!zone.id); // If no id, we are in create mode and fields are editable
    //const [isAddNewZone, setIsAddNewZone] = useState(false);
    const canSave = editableZone.name.trim() !== '' && editableZone.geometry !== null; // Determine if save should be active: only if name and geometry are defined

    // Fetch signals and routes when the component mounts
    useEffect(() => {
        fetchTrafficSignals();
        fetchGtfsRoutes();
        fetchTransitRoutesWithHeadsigns();
    }, []);

    useEffect(() => {
        if (updatedGeometry) {
            setEditableZone((prev) => ({
                ...prev,
                geometry: updatedGeometry
            }));
        }
    }, [updatedGeometry]);

    // Fetch zone signals when the zone changes
    useEffect(() => {
        console.log('Zone ID is ' + zone.id);
        if (zone && zone.id) {
            // Existing zone
            setEditableZone(zone);
            fetchZoneSignals(zone.id);
        } else {
            // New zone scenario
            setEditableZone({
                id: null,
                name: '',
                type: 1,
                geometry: null
            });
            // Clear any existing zone signals for a fresh start
            setZoneSignals([]);
            setSelectedSignalId(null);
            setIsEditable(true);
            //setIsAddNewZone(true);
            onEditGeometry(zone);
            //handleEditGeometryClick();
        }
    }, [zone]);

    const handleEditGeometryClick = () => {
        setIsEditable(true);
        if (onEditGeometry) {
            onEditGeometry(editableZone);
        }
    };

    const handleDeleteZone = async () => {
        if (!editableZone.id) return; // If no ID, can't delete
        const response = await ApiService.deleteZone(editableZone.id);
        if (response.success) {
            // Notify parent that zone is deleted
            onZoneDelete();
        } else {
            console.error('Failed to delete zone:', response.errorMessage);
        }
    };

    const handleCancelEdit = () => {
        setIsEditable(false);
        setEditableZone(zone); // Revert to original zone details
        onZoneUpdate();
    };

    const fetchZoneSignals = async (zoneId) => {
        try {
            const response = await ApiService.getAllZoneSignalsForZone(zoneId);
            if (response.success) {
                setZoneSignals(response.zoneSignals);
                setSelectedSignalId(response.zoneSignals.length ? response.zoneSignals[0].id : null);
                // Initialize schedule state directly with the fetched signals
                initializeScheduleState(response.zoneSignals);
            } else {
                console.error('Failed to fetch zone signals:', response.errorMessage);
            }
        } catch (error) {
            console.error('Error fetching zone signals:', error);
        }
    };

    const fetchTrafficSignals = async () => {
        try {
            const response = await ApiService.getTrafficSignals();
            if (response.success) {
                setSignals(response.devices);
            } else {
                console.error('Failed to fetch traffic signals:', response.errorMessage);
            }
        } catch (error) {
            console.error('Error fetching traffic signals:', error);
        }
    };

    const fetchGtfsRoutes = async () => {
        try {
            const response = await ApiService.getGtfsRoutes();
            if (response.success) {
                setRoutes(response.routes);
            } else {
                console.error('Failed to fetch GTFS routes:', response.errorMessage);
            }
        } catch (error) {
            console.error('Error fetching GTFS routes:', error);
        }
    };

    const fetchTransitRoutesWithHeadsigns = async () => {
        try {
            const response = await ApiService.getTransitRoutesWithHeadsigns();
            if (response.success) {
                setRouteHeadsigns(response.routes);
            } else {
                console.error('Failed to fetch transit routes with headsigns:', response.errorMessage);
            }
        } catch (error) {
            console.error('Error fetching transit routes with headsigns:', error);
        }
    };

    const handleZoneChange = (e) => {
        const { name, value } = e.target;
        setEditableZone((prev) => ({ ...prev, [name]: value }));
    };

    const handleSignalIdChange = (event) => {
        const signalId = event.target.value;
        console.log('Selected Signal ID:', signalId);
        setSelectedSignalId(signalId);
        updateScheduleMatrixForSignal(signalId);
    };

    const handleActiveStateChange = (value) => {
        const isActive = value === 'Yes';
        setZoneSignals((prevSignals) =>
            prevSignals.map((signal) => (signal.id === selectedSignalId ? { ...signal, active: isActive } : signal))
        );
    };

    const updateScheduleMatrixForSignal = (signalId) => {
        // Find the selected signal based on the signalId
        const selectedSignal = zoneSignals.find((signal) => signal.id === signalId);

        if (selectedSignal && selectedSignal.schedules) {
            // Format the schedules for this specific signal
            const formattedMatrix = formatScheduleData(selectedSignal.schedules);

            // Update the schedules for this specific signal
            setZoneSignalSchedules((prev) => ({
                ...prev,
                [signalId]: formattedMatrix
            }));
        } else {
            // If no schedules exist, create an empty matrix
            setZoneSignalSchedules((prev) => ({
                ...prev,
                [signalId]: createEmptyScheduleMatrix()
            }));
        }
    };

    const formatScheduleData = (schedules) => {
        // Create a new matrix for the week, initializing all slots as false (not scheduled)
        const newMatrix = Array.from({ length: 7 }, () => Array(96).fill(false));
        schedules.forEach((schedule) => {
            const dayIndex = schedule.dayOfWeek - 1; // Convert to 0-based index if necessary
            const startIndex = timeToIndex(schedule.startTime);
            const endIndex = timeToIndex(schedule.endTime) - 1; // Subtract 1 to align end time to the correct slot
            for (let i = startIndex; i <= endIndex; i++) {
                newMatrix[dayIndex][i] = true; // Set the schedule slots to true
            }
        });
        return newMatrix;
    };

    const handleScheduleUpdate = (signalId, updatedMatrix) => {
        setSignalSchedules((prev) => ({
            ...prev,
            [signalId]: updatedMatrix
        }));
    };

    const initializeScheduleState = (zoneSignals) => {
        const initialSchedules = {};
        if (zoneSignals && Array.isArray(zoneSignals)) {
            zoneSignals.forEach((signal) => {
                if (signal && signal.id) {
                    // Ensure each signal has an ID
                    initialSchedules[signal.id] = signal.schedules
                        ? formatScheduleDataToMatrix(signal.schedules)
                        : createEmptyScheduleMatrix();
                }
            });
        }
        setSignalSchedules(initialSchedules);
    };

    const formatScheduleDataToMatrix = (schedules) => {
        const matrix = createEmptyScheduleMatrix();
        schedules.forEach((schedule) => {
            const dayIndex = schedule.dayOfWeek - 1; // Convert to 0-based index
            const startIndex = timeToIndex(schedule.startTime);
            const endIndex = timeToIndex(schedule.endTime) - 1; // Subtract 1 to align end time

            for (let i = startIndex; i <= endIndex; i++) {
                matrix[dayIndex][i] = true; // Set scheduled times to true
            }
        });
        return matrix;
    };

    const timeToIndex = (time) => {
        // Assumes time is in 'HH:MM:SS' format
        const [hours, minutes] = time.split(':').map(Number);
        return hours * 4 + Math.floor(minutes / 15); // Calculate the index for 15-minute intervals
    };

    const getSelectedSignalSchedules = (signalId) => {
        const signal = zoneSignals.find((signal) => signal.id === signalId);
        return signal ? signal.schedules : [];
    };

    const handleSignalChange = (e) => {
        const { value } = e.target;
        setZoneSignals((prevSignals) =>
            prevSignals.map((signal) => (signal.id === selectedSignalId ? { ...signal, signalId: value } : signal))
        );
    };

    const handleRouteChange = (e) => {
        const { value } = e.target;
        setZoneSignals((prevSignals) =>
            prevSignals.map((signal) => (signal.id === selectedSignalId ? { ...signal, routeId: value, tripHeadsign: '' } : signal))
        );
    };

    const handleTripHeadsignChange = (e) => {
        const { value } = e.target;
        setZoneSignals((prevSignals) =>
            prevSignals.map((signal) => (signal.id === selectedSignalId ? { ...signal, tripHeadsign: value } : signal))
        );
    };

    const handleZoneSignalFieldChange = (e) => {
        const { name, value } = e.target;
        setZoneSignals((prevSignals) =>
            prevSignals.map((signal) => (signal.id === selectedSignalId ? { ...signal, [name]: value } : signal))
        );
    };

    const handleScheduleSlotClick = (day, timeSlot) => {
        setScheduleMatrix((prevMatrix) => {
            const updatedMatrix = [...prevMatrix];
            updatedMatrix[day][timeSlot] = !updatedMatrix[day][timeSlot];
            return updatedMatrix;
        });
    };

    const handleSaveZone = async () => {
        const geometryWKT = editableZone.geometry ? geojsonToWKT(editableZone.geometry) : null;
        const zoneToSave = {
            ...editableZone,
            geometry: geometryWKT
        };

        // Save the Zone first
        const response = await ApiService.saveZone(zoneToSave);
        //onsole.log('SaveZone response:', response);
        if (response.success) {
            // Use the returned ID instead of zoneToSave.id
            const savedZoneId = response.message.id;

            // Save each ZoneSignal using the newly returned zone ID
            // Loop through each signal and save its schedule
            const saveSignalsPromises = zoneSignals.map(async (zoneSignal) => {
                const schedules = matrixToScheduleData(signalSchedules[zoneSignal.id] || createEmptyScheduleMatrix(), zoneSignal.id);
                const zoneSignalWithSchedules = {
                    ...zoneSignal,
                    zone: { id: savedZoneId },
                    //zoneId: savedZoneId,
                    schedules: schedules
                };
                return await ApiService.saveZoneSignal(zoneSignalWithSchedules);
            });

            try {
                // Wait for all signal schedules to be saved
                const results = await Promise.all(saveSignalsPromises);
                results.forEach((result) => {
                    if (!result.success) {
                        throw new Error(`Failed to save zone signal: ${result.errorMessage}`);
                    }
                });
                // After saving all zone signals successfully, re-fetch them to ensure IDs are updated
                await fetchZoneSignals(savedZoneId);

                setIsEditable(false); // Make fields read-only again
                // Update the editableZone with the saved zone ID so it's consistent
                setEditableZone((prevZone) => ({ ...prevZone, id: savedZoneId }));

                // Notify parent to refresh data
                onZoneUpdate();
            } catch (error) {
                console.error('Error saving zone signals:', error);
            }

            // for (const zoneSignal of zoneSignals) {
            //     const signalSchedules = zoneSignalSchedules[zoneSignal.id] || createEmptyScheduleMatrix();

            //     const schedules = [];
            //     signalSchedules.forEach((day, dayIndex) => {
            //         let startTime = null;
            //         day.forEach((slot, timeIndex) => {
            //             if (slot && startTime === null) {
            //                 startTime = formatTimeSlot(timeIndex);
            //             } else if (!slot && startTime !== null) {
            //                 schedules.push({
            //                     dayOfWeek: dayIndex + 1,
            //                     startTime: startTime,
            //                     endTime: formatTimeSlot(timeIndex)
            //                 });
            //                 startTime = null;
            //             }
            //         });

            //         // Handle case where schedule continues until end of day
            //         if (startTime !== null) {
            //             schedules.push({
            //                 dayOfWeek: dayIndex + 1,
            //                 startTime: startTime,
            //                 endTime: '24:00:00'
            //             });
            //         }
            //     });

            //     const zoneSignalWithZone = {
            //         ...zoneSignal,
            //         zone: { id: savedZoneId },
            //         schedules
            //     };

            //     const zoneSignalResponse = await ApiService.saveZoneSignal(zoneSignalWithZone);
            //     if (!zoneSignalResponse.success) {
            //         console.error(`Failed to save zone signal ${zoneSignal.id}:`, zoneSignalResponse.errorMessage);
            //         return; // Exit if any zone signal fails to save
            //     }
            // }
        } else {
            console.error('Failed to save zone:', response.errorMessage);
        }
    };

    const handleAddZoneSignal = () => {
        // Create a new zoneSignal with default values
        const newZoneSignal = {
            id: -1,
            signalId: signals.length > 0 ? signals[0].id : '',
            routeId: routes.length > 0 ? routes[0].routeId : '',
            tripHeadsign: routeHeadsigns.length > 0 ? routeHeadsigns[0].tripHeadsign : '',
            eta: '',
            priorityInput: '',
            routePriority: '',
            latenessThreshold: '',
            headingRange: ''
        };

        setZoneSignals((prev) => [...prev, newZoneSignal]);
        setSelectedSignalId(newZoneSignal.id);
        setZoneSignalSchedules((prev) => ({
            ...prev,
            [newZoneSignal.id]: createEmptyScheduleMatrix()
        }));
    };

    const renderZoneSignalForm = () => {
        const selectedSignal = zoneSignals.find((signal) => signal.id === selectedSignalId);
        if (!selectedSignal) return null;

        const availableHeadsigns = routeHeadsigns.filter((route) => route.routeId === selectedSignal.routeId);

        return (
            <Box>
                <Grid container spacing={1} sx={{ paddingTop: '20px' }}>
                    <Grid item xs={12} sm={6} md={2}>
                        <Select
                            label="Active State"
                            value={selectedSignal.active ? 'Yes' : 'No'}
                            onChange={(e) => handleActiveStateChange(e.target.value)}
                            fullWidth
                            disabled={!isEditable}
                        >
                            <MenuItem value="Yes">Yes</MenuItem>
                            <MenuItem value="No">No</MenuItem>
                        </Select>
                    </Grid>
                    <Grid item xs={12} sm={6} md={2}>
                        <Select
                            label="Route ID"
                            value={selectedSignal.routeId || ''}
                            onChange={handleRouteChange}
                            fullWidth
                            disabled={!isEditable}
                        >
                            {routes.map((route) => (
                                <MenuItem key={route.routeId} value={route.routeId}>
                                    {route.routeShortName}
                                </MenuItem>
                            ))}
                        </Select>
                    </Grid>

                    <Grid item xs={12} sm={6} md={4}>
                        <Select
                            label="Trip Headsign"
                            value={selectedSignal.tripHeadsign || ''}
                            onChange={handleTripHeadsignChange}
                            fullWidth
                            disabled={!isEditable}
                        >
                            {availableHeadsigns.map((route, index) => (
                                <MenuItem key={index} value={route.tripHeadsign}>
                                    {route.tripHeadsign}
                                </MenuItem>
                            ))}
                        </Select>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                        <Select
                            label="Signal ID"
                            value={selectedSignal.signalId || ''}
                            onChange={handleSignalChange}
                            fullWidth
                            disabled={!isEditable}
                        >
                            {signals.map((signal) => (
                                <MenuItem key={signal.id} value={signal.id}>
                                    {signal.name}
                                </MenuItem>
                            ))}
                        </Select>
                    </Grid>
                </Grid>
                <Grid container spacing={1} sx={{ paddingTop: '20px' }}>
                    <Grid item xs={12} sm={6} md={1.5}>
                        <TextField
                            label="ETA"
                            name="eta"
                            type="number"
                            value={selectedSignal.eta || ''}
                            onChange={handleZoneSignalFieldChange}
                            inputProps={{ min: 1, max: 255 }}
                            fullWidth
                            disabled={!isEditable}
                        />
                    </Grid>

                    <Grid item xs={12} sm={6} md={2}>
                        <TextField
                            label="Priority Input"
                            name="priorityInput"
                            type="number"
                            value={selectedSignal.priorityInput || ''}
                            onChange={handleZoneSignalFieldChange}
                            inputProps={{ min: 1, max: 255 }}
                            fullWidth
                            disabled={!isEditable}
                        />
                    </Grid>

                    <Grid item xs={12} sm={6} md={2}>
                        <TextField
                            label="Route Priority"
                            name="routePriority"
                            type="number"
                            value={selectedSignal.routePriority || ''}
                            onChange={handleZoneSignalFieldChange}
                            inputProps={{ min: 1, max: 255 }}
                            fullWidth
                            disabled={!isEditable}
                        />
                    </Grid>

                    <Grid item xs={12} sm={6} md={2}>
                        <TextField
                            label="Direction Priority"
                            name="headsignPriority"
                            type="number"
                            value={selectedSignal.routePriority || ''}
                            onChange={handleZoneSignalFieldChange}
                            inputProps={{ min: 1, max: 255 }}
                            fullWidth
                            disabled={!isEditable}
                        />
                    </Grid>

                    <Grid item xs={12} sm={6} md={2}>
                        <TextField
                            label="Lateness Threshold"
                            name="latenessThreshold"
                            type="number"
                            value={selectedSignal.latenessThreshold || ''}
                            onChange={handleZoneSignalFieldChange}
                            inputProps={{ min: 1, max: 60 }}
                            fullWidth
                            disabled={!isEditable}
                        />
                    </Grid>

                    {editableZone.type === 2 && (
                        <>
                            <Grid item xs={12} sm={6} md={4}>
                                <Select
                                    label="Signal ID"
                                    value={selectedSignal.signalId || ''}
                                    onChange={handleSignalChange}
                                    fullWidth
                                    disabled={!isEditable}
                                >
                                    {signals.map((signal) => (
                                        <MenuItem key={signal.id} value={signal.id}>
                                            {signal.name}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </Grid>

                            <Grid item xs={12} sm={6} md={4}>
                                <TextField
                                    label="Heading Range"
                                    name="headingRange"
                                    value={selectedSignal.headingRange || ''}
                                    onChange={handleZoneSignalFieldChange}
                                    fullWidth
                                    disabled={!isEditable}
                                    inputProps={{ min: 1, max: 60 }}
                                />
                            </Grid>

                            <Grid item xs={12} sm={6} md={4}>
                                <TextField
                                    label="Priority Input"
                                    name="priorityInput"
                                    type="number"
                                    value={selectedSignal.priorityInput || ''}
                                    onChange={handleZoneSignalFieldChange}
                                    inputProps={{ min: 1, max: 255 }}
                                    fullWidth
                                />
                            </Grid>
                        </>
                    )}
                </Grid>
            </Box>
        );
    };

    return (
        <Box display="flex" flexDirection="row" justifyContent="space-between" sx={{ overflowY: 'auto' }}>
            {/* Zone Details Section */}
            <Box flex={1} padding={2}>
                <Box display="flex" flexDirection="row" justifyContent="space-between">
                    <TextField
                        label="Zone Name"
                        name="name"
                        value={editableZone.name || ''}
                        onChange={handleZoneChange}
                        fullWidth
                        InputProps={{ readOnly: !isEditable }}
                    />
                    {isEditable ? (
                        <>
                            <IconButton color="primary" onClick={handleSaveZone} disabled={!canSave}>
                                <SaveIcon />
                            </IconButton>
                            <IconButton color="default" onClick={handleCancelEdit}>
                                <CancelIcon />
                            </IconButton>
                        </>
                    ) : (
                        <IconButton color="primary" onClick={handleEditGeometryClick}>
                            <EditIcon />
                        </IconButton>
                    )}
                    <IconButton color="secondary" onClick={handleDeleteZone} disabled={!isEditable || !zone.id}>
                        <DeleteIcon />
                    </IconButton>
                </Box>

                <Typography variant="h6" sx={{ marginTop: 2 }}>
                    Zone Assignments
                </Typography>
                <Box display="flex" alignItems="center" gap={1}>
                    <Select value={selectedSignalId || ''} onChange={handleSignalIdChange} fullWidth>
                        {zoneSignals.length === 0 && <MenuItem value="">No Signals</MenuItem>}
                        {zoneSignals.map((signal) => (
                            <MenuItem key={signal.id} value={signal.id}>
                                {signal.tripHeadsign || `Signal ${signal.id}`}
                            </MenuItem>
                        ))}
                    </Select>
                    <IconButton onClick={handleAddZoneSignal} color="primary" title="Add New Zone Signal" disabled={!isEditable}>
                        <AddIcon />
                    </IconButton>
                </Box>

                {renderZoneSignalForm()}
            </Box>

            {/* Schedules Section */}
            <Box flex={0.5} padding={2} sx={{ border: 'dotted' }}>
                <Typography variant="h6" sx={{ margin: '5px 0' }}>
                    Schedules
                </Typography>
                <ScheduleMatrix
                    initialScheduleMatrix={signalSchedules[selectedSignalId] || createEmptyScheduleMatrix()}
                    handleScheduleUpdate={(updatedMatrix) => handleScheduleUpdate(selectedSignalId, updatedMatrix)}
                    editable={isEditable}
                />
            </Box>
        </Box>
    );
};

const createEmptyScheduleMatrix = () => {
    return Array.from({ length: 7 }, () => Array(96).fill(false));
};

// Utility function to format the time slots from index
const formatTimeSlot = (index) => {
    const hours = String(Math.floor(index / 4)).padStart(2, '0');
    const minutes = String((index % 4) * 15).padStart(2, '0');
    return `${hours}:${minutes}:00`;
};

// Converts schedule matrix to API schedule format
const matrixToScheduleData = (matrix, signalId) => {
    const schedules = [];
    matrix.forEach((daySlots, dayIndex) => {
        let isScheduled = false;
        let startTimeIndex = null;
        daySlots.forEach((slot, index) => {
            if (slot && !isScheduled) {
                // Schedule starts
                isScheduled = true;
                startTimeIndex = index;
            } else if (!slot && isScheduled) {
                // Schedule ends
                schedules.push(createSchedule(dayIndex + 1, startTimeIndex, index, signalId));
                isScheduled = false;
            }
        });
        // Check if the last slot was scheduled and not closed
        if (isScheduled) {
            schedules.push(createSchedule(dayIndex + 1, startTimeIndex, daySlots.length, signalId));
        }
    });
    return schedules;
};

// Helper to create a schedule entry
const createSchedule = (dayOfWeek, startSlot, endSlot, signalId) => {
    return {
        signalId: signalId,
        dayOfWeek: dayOfWeek,
        startTime: formatTimeSlot(startSlot),
        endTime: formatTimeSlot(endSlot)
    };
};

ZoneProperties.propTypes = {
    zone: PropTypes.object,
    onZoneUpdate: PropTypes.func.isRequired,
    onEditGeometry: PropTypes.func.isRequired,
    onGeometryChange: PropTypes.func.isRequired,
    updatedGeometry: PropTypes.object,
    onZoneDelete: PropTypes.func.isRequired
};

export default ZoneProperties;

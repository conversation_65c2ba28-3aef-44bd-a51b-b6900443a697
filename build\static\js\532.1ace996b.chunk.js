"use strict";(self.webpackChunkmantis_free_react_admin_template=self.webpackChunkmantis_free_react_admin_template||[]).push([[532],{30532:(t,e,n)=>{n.r(e),n.d(e,{default:()=>l});var i=n(59254),r=n(88341),a=n(55515),s=n(44414);const o=(0,i.Ay)("iframe")((()=>({height:"calc(100vh - 210px)",border:"none"}))),l=()=>(0,s.jsx)(r.A,{children:(0,s.jsx)(a.A,{title:"Ant Icons",children:(0,s.jsx)(o,{title:"Ant Icon",width:"100%",src:"https://ant.design/components/icon/"})})})},88341:(t,e,n)=>{n.d(e,{A:()=>F});var i=n(9950),r=n(98587),a=n(58168),s=n(72004),o=n(88283),l=n(88465),h=n(97161),d=n(97497),c=n(59254),u=n(18463),m=n(1763),p=n(423);function f(t){return(0,p.Ay)("MuiSkeleton",t)}(0,m.A)("MuiSkeleton",["root","text","rectangular","rounded","circular","pulse","wave","withChildren","fitContent","heightAuto"]);var g=n(44414);const x=["animation","className","component","height","style","variant","width"];let w,v,b,A,y=t=>t;const k=(0,o.keyframes)(w||(w=y`
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.4;
  }

  100% {
    opacity: 1;
  }
`)),j=(0,o.keyframes)(v||(v=y`
  0% {
    transform: translateX(-100%);
  }

  50% {
    /* +0.5s of delay between each loop */
    transform: translateX(100%);
  }

  100% {
    transform: translateX(100%);
  }
`)),C=(0,c.Ay)("span",{name:"MuiSkeleton",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:n}=t;return[e.root,e[n.variant],!1!==n.animation&&e[n.animation],n.hasChildren&&e.withChildren,n.hasChildren&&!n.width&&e.fitContent,n.hasChildren&&!n.height&&e.heightAuto]}})((t=>{let{theme:e,ownerState:n}=t;const i=(0,h.l_)(e.shape.borderRadius)||"px",r=(0,h.db)(e.shape.borderRadius);return(0,a.A)({display:"block",backgroundColor:e.vars?e.vars.palette.Skeleton.bg:(0,d.X4)(e.palette.text.primary,"light"===e.palette.mode?.11:.13),height:"1.2em"},"text"===n.variant&&{marginTop:0,marginBottom:0,height:"auto",transformOrigin:"0 55%",transform:"scale(1, 0.60)",borderRadius:`${r}${i}/${Math.round(r/.6*10)/10}${i}`,"&:empty:before":{content:'"\\00a0"'}},"circular"===n.variant&&{borderRadius:"50%"},"rounded"===n.variant&&{borderRadius:(e.vars||e).shape.borderRadius},n.hasChildren&&{"& > *":{visibility:"hidden"}},n.hasChildren&&!n.width&&{maxWidth:"fit-content"},n.hasChildren&&!n.height&&{height:"auto"})}),(t=>{let{ownerState:e}=t;return"pulse"===e.animation&&(0,o.css)(b||(b=y`
      animation: ${0} 2s ease-in-out 0.5s infinite;
    `),k)}),(t=>{let{ownerState:e,theme:n}=t;return"wave"===e.animation&&(0,o.css)(A||(A=y`
      position: relative;
      overflow: hidden;

      /* Fix bug in Safari https://bugs.webkit.org/show_bug.cgi?id=68196 */
      -webkit-mask-image: -webkit-radial-gradient(white, black);

      &::after {
        animation: ${0} 2s linear 0.5s infinite;
        background: linear-gradient(
          90deg,
          transparent,
          ${0},
          transparent
        );
        content: '';
        position: absolute;
        transform: translateX(-100%); /* Avoid flash during server-side hydration */
        bottom: 0;
        left: 0;
        right: 0;
        top: 0;
      }
    `),j,(n.vars||n).palette.action.hover)})),S=i.forwardRef((function(t,e){const n=(0,u.b)({props:t,name:"MuiSkeleton"}),{animation:i="pulse",className:o,component:h="span",height:d,style:c,variant:m="text",width:p}=n,w=(0,r.A)(n,x),v=(0,a.A)({},n,{animation:i,component:h,variant:m,hasChildren:Boolean(w.children)}),b=(t=>{const{classes:e,variant:n,animation:i,hasChildren:r,width:a,height:s}=t,o={root:["root",n,i,r&&"withChildren",r&&!a&&"fitContent",r&&!s&&"heightAuto"]};return(0,l.A)(o,f,e)})(v);return(0,g.jsx)(C,(0,a.A)({as:h,ref:e,className:(0,s.A)(b.root,o),ownerState:v},w,{style:(0,a.A)({width:p,height:d},c)}))}));var $=n(93230),_=n(4139),R=n(55515);const F=t=>{let{children:e}=t;const[n,r]=(0,i.useState)(!0);(0,i.useEffect)((()=>{r(!1)}),[]);const a=(0,g.jsx)(R.A,{title:(0,g.jsx)(S,{sx:{width:{xs:120,md:180}}}),secondary:(0,g.jsx)(S,{animation:"wave",variant:"circular",width:24,height:24}),children:(0,g.jsxs)($.A,{spacing:1,children:[(0,g.jsx)(S,{}),(0,g.jsx)(S,{sx:{height:64},animation:"wave",variant:"rectangular"}),(0,g.jsx)(S,{}),(0,g.jsx)(S,{})]})});return(0,g.jsxs)(g.Fragment,{children:[n&&(0,g.jsxs)(_.Ay,{container:!0,spacing:3,children:[(0,g.jsx)(_.Ay,{item:!0,xs:12,md:6,children:a}),(0,g.jsx)(_.Ay,{item:!0,xs:12,md:6,children:a}),(0,g.jsx)(_.Ay,{item:!0,xs:12,md:6,children:a}),(0,g.jsx)(_.Ay,{item:!0,xs:12,md:6,children:a})]}),!n&&e]})}},97161:(t,e,n)=>{function i(t){return String(parseFloat(t)).length===String(t).length}function r(t){return String(t).match(/[\d.\-+]*\s*(.*)/)[1]||""}function a(t){return parseFloat(t)}function s(t){return(e,n)=>{const i=r(e);if(i===n)return e;let s=a(e);"px"!==i&&("em"===i||"rem"===i)&&(s=a(e)*a(t));let o=s;if("px"!==n)if("em"===n)o=s/a(t);else{if("rem"!==n)return e;o=s/a(t)}return parseFloat(o.toFixed(5))+n}}function o(t){let{size:e,grid:n}=t;const i=e-e%n,r=i+n;return e-i<r-e?i:r}function l(t){let{lineHeight:e,pixels:n,htmlFontSize:i}=t;return n/(e*i)}function h(t){let{cssProperty:e,min:n,max:i,unit:r="rem",breakpoints:a=[600,900,1200],transform:s=null}=t;const o={[e]:`${n}${r}`},l=(i-n)/a[a.length-1];return a.forEach((t=>{let i=n+l*t;null!==s&&(i=s(i)),o[`@media (min-width:${t}px)`]={[e]:`${Math.round(1e4*i)/1e4}${r}`}})),o}n.d(e,{I3:()=>s,VR:()=>o,a9:()=>i,db:()=>a,l_:()=>r,qW:()=>l,yL:()=>h})}}]);
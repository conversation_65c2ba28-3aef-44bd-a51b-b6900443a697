import React, { useState, useEffect } from 'react';
import { Box, Tab, Tabs, Typography } from '@mui/material';
import PropTypes from 'prop-types';
import MUIDataTable from 'mui-datatables';
import ZoneProperties from 'pages/dashboard/ZoneProperties';

const DetailsTabs = ({
    vehicles,
    onVehicleSelect,
    selectedZone,
    onEditGeometry,
    onGeometryChange,
    defaultTab,
    updatedGeometry,
    onZoneUpdate,
    onZoneDelete
}) => {
    const [tabValue, setTabValue] = useState(defaultTab || 0);
    const [selectedVehicleId, setSelectedVehicleId] = useState(null);
    const [editableZone, setEditableZone] = useState(null);

    const handleChange = (event, newValue) => {
        setTabValue(newValue);
    };

    useEffect(() => {
        if (selectedZone) {
            setEditableZone(selectedZone);
        }
    }, [selectedZone]);

    // Update tab when `defaultTab` changes
    useEffect(() => {
        if (defaultTab !== undefined) {
            setTabValue(defaultTab);
        }
    }, [defaultTab]);

    const handleRowClick = (rowData) => {
        const selectedVehicleId = rowData[0]; // Assuming vehicleId is the first column
        setSelectedVehicleId(selectedVehicleId); // Update the selected vehicle
        onVehicleSelect(selectedVehicleId); // Call the prop to notify parent
    };

    // Sort vehicles by lateness in descending order
    const sortedVehicles = vehicles.slice().sort((a, b) => b.lateness - a.lateness);
    const cellPadding = '4px';

    // Define columns for the table
    const columns = [
        {
            name: 'vehicleId',
            label: 'Vehicle ID',
            options: {
                setCellProps: () => ({
                    style: {
                        padding: '4px 0px 4px 20px' // 4px top, 0px right, 4px bottom, 10px left
                    }
                })
            }
        },
        {
            name: 'routeId',
            label: 'Route ID',
            options: {
                setCellProps: () => ({
                    style: {
                        padding: cellPadding // Use the configurable padding value
                    }
                })
            }
        },
        {
            name: 'tripId',
            label: 'Trip ID',
            options: {
                setCellProps: () => ({
                    style: {
                        padding: cellPadding // Use the configurable padding value
                    }
                })
            }
        },
        {
            name: 'timestamp',
            label: 'Timestamp',
            options: {
                customBodyRender: (value) => formatTimestamp(value),
                setCellProps: () => ({
                    style: {
                        padding: cellPadding // Use the configurable padding value
                    }
                })
            }
        },
        {
            name: 'lateness',
            label: 'Lateness (sec)',
            options: {
                customBodyRender: (lateness) => (
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box
                            sx={{
                                width: 16,
                                height: 16,
                                backgroundColor: getColorByVehicleLateness(lateness),
                                marginRight: 1
                            }}
                        />
                        {lateness} sec
                    </Box>
                ),
                setCellProps: () => ({
                    style: {
                        padding: cellPadding // Use the configurable padding value
                    }
                })
            }
        },
        {
            name: 'speed',
            label: 'Speed',
            options: {
                setCellProps: () => ({
                    style: {
                        padding: cellPadding // Use the configurable padding value
                    }
                })
            }
        },
        {
            name: 'bearing',
            label: 'Heading',
            options: {
                setCellProps: () => ({
                    style: {
                        padding: cellPadding // Use the configurable padding value
                    }
                })
            }
        }
    ];

    // Convert sorted vehicles into the format for the data table
    const data = sortedVehicles.map((vehicle) => ({
        vehicleId: vehicle.vehicleId,
        routeId: vehicle.routeId,
        tripId: vehicle.tripId,
        timestamp: formatTimestamp(vehicle.timestamp), // Convert to local time
        lateness: vehicle.lateness,
        speed: vehicle.speed,
        bearing: vehicle.bearing
    }));

    // Calculate the maximum table height based on screen size or window dimensions
    const getTableMaxHeight = () => {
        const screenWidth = window.innerWidth;
        if (screenWidth > 1920) {
            return '86vh'; // Adjust for larger screens
        } else if (screenWidth <= 1920 && screenWidth > 1024) {
            return '84.5vh'; // Adjust for smaller screens
        } else {
            return '70vh'; // For mobile screens
        }
    };

    const staticOptions = {
        filter: true,
        download: false,
        print: false,
        viewColumns: false,
        responsive: 'standard',
        fixedHeader: true,
        tableBodyMaxHeight: '430px', // This ensures a scrollable body
        //tableBodyHeight: getTableMaxHeight(),
        tableBodyHeight: '300px',
        selectableRows: 'none',
        setRowProps: (row, dataIndex) => {
            return {
                style: {
                    backgroundColor: row[0] === selectedVehicleId ? '#5d5d5d' : 'inherit', // Highlight the selected row
                    cursor: 'pointer'
                }
            };
        },
        onRowClick: handleRowClick
    };

    const options = {
        ...staticOptions
    };

    return (
        <Box sx={{ width: '100%' }}>
            <Tabs value={tabValue} onChange={handleChange} aria-label="Vehicle List Tabs">
                <Tab label="Vehicle List" />
                <Tab label="Properties" />
            </Tabs>
            <TabPanel value={tabValue} index={0}>
                <Box sx={{ height: '430px', overflowY: 'auto' }}>
                    <MUIDataTable data={data} columns={columns} options={options} />
                </Box>
            </TabPanel>
            <TabPanel value={tabValue} index={1}>
                <Box sx={{ height: '25vh', overflowY: 'auto' }}>
                    {selectedZone ? (
                        <ZoneProperties
                            zone={selectedZone}
                            onEditGeometry={onEditGeometry} // Initiate geometry editing
                            onGeometryChange={onGeometryChange} // Pass any geometry changes
                            updatedGeometry={updatedGeometry}
                            onZoneUpdate={onZoneUpdate}
                            onZoneDelete={onZoneDelete}
                        />
                    ) : (
                        <Box>
                            <Typography variant="h6" sx={{ marginTop: 2 }}>
                                No Item is Selected
                            </Typography>
                        </Box>
                    )}
                </Box>
            </TabPanel>
        </Box>
    );
};

// Helper component to display each tab panel
const TabPanel = (props) => {
    const { children, value, index, ...other } = props;
    return (
        <div role="tabpanel" hidden={value !== index} {...other}>
            {value === index && <Box sx={{ p: 0 }}>{children}</Box>}
        </div>
    );
};

TabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired
};

DetailsTabs.propTypes = {
    vehicles: PropTypes.array.isRequired,
    onVehicleSelect: PropTypes.func.isRequired,
    selectedZone: PropTypes.object, // Selected zone object to pass to ZoneProperties
    onEditGeometry: PropTypes.func.isRequired,
    onGeometryChange: PropTypes.func.isRequired,
    defaultTab: PropTypes.number, // Default tab to show (0: Vehicle List, 1: Properties)
    onZoneUpdate: PropTypes.func.isRequired,
    onZoneDelete: PropTypes.func.isRequired
};

const formatTimestamp = (timestamp) => {
    const UTCdate = new Date(timestamp);

    // Convert to the user's local timezone
    const localDate = new Date(UTCdate.toLocaleString());

    const year = localDate.getFullYear();
    const month = String(localDate.getMonth() + 1).padStart(2, '0');
    const day = String(localDate.getDate()).padStart(2, '0');
    const hours = String(localDate.getHours()).padStart(2, '0');
    const minutes = String(localDate.getMinutes()).padStart(2, '0');
    const seconds = String(localDate.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// Function to get the color based on lateness
const getColorByVehicleLateness = (lateness) => {
    if (lateness < 60) {
        return 'green';
    } else if (lateness >= 60 && lateness < 180) {
        return 'yellow';
    } else if (lateness >= 180 && lateness < 300) {
        return 'orange';
    } else {
        return 'red';
    }
};

export default DetailsTabs;

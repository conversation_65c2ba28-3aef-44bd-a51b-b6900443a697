import { lazy } from 'react';

// project import
import Loadable from 'components/Loadable';
import MainLayout from 'layout/MainLayout';
import { CheckAuthToken } from 'utils/auth';

// render - dashboard
const DashboardDefault = Loadable(lazy(() => import('pages/dashboard')));

// render - SystemConfiguration
const SystemConfiguration = Loadable(lazy(() => import('pages/systemConfiguration')));

// render - utilities
const UserManagement = Loadable(lazy(() => import('pages/components-overview/UserManagement')));
const Color = Loadable(lazy(() => import('pages/components-overview/Color')));
const Shadow = Loadable(lazy(() => import('pages/components-overview/Shadow')));
const AntIcons = Loadable(lazy(() => import('pages/components-overview/AntIcons')));

// ==============================|| MAIN ROUTING ||============================== //
//const LoadrTest = console.log('This is a test');

const MainRoutes = {
    path: '/main',
    element: <MainLayout />,
    loader: CheckAuthToken,
    children: [
        {
            path: '',
            element: <DashboardDefault />
            //loader: CheckAuthToken
        },
        {
            path: 'color',
            element: <Color />
        },
        {
            path: 'dashboard',
            children: [
                {
                    path: 'default',
                    element: <DashboardDefault />
                }
            ]
        },
        {
            path: 'system',
            element: <SystemConfiguration />
        },
        {
            path: 'users',
            element: <UserManagement />
        },
        {
            path: 'icons/ant',
            element: <AntIcons />
        }
    ]
};

export default MainRoutes;

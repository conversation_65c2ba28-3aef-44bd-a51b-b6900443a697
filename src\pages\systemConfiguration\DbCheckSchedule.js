import React, { useState, useEffect } from 'react';
import { Button, TextField, Select, MenuItem, FormControl, InputLabel, Typography } from '@mui/material';
import { getAuthToken } from 'utils/auth';

const DbCheckSchedule = () => {
    const [scheduleType, setScheduleType] = useState('interval'); // 'interval' or 'time'
    const [interval, setInterval] = useState('');
    const [selectedTime, setSelectedTime] = useState('');
    const [currentCron, setCurrentCron] = useState('');
    const [refreshCron, setRefreshCron] = useState(true);
    const token = getAuthToken();

    useEffect(() => {
        // Fetch the current cron value when component mounts
        fetch(`${process.env.REACT_APP_API_ENDPOINT}/systemProperties/getByName?name=runGtfsCron`, {
            method: 'GET',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
                // Add your authorization token here if needed
            }
        })
            .then((response) => response.json())
            .then((data) => {
                setCurrentCron(data.value);
                setRefreshCron(false); // Reset the flag after fetching
            })
            .catch((error) => {
                console.error('Error fetching current cron:', error);
            });
    }, [refreshCron]);

    const parseCron = (cron) => {
        if (!cron) return '';
        const parts = cron.split(' ');
        if (parts[1].includes('/')) {
            // It's an interval
            const interval = parts[1].split('/')[1];
            return `Every ${interval} minutes`;
        } else {
            // It's a specific time
            return `Everyday At ${parts[2]}:${parts[1]}`;
        }
    };

    const handleSubmit = () => {
        let cronExpression = '';

        // if (scheduleType === 'interval') {
        //     // Convert interval to cron expression
        //     cronExpression = `0 0/${interval} * * * ?`;
        // } else {
        //     const [hour, minute] = selectedTime.split(':');
        //     cronExpression = `0 ${minute} ${hour} * * ?`;
        // }

        const [hour, minute] = selectedTime.split(':');
        cronExpression = `0 ${minute} ${hour} * * ?`;

        // Send cronExpression to backend
        fetch(`${process.env.REACT_APP_API_ENDPOINT}/job/rescheduleGtfs`, {
            method: 'POST',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
                // Add your authorization token here if needed
            },
            body: JSON.stringify(cronExpression)
        })
            .then((response) => {
                if (response.ok) {
                    console.log('Schedule updated successfully');
                    setRefreshCron(true);
                } else {
                    console.error('Error updating schedule', response.statusText);
                }
            })
            .catch((error) => {
                console.error('Error updating schedule:', error);
            });
    };

    return (
        <div>
            <TextField
                label="Select Time"
                type="time"
                value={selectedTime}
                onChange={(e) => setSelectedTime(e.target.value)}
                InputLabelProps={{
                    shrink: true
                }}
                inputProps={{
                    step: 300 // 5 minutes
                }}
            />

            <Button variant="contained" color="primary" onClick={handleSubmit}>
                Apply
            </Button>
            <Typography variant="body1" style={{ marginLeft: 20 }}>
                Current Schedule: {parseCron(currentCron)}
            </Typography>
        </div>
    );
};

export default DbCheckSchedule;

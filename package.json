{"name": "mantis-free-react-admin-template", "version": "1.1.0", "private": true, "homepage": "/", "dependencies": {"@ant-design/colors": "^6.0.0", "@ant-design/icons": "^4.7.0", "@emotion/cache": "^11.10.3", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mapbox/mapbox-gl-draw": "^1.4.3", "@mui/icons-material": "^5.11.0", "@mui/lab": "^5.0.0-alpha.100", "@mui/material": "^5.11.3", "@mui/styles": "^5.10.6", "@mui/x-date-pickers": "^6.19.0", "@mui/x-tree-view": "^7.18.0", "@reduxjs/toolkit": "^1.8.5", "@stomp/stompjs": "^7.0.0", "@terraformer/wkt": "^2.2.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "apexcharts": "^3.49.1", "axios": "^1.5.0", "date-fns": "^3.6.0", "formik": "^2.2.9", "framer-motion": "^7.3.6", "history": "^5.3.0", "lodash": "^4.17.21", "mapbox-gl": "^2.15.0", "mui-datatables": "^4.3.0", "prop-types": "^15.8.1", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-copy-to-clipboard": "^5.1.0", "react-device-detect": "^2.2.2", "react-dom": "^18.2.0", "react-draggable": "^4.4.5", "react-element-to-jsx-string": "^15.0.0", "react-map-gl": "^7.1.6", "react-number-format": "^4.9.4", "react-perfect-scrollbar": "^1.5.8", "react-redux": "^8.0.4", "react-router": "^6.8.0", "react-router-dom": "^6.8.0", "react-scripts": "^5.0.1", "react-syntax-highlighter": "^15.5.0", "react-window": "^1.8.7", "recharts": "^2.12.7", "reconnecting-websocket": "^4.4.0", "redux": "^4.2.0", "rsuite": "^5.69.0", "simplebar": "^5.3.8", "simplebar-react": "^2.4.1", "sockjs-client": "^1.6.1", "terraformer-wkt-parser": "^1.2.1", "typescript": "4.8.3", "web-vitals": "^3.0.2", "yup": "^0.32.11"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "babel": {"presets": ["@babel/preset-react"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.19.1", "@babel/eslint-parser": "^7.19.1", "eslint": "^8.23.1", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.5.0", "eslint-config-react-app": "7.0.1", "eslint-import-resolver-typescript": "3.5.1", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "6.6.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.31.8", "eslint-plugin-react-hooks": "4.6.0", "prettier": "2.7.1"}}
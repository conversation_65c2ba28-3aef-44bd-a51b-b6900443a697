// project import
import dashboard from './dashboard';
import configuration from './configuration';
import { getSessionUser } from 'utils/auth';

// ==============================|| MENU ITEMS ||============================== //

const getMenuItems = () => {
    const userRoles = getSessionUser()?.roles || [];
    const isAdmin = userRoles.includes('ROLE_ADMIN');
    //console.log(userRoles);

    return {
        items: isAdmin ? [dashboard, configuration] : [dashboard]
    };
};

export default getMenuItems;

// material-ui
import { createTheme } from '@mui/material/styles';

// third-party
import { presetPalettes } from '@ant-design/colors';

// project import
import ThemeOption from './theme';

// ==============================|| DEFAULT THEME - PALETTE  ||============================== //

const Palette = (mode) => {
    const colors = presetPalettes;

    const greyPrimary = [
        '#ffffff',
        '#fafafa',
        '#f5f5f5',
        '#f0f0f0',
        '#d9d9d9',
        '#bfbfbf',
        '#8c8c8c',
        '#595959',
        '#262626',
        '#141414',
        '#000000'
    ];
    const greyAscent = ['#fafafa', '#bfbfbf', '#434343', '#1f1f1f'];
    const greyConstant = ['#fafafb', '#e6ebf1'];

    colors.grey = [...greyPrimary, ...greyAscent, ...greyConstant];

    const paletteColor = ThemeOption(colors);

    return createTheme({
        palette: {
            mode,
            common: {
                black: '#000',
                white: '#fff'
            },
            primary: {
                main: mode === 'dark' ? '#90caf9' : paletteColor.primary.main,
                light: mode === 'dark' ? '#e3f2fd' : paletteColor.primary.light,
                dark: mode === 'dark' ? '#42a5f5' : paletteColor.primary.dark,
                contrastText: mode === 'dark' ? '#3b4148' : paletteColor.primary.contrastText
            },
            secondary: {
                main: mode === 'dark' ? '#f48fb1' : paletteColor.secondary.main,
                light: mode === 'dark' ? '#f8bbd0' : paletteColor.secondary.light,
                dark: mode === 'dark' ? '#f06292' : paletteColor.secondary.dark,
                contrastText: mode === 'dark' ? '#3b4148' : paletteColor.secondary.contrastText
            },
            background: {
                paper: mode === 'dark' ? '#3a4149 !important' : paletteColor.background.paper,
                default: mode === 'dark' ? '#343332 !important' : paletteColor.background.default
            },
            text: {
                primary: mode === 'dark' ? '#ffffff' : paletteColor.text.primary,
                secondary: mode === 'dark' ? '#bdbdbd' : paletteColor.text.secondary,
                disabled: mode === 'dark' ? '#757575' : paletteColor.text.disabled
            }
        }
    });
};

export default Palette;

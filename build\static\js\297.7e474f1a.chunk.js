"use strict";(self.webpackChunkmantis_free_react_admin_template=self.webpackChunkmantis_free_react_admin_template||[]).push([[297],{88341:(e,t,s)=>{s.d(t,{A:()=>F});var i=s(9950),n=s(98587),r=s(58168),a=s(72004),o=s(88283),d=s(88465),l=s(97161),h=s(97497),m=s(59254),c=s(18463),x=s(1763),g=s(423);function j(e){return(0,g.Ay)("MuiSkeleton",e)}(0,x.A)("MuiSkeleton",["root","text","rectangular","rounded","circular","pulse","wave","withChildren","fitContent","heightAuto"]);var u=s(44414);const w=["animation","className","component","height","style","variant","width"];let A,y,p,f,b=e=>e;const v=(0,o.keyframes)(A||(A=b`
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.4;
  }

  100% {
    opacity: 1;
  }
`)),S=(0,o.keyframes)(y||(y=b`
  0% {
    transform: translateX(-100%);
  }

  50% {
    /* +0.5s of delay between each loop */
    transform: translateX(100%);
  }

  100% {
    transform: translateX(100%);
  }
`)),k=(0,m.Ay)("span",{name:"MuiSkeleton",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:s}=e;return[t.root,t[s.variant],!1!==s.animation&&t[s.animation],s.hasChildren&&t.withChildren,s.hasChildren&&!s.width&&t.fitContent,s.hasChildren&&!s.height&&t.heightAuto]}})((e=>{let{theme:t,ownerState:s}=e;const i=(0,l.l_)(t.shape.borderRadius)||"px",n=(0,l.db)(t.shape.borderRadius);return(0,r.A)({display:"block",backgroundColor:t.vars?t.vars.palette.Skeleton.bg:(0,h.X4)(t.palette.text.primary,"light"===t.palette.mode?.11:.13),height:"1.2em"},"text"===s.variant&&{marginTop:0,marginBottom:0,height:"auto",transformOrigin:"0 55%",transform:"scale(1, 0.60)",borderRadius:`${n}${i}/${Math.round(n/.6*10)/10}${i}`,"&:empty:before":{content:'"\\00a0"'}},"circular"===s.variant&&{borderRadius:"50%"},"rounded"===s.variant&&{borderRadius:(t.vars||t).shape.borderRadius},s.hasChildren&&{"& > *":{visibility:"hidden"}},s.hasChildren&&!s.width&&{maxWidth:"fit-content"},s.hasChildren&&!s.height&&{height:"auto"})}),(e=>{let{ownerState:t}=e;return"pulse"===t.animation&&(0,o.css)(p||(p=b`
      animation: ${0} 2s ease-in-out 0.5s infinite;
    `),v)}),(e=>{let{ownerState:t,theme:s}=e;return"wave"===t.animation&&(0,o.css)(f||(f=b`
      position: relative;
      overflow: hidden;

      /* Fix bug in Safari https://bugs.webkit.org/show_bug.cgi?id=68196 */
      -webkit-mask-image: -webkit-radial-gradient(white, black);

      &::after {
        animation: ${0} 2s linear 0.5s infinite;
        background: linear-gradient(
          90deg,
          transparent,
          ${0},
          transparent
        );
        content: '';
        position: absolute;
        transform: translateX(-100%); /* Avoid flash during server-side hydration */
        bottom: 0;
        left: 0;
        right: 0;
        top: 0;
      }
    `),S,(s.vars||s).palette.action.hover)})),C=i.forwardRef((function(e,t){const s=(0,c.b)({props:e,name:"MuiSkeleton"}),{animation:i="pulse",className:o,component:l="span",height:h,style:m,variant:x="text",width:g}=s,A=(0,n.A)(s,w),y=(0,r.A)({},s,{animation:i,component:l,variant:x,hasChildren:Boolean(A.children)}),p=(e=>{const{classes:t,variant:s,animation:i,hasChildren:n,width:r,height:a}=e,o={root:["root",s,i,n&&"withChildren",n&&!r&&"fitContent",n&&!a&&"heightAuto"]};return(0,d.A)(o,j,t)})(y);return(0,u.jsx)(k,(0,r.A)({as:l,ref:t,className:(0,a.A)(p.root,o),ownerState:y},A,{style:(0,r.A)({width:g,height:h},m)}))}));var $=s(93230),_=s(4139),R=s(55515);const F=e=>{let{children:t}=e;const[s,n]=(0,i.useState)(!0);(0,i.useEffect)((()=>{n(!1)}),[]);const r=(0,u.jsx)(R.A,{title:(0,u.jsx)(C,{sx:{width:{xs:120,md:180}}}),secondary:(0,u.jsx)(C,{animation:"wave",variant:"circular",width:24,height:24}),children:(0,u.jsxs)($.A,{spacing:1,children:[(0,u.jsx)(C,{}),(0,u.jsx)(C,{sx:{height:64},animation:"wave",variant:"rectangular"}),(0,u.jsx)(C,{}),(0,u.jsx)(C,{})]})});return(0,u.jsxs)(u.Fragment,{children:[s&&(0,u.jsxs)(_.Ay,{container:!0,spacing:3,children:[(0,u.jsx)(_.Ay,{item:!0,xs:12,md:6,children:r}),(0,u.jsx)(_.Ay,{item:!0,xs:12,md:6,children:r}),(0,u.jsx)(_.Ay,{item:!0,xs:12,md:6,children:r}),(0,u.jsx)(_.Ay,{item:!0,xs:12,md:6,children:r})]}),!s&&t]})}},34297:(e,t,s)=>{s.r(t),s.d(t,{default:()=>c});var i=s(14857),n=s(93230),r=s(87233),a=s(4139),o=s(55515),d=s(88341),l=s(44414);function h(e){let{shadow:t}=e;return(0,l.jsx)(o.A,{border:!1,sx:{boxShadow:t},children:(0,l.jsxs)(n.A,{spacing:1,justifyContent:"center",alignItems:"center",children:[(0,l.jsx)(r.A,{variant:"h6",children:"boxShadow"}),(0,l.jsx)(r.A,{variant:"subtitle1",children:t})]})})}function m(e){let{shadow:t,label:s,color:i,bgcolor:a}=e;return(0,l.jsx)(o.A,{border:!1,sx:{bgcolor:a||"inherit",boxShadow:t},children:(0,l.jsx)(n.A,{spacing:1,justifyContent:"center",alignItems:"center",children:(0,l.jsx)(r.A,{variant:"subtitle1",color:i,children:s})})})}const c=()=>{const e=(0,i.A)();return(0,l.jsx)(d.A,{children:(0,l.jsxs)(a.Ay,{container:!0,spacing:3,children:[(0,l.jsx)(a.Ay,{item:!0,xs:12,children:(0,l.jsx)(o.A,{title:"Basic Shadow",codeHighlight:!0,children:(0,l.jsxs)(a.Ay,{container:!0,spacing:3,children:[(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"0"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"1"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"2"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"3"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"4"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"5"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"6"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"7"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"8"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"9"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"10"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"11"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"12"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"13"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"14"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"15"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"16"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"17"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"18"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"19"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"20"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"21"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"22"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"23"})}),(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(h,{shadow:"24"})})]})})}),(0,l.jsx)(a.Ay,{item:!0,xs:12,children:(0,l.jsx)(o.A,{title:"Custom Shadow",codeHighlight:!0,children:(0,l.jsx)(a.Ay,{container:!0,spacing:3,children:(0,l.jsx)(a.Ay,{item:!0,xs:6,sm:4,md:3,lg:2,children:(0,l.jsx)(m,{shadow:e.customShadows.z1,label:"z1",color:"inherit"})})})})})]})})}},97161:(e,t,s)=>{function i(e){return String(parseFloat(e)).length===String(e).length}function n(e){return String(e).match(/[\d.\-+]*\s*(.*)/)[1]||""}function r(e){return parseFloat(e)}function a(e){return(t,s)=>{const i=n(t);if(i===s)return t;let a=r(t);"px"!==i&&("em"===i||"rem"===i)&&(a=r(t)*r(e));let o=a;if("px"!==s)if("em"===s)o=a/r(e);else{if("rem"!==s)return t;o=a/r(e)}return parseFloat(o.toFixed(5))+s}}function o(e){let{size:t,grid:s}=e;const i=t-t%s,n=i+s;return t-i<n-t?i:n}function d(e){let{lineHeight:t,pixels:s,htmlFontSize:i}=e;return s/(t*i)}function l(e){let{cssProperty:t,min:s,max:i,unit:n="rem",breakpoints:r=[600,900,1200],transform:a=null}=e;const o={[t]:`${s}${n}`},d=(i-s)/r[r.length-1];return r.forEach((e=>{let i=s+d*e;null!==a&&(i=a(i)),o[`@media (min-width:${e}px)`]={[t]:`${Math.round(1e4*i)/1e4}${n}`}})),o}s.d(t,{I3:()=>a,VR:()=>o,a9:()=>i,db:()=>r,l_:()=>n,qW:()=>d,yL:()=>l})}}]);
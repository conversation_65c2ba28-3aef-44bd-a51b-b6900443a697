import React, { useEffect, useState } from 'react';
import {
    Avatar,
    Button,
    CssBaseline,
    TextField,
    Alert,
    FormControl,
    FormHelperText,
    FormControlLabel,
    Checkbox,
    Link,
    InputAdornment,
    IconButton,
    Paper,
    Box,
    Grid,
    Typography,
    createTheme,
    ThemeProvider
} from '@mui/material';
import LockOutlinedIcon from '@mui/icons-material/LockOutlined';
import { strengthColor, strengthIndicator } from 'utils/password-strength';
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import LoginBackground from 'assets/images/auth/LoginBackground.png';
import { useNavigate } from 'react-router-dom';
import { Link as RouterLink } from 'react-router-dom';
import { useFormik } from 'formik'; // Import useFormik from Formik
import * as Yup from 'yup'; // Import Yup for validation

const theme = createTheme();

const darkTheme = createTheme({
    palette: {
        mode: 'dark' // Switches the theme to dark mode
    }
});

function SignUp() {
    const navigate = useNavigate();
    const [showPassword, setShowPassword] = useState(false);
    const [level, setLevel] = useState();

    const validationSchema = Yup.object().shape({
        username: Yup.string().max(20).required('Username is required'),
        email: Yup.string().email('Must be a valid email').max(50).required('Email is required'),
        password: Yup.string().max(255).required('Password is required')
    });

    const handleClickShowPassword = () => {
        setShowPassword(!showPassword);
    };

    const handleMouseDownPassword = (event) => {
        event.preventDefault();
    };

    const handlePasswordChange = (value) => {
        const temp = strengthIndicator(value);
        setLevel(strengthColor(temp));
    };

    useEffect(() => {
        handlePasswordChange('');
    }, []);

    const formik = useFormik({
        initialValues: {
            username: '',
            email: '',
            password: ''
        },
        validationSchema,
        onSubmit: async (values) => {
            // Clear the success and error messages first
            setSuccess('');
            setError('');
            try {
                const response = await fetch(`${process.env.REACT_APP_API_ENDPOINT}/api/auth/signup`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(values)
                });

                if (response.ok) {
                    const data = await response.json();
                    setSuccess(data.message);
                } else {
                    const data = await response.json();
                    setError(data.message);
                }
            } catch (error) {
                setError('An error occurred. Please try again later.');
            }
        }
    });

    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');

    return (
        <ThemeProvider theme={darkTheme}>
            <Grid container component="main" sx={{ height: '100vh' }}>
                <CssBaseline />
                <Grid
                    item
                    xs={false}
                    sm={false}
                    md={7}
                    sx={{
                        backgroundImage: `url(${LoginBackground})`,
                        backgroundRepeat: 'no-repeat',
                        backgroundColor: (t) => (t.palette.mode === 'light' ? t.palette.grey[50] : t.palette.grey[900]),
                        backgroundSize: 'cover',
                        backgroundPosition: 'left'
                        //backgroundColor: '#2b7bba'
                    }}
                />
                <Grid item xs={12} sm={12} md={5} component={Paper} elevation={0} square sx={{ backgroundColor: '#0f2139 !important' }}>
                    <Box
                        sx={{
                            my: 8,
                            mx: 4,
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center'
                        }}
                    >
                        <Avatar sx={{ m: 1, bgcolor: 'secondary.main' }}>
                            <LockOutlinedIcon />
                        </Avatar>
                        <Typography component="h1" variant="h5">
                            Sign Up
                        </Typography>
                        <Box sx={{ mt: 1, width: '80%' }}>
                            <form onSubmit={formik.handleSubmit}>
                                <TextField
                                    margin="normal"
                                    required
                                    fullWidth
                                    id="username"
                                    label="Username"
                                    name="username"
                                    autoComplete="username"
                                    error={formik.touched.username && Boolean(formik.errors.username)}
                                    helperText={formik.touched.username && formik.errors.username}
                                    value={formik.values.username}
                                    onChange={formik.handleChange}
                                    onBlur={formik.handleBlur}
                                />
                                <TextField
                                    margin="normal"
                                    required
                                    fullWidth
                                    id="email"
                                    label="Email"
                                    name="email"
                                    autoComplete="email"
                                    error={formik.touched.email && Boolean(formik.errors.email)}
                                    helperText={formik.touched.email && formik.errors.email}
                                    value={formik.values.email}
                                    onChange={formik.handleChange}
                                    onBlur={formik.handleBlur}
                                />
                                <TextField
                                    margin="normal"
                                    required
                                    fullWidth
                                    name="password"
                                    label="Password"
                                    //type="password"
                                    id="password"
                                    autoComplete="new-password"
                                    error={formik.touched.password && Boolean(formik.errors.password)}
                                    helperText={formik.touched.password && formik.errors.password}
                                    value={formik.values.password}
                                    onChange={(e) => {
                                        formik.handleChange(e);
                                        handlePasswordChange(e.target.value);
                                    }}
                                    onBlur={formik.handleBlur}
                                    type={showPassword ? 'text' : 'password'} // Set the input type based on showPassword state
                                    InputProps={{
                                        endAdornment: (
                                            <InputAdornment position="end">
                                                <IconButton
                                                    aria-label="toggle password visibility"
                                                    onClick={handleClickShowPassword}
                                                    onMouseDown={handleMouseDownPassword}
                                                    edge="end"
                                                    size="large"
                                                >
                                                    {showPassword ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                                                </IconButton>
                                            </InputAdornment>
                                        )
                                    }}
                                />
                                <FormControl fullWidth sx={{ mt: 2 }}>
                                    <Grid container spacing={2} alignItems="center">
                                        <Grid item>
                                            <Box sx={{ bgcolor: level?.color, width: 85, height: 8, borderRadius: '7px' }} />
                                        </Grid>
                                        <Grid item>
                                            <Typography variant="subtitle1" fontSize="0.75rem">
                                                {level?.label}
                                            </Typography>
                                        </Grid>
                                    </Grid>
                                </FormControl>

                                <Button
                                    type="submit"
                                    fullWidth
                                    variant="contained"
                                    sx={{ mt: 3, mb: 2 }}
                                    disabled={!formik.isValid || formik.isSubmitting}
                                >
                                    Create Account
                                </Button>
                                <Grid container>
                                    <Grid item xs>
                                        {/* You can add any additional links or content here */}
                                    </Grid>
                                    <Grid item>
                                        <RouterLink to="/" style={{ textDecoration: 'none', color: 'inherit' }}>
                                            {'Already have an account? Sign In'}
                                        </RouterLink>
                                    </Grid>
                                </Grid>
                            </form>
                        </Box>
                        {success && (
                            <Alert severity="success" sx={{ mt: 2 }}>
                                {success}
                            </Alert>
                        )}
                        {error && (
                            <Alert severity="error" sx={{ mt: 2 }}>
                                {error}
                            </Alert>
                        )}
                    </Box>
                </Grid>
            </Grid>
        </ThemeProvider>
    );
}

export default SignUp;

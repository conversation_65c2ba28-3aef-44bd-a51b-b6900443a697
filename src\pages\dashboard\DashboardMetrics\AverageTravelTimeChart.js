import React from 'react';
import Chart from 'react-apexcharts';
import MainCard from 'components/MainCard';

const AverageTravelTimeChart = React.memo(({ data, compareOption, compareToOption }) => {
    const categories = data.map((item) => item.name || 'Unknown');
    const seriesData = data.map((item) => {
        const value = item[compareToOption] ?? 0;
        return typeof value === 'number' ? value : 0;
    });

    console.log('Travel Time Data:', seriesData); // Debug log

    const chartOptions = {
        chart: {
            type: 'bar',
            height: 250
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: '55%',
                endingShape: 'rounded'
            }
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            show: true,
            width: 2,
            colors: ['transparent']
        },
        xaxis: {
            categories: categories,
            labels: {
                style: {
                    colors: '#a9a9a9'
                }
            },
            axisTicks: {
                show: true,
                color: '#a9a9a9'
            }
        },
        yaxis: {
            labels: {
                style: {
                    colors: '#a9a9a9'
                }
            },
            title: {
                text: 'Time (minutes)',
                style: {
                    color: '#a9a9a9'
                },
                labels: {
                    style: {
                        colors: '#a9a9a9'
                    }
                }
            },
            axisBorder: {
                show: true,
                color: '#a9a9a9'
            },
            axisTicks: {
                show: true,
                color: '#a9a9a9'
            }
        },
        fill: {
            opacity: 1
        },
        grid: {
            borderColor: '#a9a9a9',
            strokeDashArray: 3
        },
        legend: {
            labels: {
                colors: '#a9a9a9'
            }
        },
        tooltip: {
            theme: 'dark',
            style: {
                fontSize: '12px',
                fontFamily: undefined,
                colors: ['#a9a9a9']
            },
            y: {
                formatter: (val) => `${val} minutes`
            }
        }
    };

    const series = [
        {
            name: compareOption,
            data: data.map((item) => item[compareOption] || 0),
            color: '#71d3c8'
        },
        {
            name: compareToOption,
            data: data.map((item) => item[compareToOption] || 0),
            color: '#dbaa32'
        }
    ];

    return (
        <MainCard title="Average Travel Time">
            <Chart options={chartOptions} series={series} type="bar" height={250} />
        </MainCard>
    );
});

export default AverageTravelTimeChart;

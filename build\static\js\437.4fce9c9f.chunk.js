"use strict";(self.webpackChunkmantis_free_react_admin_template=self.webpackChunkmantis_free_react_admin_template||[]).push([[437],{88341:(i,e,t)=>{t.d(e,{A:()=>q});var r=t(9950),a=t(98587),n=t(58168),s=t(72004),l=t(88283),d=t(88465),o=t(97161),h=t(97497),c=t(59254),u=t(18463),m=t(1763),x=t(423);function g(i){return(0,x.Ay)("MuiSkeleton",i)}(0,m.A)("MuiSkeleton",["root","text","rectangular","rounded","circular","pulse","wave","withChildren","fitContent","heightAuto"]);var A=t(44414);const j=["animation","className","component","height","style","variant","width"];let p,b,v,y,f=i=>i;const H=(0,l.keyframes)(p||(p=f`
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.4;
  }

  100% {
    opacity: 1;
  }
`)),S=(0,l.keyframes)(b||(b=f`
  0% {
    transform: translateX(-100%);
  }

  50% {
    /* +0.5s of delay between each loop */
    transform: translateX(100%);
  }

  100% {
    transform: translateX(100%);
  }
`)),w=(0,c.Ay)("span",{name:"MuiSkeleton",slot:"Root",overridesResolver:(i,e)=>{const{ownerState:t}=i;return[e.root,e[t.variant],!1!==t.animation&&e[t.animation],t.hasChildren&&e.withChildren,t.hasChildren&&!t.width&&e.fitContent,t.hasChildren&&!t.height&&e.heightAuto]}})((i=>{let{theme:e,ownerState:t}=i;const r=(0,o.l_)(e.shape.borderRadius)||"px",a=(0,o.db)(e.shape.borderRadius);return(0,n.A)({display:"block",backgroundColor:e.vars?e.vars.palette.Skeleton.bg:(0,h.X4)(e.palette.text.primary,"light"===e.palette.mode?.11:.13),height:"1.2em"},"text"===t.variant&&{marginTop:0,marginBottom:0,height:"auto",transformOrigin:"0 55%",transform:"scale(1, 0.60)",borderRadius:`${a}${r}/${Math.round(a/.6*10)/10}${r}`,"&:empty:before":{content:'"\\00a0"'}},"circular"===t.variant&&{borderRadius:"50%"},"rounded"===t.variant&&{borderRadius:(e.vars||e).shape.borderRadius},t.hasChildren&&{"& > *":{visibility:"hidden"}},t.hasChildren&&!t.width&&{maxWidth:"fit-content"},t.hasChildren&&!t.height&&{height:"auto"})}),(i=>{let{ownerState:e}=i;return"pulse"===e.animation&&(0,l.css)(v||(v=f`
      animation: ${0} 2s ease-in-out 0.5s infinite;
    `),H)}),(i=>{let{ownerState:e,theme:t}=i;return"wave"===e.animation&&(0,l.css)(y||(y=f`
      position: relative;
      overflow: hidden;

      /* Fix bug in Safari https://bugs.webkit.org/show_bug.cgi?id=68196 */
      -webkit-mask-image: -webkit-radial-gradient(white, black);

      &::after {
        animation: ${0} 2s linear 0.5s infinite;
        background: linear-gradient(
          90deg,
          transparent,
          ${0},
          transparent
        );
        content: '';
        position: absolute;
        transform: translateX(-100%); /* Avoid flash during server-side hydration */
        bottom: 0;
        left: 0;
        right: 0;
        top: 0;
      }
    `),S,(t.vars||t).palette.action.hover)})),L=r.forwardRef((function(i,e){const t=(0,u.b)({props:i,name:"MuiSkeleton"}),{animation:r="pulse",className:l,component:o="span",height:h,style:c,variant:m="text",width:x}=t,p=(0,a.A)(t,j),b=(0,n.A)({},t,{animation:r,component:o,variant:m,hasChildren:Boolean(p.children)}),v=(i=>{const{classes:e,variant:t,animation:r,hasChildren:a,width:n,height:s}=i,l={root:["root",t,r,a&&"withChildren",a&&!n&&"fitContent",a&&!s&&"heightAuto"]};return(0,d.A)(l,g,e)})(b);return(0,A.jsx)(w,(0,n.A)({as:o,ref:e,className:(0,s.A)(v.root,l),ownerState:b},p,{style:(0,n.A)({width:x,height:h},c)}))}));var B=t(93230),R=t(4139),k=t(55515);const q=i=>{let{children:e}=i;const[t,a]=(0,r.useState)(!0);(0,r.useEffect)((()=>{a(!1)}),[]);const n=(0,A.jsx)(k.A,{title:(0,A.jsx)(L,{sx:{width:{xs:120,md:180}}}),secondary:(0,A.jsx)(L,{animation:"wave",variant:"circular",width:24,height:24}),children:(0,A.jsxs)(B.A,{spacing:1,children:[(0,A.jsx)(L,{}),(0,A.jsx)(L,{sx:{height:64},animation:"wave",variant:"rectangular"}),(0,A.jsx)(L,{}),(0,A.jsx)(L,{})]})});return(0,A.jsxs)(A.Fragment,{children:[t&&(0,A.jsxs)(R.Ay,{container:!0,spacing:3,children:[(0,A.jsx)(R.Ay,{item:!0,xs:12,md:6,children:n}),(0,A.jsx)(R.Ay,{item:!0,xs:12,md:6,children:n}),(0,A.jsx)(R.Ay,{item:!0,xs:12,md:6,children:n}),(0,A.jsx)(R.Ay,{item:!0,xs:12,md:6,children:n})]}),!t&&e]})}},45437:(i,e,t)=>{t.r(e),t.d(e,{default:()=>u});var r=t(4139),a=t(87233),n=t(93230),s=t(52432),l=t(74745),d=t(36080),o=t(88341),h=t(55515),c=t(44414);const u=()=>(0,c.jsx)(o.A,{children:(0,c.jsxs)(r.Ay,{container:!0,spacing:3,children:[(0,c.jsx)(r.Ay,{item:!0,xs:12,sx:{mb:-2.25},children:(0,c.jsx)(a.A,{variant:"h5",children:"User Management"})}),(0,c.jsx)(r.Ay,{item:!0,xs:12,lg:6,children:(0,c.jsxs)(n.A,{spacing:3,children:[(0,c.jsx)(h.A,{title:"Basic",codeHighlight:!0,children:(0,c.jsxs)(n.A,{spacing:.75,sx:{mt:-1.5},children:[(0,c.jsx)(a.A,{variant:"h1",children:"Inter"}),(0,c.jsx)(a.A,{variant:"h5",children:"Font Family"}),(0,c.jsxs)(s.A,{"aria-label":"breadcrumb",children:[(0,c.jsx)(a.A,{variant:"h6",children:"Regular"}),(0,c.jsx)(a.A,{variant:"h6",children:"Medium"}),(0,c.jsx)(a.A,{variant:"h6",children:"Bold"})]})]})}),(0,c.jsx)(h.A,{title:"Heading",codeHighlight:!0,children:(0,c.jsxs)(n.A,{spacing:2,children:[(0,c.jsx)(a.A,{variant:"h1",children:"H1 Heading"}),(0,c.jsxs)(s.A,{"aria-label":"breadcrumb",children:[(0,c.jsx)(a.A,{variant:"h6",children:"Size: 38px"}),(0,c.jsx)(a.A,{variant:"h6",children:"Weight: Bold"}),(0,c.jsx)(a.A,{variant:"h6",children:"Line Height: 46px"})]}),(0,c.jsx)(l.A,{}),(0,c.jsx)(a.A,{variant:"h2",children:"H2 Heading"}),(0,c.jsxs)(s.A,{"aria-label":"breadcrumb",children:[(0,c.jsx)(a.A,{variant:"h6",children:"Size: 30px"}),(0,c.jsx)(a.A,{variant:"h6",children:"Weight: Bold"}),(0,c.jsx)(a.A,{variant:"h6",children:"Line Height: 38px"})]}),(0,c.jsx)(l.A,{}),(0,c.jsx)(a.A,{variant:"h3",children:"H3 Heading"}),(0,c.jsxs)(s.A,{"aria-label":"breadcrumb",children:[(0,c.jsx)(a.A,{variant:"h6",children:"Size: 24px"}),(0,c.jsx)(a.A,{variant:"h6",children:"Weight: Regular & Bold"}),(0,c.jsx)(a.A,{variant:"h6",children:"Line Height: 32px"})]}),(0,c.jsx)(l.A,{}),(0,c.jsx)(a.A,{variant:"h4",children:"H4 Heading"}),(0,c.jsxs)(s.A,{"aria-label":"breadcrumb",children:[(0,c.jsx)(a.A,{variant:"h6",children:"Size: 20px"}),(0,c.jsx)(a.A,{variant:"h6",children:"Weight: Bold"}),(0,c.jsx)(a.A,{variant:"h6",children:"Line Height: 28px"})]}),(0,c.jsx)(l.A,{}),(0,c.jsx)(a.A,{variant:"h5",children:"H5 Heading"}),(0,c.jsxs)(s.A,{"aria-label":"breadcrumb",children:[(0,c.jsx)(a.A,{variant:"h6",children:"Size: 16px"}),(0,c.jsx)(a.A,{variant:"h6",children:"Weight: Regular & Medium & Bold"}),(0,c.jsx)(a.A,{variant:"h6",children:"Line Height: 24px"})]}),(0,c.jsx)(l.A,{}),(0,c.jsx)(a.A,{variant:"h6",children:"H6 Heading / Subheading"}),(0,c.jsxs)(s.A,{"aria-label":"breadcrumb",children:[(0,c.jsx)(a.A,{variant:"h6",children:"Size: 14px"}),(0,c.jsx)(a.A,{variant:"h6",children:"Weight: Regular"}),(0,c.jsx)(a.A,{variant:"h6",children:"Line Height: 22px"})]})]})}),(0,c.jsx)(h.A,{title:"Body 1",codeHighlight:!0,children:(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(a.A,{variant:"body1",gutterBottom:!0,children:"Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."}),(0,c.jsxs)(s.A,{"aria-label":"breadcrumb",children:[(0,c.jsx)(a.A,{variant:"h6",children:"Size: 14px"}),(0,c.jsx)(a.A,{variant:"h6",children:"Weight: Regular"}),(0,c.jsx)(a.A,{variant:"h6",children:"Line Height: 22px"})]})]})}),(0,c.jsx)(h.A,{title:"Body 2",codeHighlight:!0,children:(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(a.A,{variant:"body2",gutterBottom:!0,children:"Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."}),(0,c.jsxs)(s.A,{"aria-label":"breadcrumb",children:[(0,c.jsx)(a.A,{variant:"h6",children:"Size: 12px"}),(0,c.jsx)(a.A,{variant:"h6",children:"Weight: Regular"}),(0,c.jsx)(a.A,{variant:"h6",children:"Line Height: 20px"})]})]})}),(0,c.jsx)(h.A,{title:"Subtitle 1",codeHighlight:!0,children:(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(a.A,{variant:"subtitle1",gutterBottom:!0,children:"Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."}),(0,c.jsxs)(s.A,{"aria-label":"breadcrumb",children:[(0,c.jsx)(a.A,{variant:"h6",children:"Size: 14px"}),(0,c.jsx)(a.A,{variant:"h6",children:"Weight: Medium"}),(0,c.jsx)(a.A,{variant:"h6",children:"Line Height: 22px"})]})]})}),(0,c.jsx)(h.A,{title:"Subtitle 2",codeHighlight:!0,children:(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(a.A,{variant:"subtitle2",gutterBottom:!0,children:"Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."}),(0,c.jsxs)(s.A,{"aria-label":"breadcrumb",children:[(0,c.jsx)(a.A,{variant:"h6",children:"Size: 12px"}),(0,c.jsx)(a.A,{variant:"h6",children:"Weight: Medium"}),(0,c.jsx)(a.A,{variant:"h6",children:"Line Height: 20px"})]})]})}),(0,c.jsx)(h.A,{title:"Caption",codeHighlight:!0,children:(0,c.jsxs)(n.A,{spacing:1,children:[(0,c.jsx)(a.A,{variant:"caption",children:"Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."}),(0,c.jsxs)(s.A,{"aria-label":"breadcrumb",children:[(0,c.jsx)(a.A,{variant:"h6",children:"Size: 12px"}),(0,c.jsx)(a.A,{variant:"h6",children:"Weight: Regular"}),(0,c.jsx)(a.A,{variant:"h6",children:"Line Height: 20px"})]})]})})]})}),(0,c.jsx)(r.Ay,{item:!0,xs:12,lg:6,children:(0,c.jsxs)(n.A,{spacing:3,children:[(0,c.jsx)(h.A,{title:"Alignment",codeHighlight:!0,children:(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(a.A,{variant:"body2",gutterBottom:!0,children:"Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."}),(0,c.jsx)(a.A,{variant:"body2",textAlign:"center",gutterBottom:!0,children:"Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."}),(0,c.jsx)(a.A,{variant:"body2",textAlign:"right",children:"Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."})]})}),(0,c.jsx)(h.A,{title:"Gutter Bottom",codeHighlight:!0,children:(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(a.A,{variant:"body1",gutterBottom:!0,children:"Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."}),(0,c.jsx)(a.A,{variant:"body2",gutterBottom:!0,children:"Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."}),(0,c.jsxs)(s.A,{"aria-label":"breadcrumb",children:[(0,c.jsx)(a.A,{variant:"h6",children:"Size: 12px"}),(0,c.jsx)(a.A,{variant:"h6",children:"Weight: Regular"}),(0,c.jsx)(a.A,{variant:"h6",children:"Line Height: 20px"})]})]})}),(0,c.jsx)(h.A,{title:"Overline",codeHighlight:!0,children:(0,c.jsxs)(n.A,{spacing:1.5,children:[(0,c.jsx)(a.A,{variant:"overline",children:"Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."}),(0,c.jsxs)(s.A,{"aria-label":"breadcrumb",children:[(0,c.jsx)(a.A,{variant:"h6",children:"Size: 12px"}),(0,c.jsx)(a.A,{variant:"h6",children:"Weight: Regular"}),(0,c.jsx)(a.A,{variant:"h6",children:"Line Height: 20px"})]})]})}),(0,c.jsx)(h.A,{title:"Link",codeHighlight:!0,children:(0,c.jsxs)(n.A,{spacing:1.5,children:[(0,c.jsx)(d.A,{href:"#",children:"www.mantis.com"}),(0,c.jsxs)(s.A,{"aria-label":"breadcrumb",children:[(0,c.jsx)(a.A,{variant:"h6",children:"Size: 12px"}),(0,c.jsx)(a.A,{variant:"h6",children:"Weight: Regular"}),(0,c.jsx)(a.A,{variant:"h6",children:"Line Height: 20px"})]})]})}),(0,c.jsx)(h.A,{title:"Colors",codeHighlight:!0,children:(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(a.A,{variant:"h6",color:"textPrimary",gutterBottom:!0,children:"This is textPrimary text color."}),(0,c.jsx)(a.A,{variant:"h6",color:"textSecondary",gutterBottom:!0,children:"This is textSecondary text color."}),(0,c.jsx)(a.A,{variant:"h6",color:"primary",gutterBottom:!0,children:"This is primary text color."}),(0,c.jsx)(a.A,{variant:"h6",color:"secondary",gutterBottom:!0,children:"This is secondary text color."}),(0,c.jsx)(a.A,{variant:"h6",color:"success",gutterBottom:!0,children:"This is success text color."}),(0,c.jsx)(a.A,{variant:"h6",sx:{color:"warning.main"},gutterBottom:!0,children:"This is warning text color."}),(0,c.jsx)(a.A,{variant:"h6",color:"error",gutterBottom:!0,children:"This is error text color."})]})}),(0,c.jsx)(h.A,{title:"Paragraph",codeHighlight:!0,children:(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(a.A,{variant:"body1",gutterBottom:!0,children:"Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."}),(0,c.jsxs)(s.A,{"aria-label":"breadcrumb",children:[(0,c.jsx)(a.A,{variant:"h6",children:"Size: 14px"}),(0,c.jsx)(a.A,{variant:"h6",children:"Weight: Regular"}),(0,c.jsx)(a.A,{variant:"h6",children:"Line Height: 22px"})]})]})}),(0,c.jsx)(h.A,{title:"Font Style",codeHighlight:!0,children:(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(a.A,{variant:"body1",gutterBottom:!0,sx:{fontStyle:"italic"},children:"Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."}),(0,c.jsx)(a.A,{variant:"subtitle1",gutterBottom:!0,sx:{fontStyle:"italic"},children:"Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."}),(0,c.jsxs)(s.A,{"aria-label":"breadcrumb",children:[(0,c.jsx)(a.A,{variant:"h6",children:"Size: 14px"}),(0,c.jsx)(a.A,{variant:"h6",children:"Weight: Italic Regular & Italic Bold"}),(0,c.jsx)(a.A,{variant:"h6",children:"Line Height: 22px"})]})]})})]})})]})})},97161:(i,e,t)=>{function r(i){return String(parseFloat(i)).length===String(i).length}function a(i){return String(i).match(/[\d.\-+]*\s*(.*)/)[1]||""}function n(i){return parseFloat(i)}function s(i){return(e,t)=>{const r=a(e);if(r===t)return e;let s=n(e);"px"!==r&&("em"===r||"rem"===r)&&(s=n(e)*n(i));let l=s;if("px"!==t)if("em"===t)l=s/n(i);else{if("rem"!==t)return e;l=s/n(i)}return parseFloat(l.toFixed(5))+t}}function l(i){let{size:e,grid:t}=i;const r=e-e%t,a=r+t;return e-r<a-e?r:a}function d(i){let{lineHeight:e,pixels:t,htmlFontSize:r}=i;return t/(e*r)}function o(i){let{cssProperty:e,min:t,max:r,unit:a="rem",breakpoints:n=[600,900,1200],transform:s=null}=i;const l={[e]:`${t}${a}`},d=(r-t)/n[n.length-1];return n.forEach((i=>{let r=t+d*i;null!==s&&(r=s(r)),l[`@media (min-width:${i}px)`]={[e]:`${Math.round(1e4*r)/1e4}${a}`}})),l}t.d(e,{I3:()=>s,VR:()=>l,a9:()=>r,db:()=>n,l_:()=>a,qW:()=>d,yL:()=>o})}}]);
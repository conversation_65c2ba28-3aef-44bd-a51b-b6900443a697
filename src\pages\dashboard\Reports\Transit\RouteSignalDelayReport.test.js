import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import RouteSignalDelayReport from './RouteSignalDelayReport';
import ApiService from '../../../../utils/ApiService';

// Mock the ApiService
jest.mock('../../../../utils/ApiService');

// Mock Mapbox GL JS
jest.mock('mapbox-gl', () => ({
    accessToken: '',
    Map: jest.fn(() => ({
        addControl: jest.fn(),
        remove: jest.fn(),
        fitBounds: jest.fn(),
    })),
    NavigationControl: jest.fn(),
    Marker: jest.fn(() => ({
        setLngLat: jest.fn().mockReturnThis(),
        setPopup: jest.fn().mockReturnThis(),
        addTo: jest.fn().mockReturnThis(),
        remove: jest.fn(),
    })),
    Popup: jest.fn(() => ({
        setHTML: jest.fn().mockReturnThis(),
    })),
    LngLatBounds: jest.fn(() => ({
        extend: jest.fn(),
    })),
}));

const mockProps = {
    routeId: '1S',
    headsign: 'Main Line to Downtown',
    startDate: new Date('2025-05-14T06:00:00.000Z'),
    endDate: new Date('2025-05-14T14:59:59.000Z'),
    routeName: 'Route 1S',
    onLoadComplete: jest.fn()
};

const mockApiResponse = {
    success: true,
    data: {
        routeId: '1S',
        headsign: 'Main Line to Downtown',
        signals: [
            {
                signalId: 123,
                signalName: 'Main St & First Ave',
                latitude: 46.501269,
                longitude: -81.036213,
                averageRedLightDelay: 15.5,
                vehicleCount: 10
            },
            {
                signalId: 124,
                signalName: 'Main St & Second Ave',
                latitude: 46.502269,
                longitude: -81.037213,
                averageRedLightDelay: 8.2,
                vehicleCount: 7
            }
        ]
    }
};

describe('RouteSignalDelayReport', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        // Mock environment variable
        process.env.REACT_APP_MAPBOX_ACCESS_TOKEN = 'test-token';
    });

    test('renders loading state initially', () => {
        ApiService.getRouteSignalDelay.mockImplementation(() => new Promise(() => {}));
        
        render(<RouteSignalDelayReport {...mockProps} />);
        
        expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    test('renders map and signal summary with data successfully', async () => {
        ApiService.getRouteSignalDelay.mockResolvedValue(mockApiResponse);
        
        render(<RouteSignalDelayReport {...mockProps} />);
        
        await waitFor(() => {
            expect(screen.getByText('Route Signal Delay')).toBeInTheDocument();
        });

        expect(screen.getByText('Route 1S - Main Line to Downtown')).toBeInTheDocument();
        expect(screen.getByText('Signal Summary (2 signals)')).toBeInTheDocument();
        expect(screen.getByText('Main St & First Ave')).toBeInTheDocument();
        expect(screen.getByText('Main St & Second Ave')).toBeInTheDocument();
        expect(screen.getByText('Avg Delay: 15.5s')).toBeInTheDocument();
        expect(screen.getByText('Vehicles: 10')).toBeInTheDocument();
    });

    test('renders error state when API fails', async () => {
        const errorResponse = {
            success: false,
            errorMessage: 'Failed to fetch data'
        };
        
        ApiService.getRouteSignalDelay.mockResolvedValue(errorResponse);
        
        render(<RouteSignalDelayReport {...mockProps} />);
        
        await waitFor(() => {
            expect(screen.getByText('Failed to fetch data')).toBeInTheDocument();
        });
    });

    test('renders no data message when signals array is empty', async () => {
        const noDataResponse = {
            success: true,
            data: {
                routeId: '1S',
                headsign: 'Main Line to Downtown',
                signals: []
            }
        };
        
        ApiService.getRouteSignalDelay.mockResolvedValue(noDataResponse);
        
        render(<RouteSignalDelayReport {...mockProps} />);
        
        await waitFor(() => {
            expect(screen.getByText('No signal data available for the selected time range')).toBeInTheDocument();
        });
    });

    test('calls API with correct parameters', async () => {
        ApiService.getRouteSignalDelay.mockResolvedValue(mockApiResponse);
        
        render(<RouteSignalDelayReport {...mockProps} />);
        
        await waitFor(() => {
            expect(ApiService.getRouteSignalDelay).toHaveBeenCalledWith(
                '1S',
                'Main Line to Downtown',
                expect.stringContaining('2025-05-14T06:00:00.000Z'),
                expect.stringContaining('2025-05-14T14:59:59.000Z')
            );
        });
    });

    test('calls onLoadComplete when data loads successfully', async () => {
        ApiService.getRouteSignalDelay.mockResolvedValue(mockApiResponse);
        
        render(<RouteSignalDelayReport {...mockProps} />);
        
        await waitFor(() => {
            expect(mockProps.onLoadComplete).toHaveBeenCalled();
        });
    });

    test('displays legend information', async () => {
        ApiService.getRouteSignalDelay.mockResolvedValue(mockApiResponse);
        
        render(<RouteSignalDelayReport {...mockProps} />);
        
        await waitFor(() => {
            expect(screen.getByText(/Circle size and color indicate average red light delay/)).toBeInTheDocument();
        });
    });
});

import { useNavigate } from '../../node_modules/react-router-dom/dist/index';
import { redirect } from '../../node_modules/react-router/dist/index';

let accessToken = undefined;
let sessionUser = undefined;
export function getAuthToken() {
    return accessToken;
}

export function setAuthToken(Token) {
    accessToken = Token;
    //console.log(accessToken);
}

export function getSessionUser() {
    return sessionUser;
}

export function setSessionUser(user) {
    sessionUser = user;
}

export function CheckAuthToken() {
    const token = getAuthToken();

    if (!token) {
        return redirect('/');
    }
    return null;
}

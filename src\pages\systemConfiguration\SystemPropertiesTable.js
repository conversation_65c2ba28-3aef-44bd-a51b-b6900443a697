import React, { useState, useEffect } from 'react';
import MUIDataTable from 'components/mui-datatable';
import { Button, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle } from '@mui/material';
import { getAuthToken } from 'utils/auth';

const SystemPropertiesTable = () => {
    const [properties, setProperties] = useState([]);
    const [openDialog, setOpenDialog] = useState(false); // State to control the opening and closing of the dialog
    const token = getAuthToken();

    useEffect(() => {
        fetch(`${process.env.REACT_APP_API_ENDPOINT}/systemProperties/all`, {
            method: 'GET',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        })
            .then((response) => response.json())
            .then((data) => {
                setProperties(data);
            })
            .catch((error) => {
                console.error('Error fetching system properties', error);
            });
    }, [token]);

    const updateProperty = (propertyData) => {
        fetch(`${process.env.REACT_APP_API_ENDPOINT}/systemProperties/update`, {
            method: 'PUT',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(propertyData)
        })
            .then((response) => {
                if (response.ok) {
                    console.log('Property updated successfully');
                    setOpenDialog(true); // Open the dialog when update is successful
                } else {
                    console.error('Error updating property', response.statusText);
                }
            })
            .catch((error) => {
                console.error('Error updating property', error);
            });
    };

    const options = {
        selectableRows: false,
        responsive: 'standard'
    };

    const columns = [
        {
            name: 'name',
            label: 'Name'
        },
        {
            name: 'value',
            label: 'Value',
            options: {
                customBodyRender: (value, tableMeta, updateValue) => {
                    if (tableMeta.rowData[0] === 'runDbCheckCron') {
                        // If the name is "runDbCheckCron", just display the value without the input and button.
                        return value;
                    }

                    return (
                        <>
                            <input type="text" value={value} onChange={(e) => updateValue(e.target.value)} />
                            <Button
                                onClick={() =>
                                    updateProperty({
                                        name: tableMeta.rowData[0],
                                        value: tableMeta.rowData[1]
                                    })
                                }
                            >
                                Update
                            </Button>
                        </>
                    );
                }
            }
        },
        {
            name: 'description',
            label: 'Description'
        }
    ];

    return (
        <>
            <MUIDataTable title={'System Properties'} data={properties} columns={columns} options={options} />
            <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
                <DialogTitle>{'Update Successful'}</DialogTitle>
                <DialogContent>
                    <DialogContentText>The system property was updated successfully.</DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setOpenDialog(false)} color="primary">
                        Okay
                    </Button>
                </DialogActions>
            </Dialog>
        </>
    );
};

export default SystemPropertiesTable;

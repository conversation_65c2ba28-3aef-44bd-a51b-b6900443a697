// material-ui
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Typography } from '@mui/material';
import SystemPropertiesTable from './SystemPropertiesTable';
import DbCheckSchedule from './DbCheckSchedule';

// project import
import MainCard from 'components/MainCard';

// ==============================|| COMPONENTS - TYPOGRAPHY ||============================== //

const ComponentTypography = () => (
    <Grid container spacing={3}>
        {/* row 1 */}

        <Grid item xs={12} lg={12}>
            <Stack spacing={3}>
                <MainCard title="GTFS Feed Update Schedule">
                    <DbCheckSchedule />
                </MainCard>
                <SystemPropertiesTable />
            </Stack>
        </Grid>
    </Grid>
);

export default ComponentTypography;

"use strict";(self.webpackChunkmantis_free_react_admin_template=self.webpackChunkmantis_free_react_admin_template||[]).push([[344],{33344:(e,t,s)=>{s.d(t,{Ay:()=>ut});var o=s(35661),i=s(397),a=s(53065),n=s(48576),l=s(72004),r=s(78353),d=s.n(r),c=s(73223),p=s.n(c),h=s(19411),u=s.n(h),m=s(51812),x=s.n(m),f=s(82280),b=s.n(f),g=s(5544),w=s.n(g),y=s(9950),R=s(87233),v=s(22057),C=s(68605),S=s(44414);const T=(0,n.makeStyles)({name:"MUIDataTableBodyCell"})((e=>({root:{},cellHide:{display:"none"},simpleHeader:{[e.breakpoints.down("sm")]:{display:"inline-block",fontWeight:"bold",width:"100%",boxSizing:"border-box"}},simpleCell:{[e.breakpoints.down("sm")]:{display:"inline-block",width:"100%",boxSizing:"border-box"}},stackedHeader:{verticalAlign:"top"},stackedCommon:{[e.breakpoints.down("md")]:{display:"inline-block",fontSize:"16px",height:"auto",width:"calc(50%)",boxSizing:"border-box","&:last-child":{borderBottom:"none"},"&:nth-last-of-type(2)":{borderBottom:"none"}}},stackedCommonAlways:{display:"inline-block",fontSize:"16px",height:"auto",width:"calc(50%)",boxSizing:"border-box","&:last-child":{borderBottom:"none"},"&:nth-last-of-type(2)":{borderBottom:"none"}},stackedParent:{[e.breakpoints.down("md")]:{display:"inline-block",fontSize:"16px",height:"auto",width:"calc(100%)",boxSizing:"border-box"}},stackedParentAlways:{display:"inline-block",fontSize:"16px",height:"auto",width:"calc(100%)",boxSizing:"border-box"},cellStackedSmall:{[e.breakpoints.down("md")]:{width:"50%",boxSizing:"border-box"}},responsiveStackedSmall:{[e.breakpoints.down("md")]:{width:"50%",boxSizing:"border-box"}},responsiveStackedSmallParent:{[e.breakpoints.down("md")]:{width:"100%",boxSizing:"border-box"}}})));const A=function(e){const{classes:t}=T(),{children:s,colIndex:o,columnHeader:i,options:a,dataIndex:n,rowIndex:r,className:d,print:c,tableId:p,...h}=e,u=a.onCellClick,m=(0,y.useCallback)((e=>{u(s,{colIndex:o,rowIndex:r,dataIndex:n,event:e})}),[u,s,o,r,n]);let x={};u&&(x.onClick=m);let f=[(0,S.jsx)("div",{className:(0,l.A)({lastColumn:2===o,[t.root]:!0,[t.cellHide]:!0,[t.stackedHeader]:!0,[t.stackedCommon]:"vertical"===a.responsive||"stacked"===a.responsive||"stackedFullWidth"===a.responsive,[t.stackedCommonAlways]:"verticalAlways"===a.responsive,[t.cellStackedSmall]:"stacked"===a.responsive||"stackedFullWidth"===a.responsive&&("none"===a.setTableProps().padding||"small"===a.setTableProps().size),[t.simpleHeader]:"simple"===a.responsive,"datatables-noprint":!c},d),children:i},1),(0,S.jsx)("div",{className:(0,l.A)({[t.root]:!0,[t.stackedCommon]:"vertical"===a.responsive||"stacked"===a.responsive||"stackedFullWidth"===a.responsive,[t.stackedCommonAlways]:"verticalAlways"===a.responsive,[t.responsiveStackedSmall]:"stacked"===a.responsive||"stackedFullWidth"===a.responsive&&("none"===a.setTableProps().padding||"small"===a.setTableProps().size),[t.simpleCell]:"simple"===a.responsive,"datatables-noprint":!c},d),children:"function"===typeof s?s(n,r):s},2)];var b;return b=-1!==["standard","scrollMaxHeight","scrollFullHeight","scrollFullHeightFullWidth"].indexOf(a.responsive)?f.slice(1,2):f,(0,S.jsx)(C.A,{...x,"data-colindex":o,"data-tableid":p,className:(0,l.A)({[t.root]:!0,[t.stackedParent]:"vertical"===a.responsive||"stacked"===a.responsive||"stackedFullWidth"===a.responsive,[t.stackedParentAlways]:"verticalAlways"===a.responsive,[t.responsiveStackedSmallParent]:"vertical"===a.responsive||"stacked"===a.responsive||"stackedFullWidth"===a.responsive&&("none"===a.setTableProps().padding||"small"===a.setTableProps().size),[t.simpleCell]:"simple"===a.responsive,"datatables-noprint":!c},d),...h,children:b})};var k=s(10371);class I extends y.Component{render(){const{classes:e,options:t,rowSelected:s,onClick:o,className:i,isRowSelectable:a,...n}=this.props;var r={};return o&&(r.onClick=o),(0,S.jsx)(k.A,{hover:!!t.rowHover,...r,className:(0,l.A)({[e.root]:!0,[e.hover]:t.rowHover,[e.hoverCursor]:t.selectableRowsOnClick&&a||t.expandableRowsOnClick,[e.responsiveSimple]:"simple"===t.responsive,[e.responsiveStacked]:"vertical"===t.responsive||"stacked"===t.responsive||"stackedFullWidth"===t.responsive,"mui-row-selected":s},i),selected:s,...n,children:this.props.children})}}const O=(0,n.withStyles)(I,(e=>({root:{"&.Mui-selected":{backgroundColor:e.palette.action.selected},"&.mui-row-selected":{backgroundColor:e.palette.action.selected}},hoverCursor:{cursor:"pointer"},responsiveStacked:{[e.breakpoints.down("md")]:{borderTop:"solid 2px rgba(0, 0, 0, 0.15)",borderBottom:"solid 2px rgba(0, 0, 0, 0.15)",padding:0,margin:0}},responsiveSimple:{[e.breakpoints.down("sm")]:{borderTop:"solid 2px rgba(0, 0, 0, 0.15)",borderBottom:"solid 2px rgba(0, 0, 0, 0.15)",padding:0,margin:0}}})),{name:"MUIDataTableBodyRow"});var D=s(94832),j=s(57073),P=s(3199),L=s(98523);const F=e=>{let{areAllRowsExpanded:t,buttonClass:s,expandableRowsHeader:o,expandedRows:i,iconClass:a,iconIndeterminateClass:n,isHeaderCell:l,onExpand:r}=e;return(0,S.jsx)(S.Fragment,{children:l&&!t()&&t&&i.data.length>0?(0,S.jsx)(j.A,{onClick:r,style:{padding:0},disabled:!1===o,className:s,children:(0,S.jsx)(L.default,{id:"expandable-button",className:n})}):(0,S.jsx)(j.A,{onClick:r,style:{padding:0},disabled:!1===o,className:s,children:(0,S.jsx)(P.default,{id:"expandable-button",className:a})})})},H=(0,n.makeStyles)({name:"MUIDataTableSelectCell"})((e=>({root:{"@media print":{display:"none"}},fixedHeader:{position:"sticky",top:"0px",zIndex:100},fixedLeft:{position:"sticky",left:"0px",zIndex:100},icon:{cursor:"pointer",transition:"transform 0.25s"},expanded:{transform:"rotate(90deg)"},hide:{visibility:"hidden"},headerCell:{zIndex:110,backgroundColor:e.palette.background.paper},expandDisabled:{},checkboxRoot:{},checked:{},disabled:{}}))),E=e=>{let{fixedHeader:t,fixedSelectColumn:s,isHeaderCell:o=!1,expandableOn:i=!1,selectableOn:a="none",isRowExpanded:n=!1,onExpand:r,isRowSelectable:d,selectableRowsHeader:c,hideExpandButton:p,expandableRowsHeader:h,expandedRows:u,areAllRowsExpanded:m=()=>!1,selectableRowsHideCheckboxes:x,setHeadCellRef:f,dataIndex:b,components:g={},...w}=e;const{classes:y}=H(),R=g.Checkbox||D.A,v=g.ExpandButton||F;if(!1===i&&("none"===a||!0===x))return null;const T=(0,l.A)({[y.root]:!0,[y.fixedHeader]:t&&o,[y.fixedLeft]:s,[y.headerCell]:o}),A=(0,l.A)({[y.expandDisabled]:p}),k=(0,l.A)({[y.icon]:!0,[y.hide]:o&&!h,[y.expanded]:n||o&&m()}),I=(0,l.A)({[y.icon]:!0,[y.hide]:o&&!h});let O={};f&&(O.ref=e=>{f(0,0,e)});return(0,S.jsx)(C.A,{className:T,padding:"checkbox",...O,children:(0,S.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[i&&(0,S.jsx)(v,{isHeaderCell:o,areAllRowsExpanded:m,expandedRows:u,onExpand:r,expandableRowsHeader:h,buttonClass:A,iconIndeterminateClass:I,iconClass:k,dataIndex:b}),"none"!==a&&!0!==x&&(!o||"multiple"===a&&!1!==c?(0,S.jsx)(R,{classes:{root:y.checkboxRoot,checked:y.checked,disabled:y.disabled},"data-description":o?"row-select-header":"row-select","data-index":b||null,color:"primary",disabled:!d,...w}):null)]})})};function N(e){return e.reduce(((e,t)=>{let{dataIndex:s}=t;return e[s]=!0,e}),{})}function z(e){return"string"===typeof e?e.replace(/^\+|^\-|^\=|^\@/g,"'$&"):e}function W(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],s="function"===typeof t?t:console.warn;t&&s(`${e}`)}function U(e,t,s){const o=e<=t?1:Math.ceil(e/t);return s>=o?o-1:s}function M(){if(Intl){return new Intl.Collator(void 0,{numeric:!0,sensitivity:"base"}).compare}return(e,t)=>e.localeCompare(t)}function B(e){return(t,s)=>{var o=null===t.data||"undefined"===typeof t.data?"":t.data,i=null===s.data||"undefined"===typeof s.data?"":s.data;return("function"===typeof o.localeCompare?o.localeCompare(i):o-i)*("asc"===e?1:-1)}}function _(e,t){const s=new Blob([e],{type:"text/csv"});if(navigator&&navigator.msSaveOrOpenBlob)navigator.msSaveOrOpenBlob(s,t);else{const o=`data:text/csv;charset=utf-8,${e}`,i=window.URL||window.webkitURL,a="undefined"===typeof i.createObjectURL?o:i.createObjectURL(s);let n=document.createElement("a");n.setAttribute("href",a),n.setAttribute("download",t),document.body.appendChild(n),n.click(),document.body.removeChild(n)}}function V(e,t,s,o){const i=function(e,t,s){const o=e=>"string"===typeof e?e.replace(/\"/g,'""'):e,i=e=>e.reduce(((e,t)=>t.download?e+'"'+z(o(t.label||t.name))+'"'+s.downloadOptions.separator:e),"").slice(0,-1)+"\r\n",a=i(e),n=t=>t.length?t.reduce(((t,i)=>t+'"'+i.data.filter(((t,s)=>e[s].download)).map((e=>z(o(e)))).join('"'+s.downloadOptions.separator+'"')+'"\r\n'),"").trim():"",l=n(t);return s.onDownload?s.onDownload(i,n,e,t):`${a}${l}`.trim()}(e,t,s);s.onDownload&&!1===i||o(i,s.downloadOptions.filename)}class $ extends y.Component{constructor(){super(...arguments),this.handleRowSelect=(e,t)=>{let s=!(!t||!t.nativeEvent)&&t.nativeEvent.shiftKey,o=[],i=this.props.previousSelectedRow;if(s&&i&&i.index<this.props.data.length){let t=i.index,s=p()(this.props.selectedRows),a=this.props.data[e.index].dataIndex;for(0===s.data.filter((e=>e.dataIndex===a)).length&&(s.data.push({index:e.index,dataIndex:a}),s.lookup[a]=!0);t!==e.index;){let i=this.props.data[t].dataIndex;if(this.isRowSelectable(i,s)){let e={index:t,dataIndex:i};0===s.data.filter((e=>e.dataIndex===i)).length&&(s.data.push(e),s.lookup[i]=!0),o.push(e)}t=e.index>t?t+1:t-1}}this.props.selectRowUpdate("cell",e,o)},this.handleRowClick=(e,t,s)=>{if("expandable-button"!==s.target.id&&("path"!==s.target.nodeName||"expandable-button"!==s.target.parentNode.id)&&(!s.target.id||!s.target.id.startsWith("MUIDataTableSelectCell"))){if(this.props.options.selectableRowsOnClick&&"none"!==this.props.options.selectableRows&&this.isRowSelectable(t.dataIndex,this.props.selectedRows)){const e={index:t.rowIndex,dataIndex:t.dataIndex};this.handleRowSelect(e,s)}if(this.props.options.expandableRowsOnClick&&this.props.options.expandableRows&&this.isRowExpandable(t.dataIndex,this.props.expandedRows)){const e={index:t.rowIndex,dataIndex:t.dataIndex};this.props.toggleExpandRow(e)}this.props.options.selectableRowsOnClick||this.props.options.onRowClick&&this.props.options.onRowClick(e,t,s)}},this.processRow=(e,t)=>{let s=[];for(let o=0;o<e.length;o++)s.push({value:e[t[o]],index:t[o]});return s}}buildRows(){const{data:e,page:t,rowsPerPage:s,count:o}=this.props;if(this.props.options.serverSide)return e.length?e:null;let i=[];const a=U(o,s,t),n=0===a?0:a*s,l=Math.min(o,(a+1)*s);t>a&&console.warn("Current page is out of range, using the highest page that is in range instead.");for(let r=n;r<o&&r<l;r++)void 0!==e[r]&&i.push(e[r]);return i.length?i:null}getRowIndex(e){const{page:t,rowsPerPage:s,options:o}=this.props;if(o.serverSide)return e;return(0===t?0:t*s)+e}isRowSelected(e){const{selectedRows:t}=this.props;return!(!t.lookup||!t.lookup[e])}isRowExpanded(e){const{expandedRows:t}=this.props;return!(!t.lookup||!t.lookup[e])}isRowSelectable(e,t){const{options:s}=this.props;return t=t||this.props.selectedRows,!s.isRowSelectable||s.isRowSelectable(e,t)}isRowExpandable(e){const{options:t,expandedRows:s}=this.props;return!t.isRowExpandable||t.isRowExpandable(e,s)}render(){const{classes:e,columns:t,toggleExpandRow:s,options:o,columnOrder:i=this.props.columns.map(((e,t)=>t)),components:a={},tableId:n}=this.props,r=this.buildRows(),d=t.filter((e=>"true"===e.display)).length;return(0,S.jsx)(v.A,{children:r&&r.length>0?r.map(((r,d)=>{const{data:c,dataIndex:p}=r;if(o.customRowRender)return o.customRowRender(c,p,d);let h="none"!==o.selectableRows&&this.isRowSelected(p),u=this.isRowSelectable(p),m=o.setRowProps&&o.setRowProps(c,p,d)||{};const x=this.processRow(c,i);return(0,S.jsxs)(y.Fragment,{children:[(0,S.jsxs)(O,{...m,options:o,rowSelected:h,isRowSelectable:u,onClick:this.handleRowClick.bind(null,c,{rowIndex:d,dataIndex:p}),className:(0,l.A)({[e.lastStackedCell]:"vertical"===o.responsive||"stacked"===o.responsive||"stackedFullWidth"===o.responsive,[e.lastSimpleCell]:"simple"===o.responsive,[m.className]:m.className}),"data-testid":"MUIDataTableBodyRow-"+p,id:`MUIDataTableBodyRow-${n}-${p}`,children:[(0,S.jsx)(E,{onChange:this.handleRowSelect.bind(null,{index:this.getRowIndex(d),dataIndex:p}),onExpand:s.bind(null,{index:this.getRowIndex(d),dataIndex:p}),fixedHeader:o.fixedHeader,fixedSelectColumn:o.fixedSelectColumn,checked:h,expandableOn:o.expandableRows,hideExpandButton:!this.isRowExpandable(p)&&o.expandableRows,selectableOn:o.selectableRows,selectableRowsHideCheckboxes:o.selectableRowsHideCheckboxes,isRowExpanded:this.isRowExpanded(p),isRowSelectable:u,dataIndex:p,id:`MUIDataTableSelectCell-${n}-${p}`,components:a}),x.map((e=>"true"===t[e.index].display&&(0,y.createElement)(A,{...t[e.index].setCellProps&&t[e.index].setCellProps(e.value,p,e.index)||{},"data-testid":`MuiDataTableBodyCell-${e.index}-${d}`,dataIndex:p,rowIndex:d,colIndex:e.index,columnHeader:t[e.index].label,print:t[e.index].print,options:o,tableId:n,key:e.index},e.value)))]}),this.isRowExpanded(p)&&o.renderExpandableRow(c,{rowIndex:d,dataIndex:p})]},d)})):(0,S.jsx)(O,{options:o,children:(0,S.jsx)(A,{colSpan:"none"!==o.selectableRows||o.expandableRows?d+1:d,options:o,colIndex:0,rowIndex:0,children:(0,S.jsx)(R.A,{variant:"body1",className:e.emptyTitle,component:"div",children:o.textLabels.body.noMatch})})})})}}$.defaultProps={toggleExpandRow:()=>{}};const K=(0,n.withStyles)($,(e=>({root:{},emptyTitle:{textAlign:"center"},lastStackedCell:{[e.breakpoints.down("md")]:{"& td:last-child":{borderBottom:"none"}}},lastSimpleCell:{[e.breakpoints.down("sm")]:{"& td:last-child":{borderBottom:"none"}}}})),{name:"MUIDataTableBody"});var X=s(37598),J=s(72145),q=s(7233),G=s(30609),Y=s(4139),Q=s(82321),Z=s(4904),ee=s(83563),te=s(27987),se=s(72534),oe=s(52983);class ie extends y.Component{constructor(e){super(e),this.filterUpdate=(e,t,s,o,i)=>{let a=this.state.filterList.slice(0);this.props.updateFilterByType(a,e,t,o,i),this.setState({filterList:a})},this.handleCheckboxChange=(e,t,s)=>{this.filterUpdate(e,t,s,"checkbox"),!0!==this.props.options.confirmFilters&&this.props.onFilterUpdate(e,t,s,"checkbox")},this.handleDropdownChange=(e,t,s)=>{const o=this.props.options.textLabels.filter.all,i=e.target.value===o?[]:[e.target.value];this.filterUpdate(t,i,s,"dropdown"),!0!==this.props.options.confirmFilters&&this.props.onFilterUpdate(t,i,s,"dropdown")},this.handleMultiselectChange=(e,t,s)=>{this.filterUpdate(e,t,s,"multiselect"),!0!==this.props.options.confirmFilters&&this.props.onFilterUpdate(e,t,s,"multiselect")},this.handleTextFieldChange=(e,t,s)=>{this.filterUpdate(t,e.target.value,s,"textField"),!0!==this.props.options.confirmFilters&&this.props.onFilterUpdate(t,e.target.value,s,"textField")},this.handleCustomChange=(e,t,s)=>{this.filterUpdate(t,e,s.name,s.filterType),!0!==this.props.options.confirmFilters&&this.props.onFilterUpdate(t,e,s.name,s.filterType)},this.applyFilters=()=>(this.state.filterList.forEach(((e,t)=>{this.props.onFilterUpdate(t,e,this.props.columns[t],"custom")})),this.props.handleClose(),this.props.options.onFilterConfirm&&this.props.options.onFilterConfirm(this.state.filterList),this.state.filterList),this.resetFilters=()=>{this.setState({filterList:this.props.columns.map((()=>[]))}),!0!==this.props.options.confirmFilters&&this.props.onFilterReset()},this.state={filterList:p()(e.filterList)}}renderCheckbox(e,t){const s=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).Checkbox||D.A,{classes:o,filterData:i}=this.props,{filterList:a}=this.state,n=e.filterOptions&&e.filterOptions.renderValue?e.filterOptions.renderValue:e=>e;return(0,S.jsx)(Y.Ay,{item:!0,xs:6,children:(0,S.jsxs)(G.A,{children:[(0,S.jsx)(Y.Ay,{item:!0,xs:12,children:(0,S.jsx)(R.A,{variant:"body2",className:o.checkboxListTitle,children:e.label})}),(0,S.jsx)(Y.Ay,{container:!0,children:i[t].map(((i,l)=>(0,S.jsx)(Y.Ay,{item:!0,children:(0,S.jsx)(q.A,{classes:{root:o.checkboxFormControl,label:o.checkboxFormControlLabel},control:(0,S.jsx)(s,{"data-description":"table-filter",color:"primary",className:o.checkboxIcon,onChange:this.handleCheckboxChange.bind(null,t,i,e.name),checked:a[t].indexOf(i)>=0,classes:{root:o.checkbox,checked:o.checked},value:null!=i?i.toString():""}),label:n(i)},l)},l)))})]})},t)}renderSelect(e,t){const{classes:s,filterData:o,options:i}=this.props,{filterList:a}=this.state,n=i.textLabels.filter,l=e.filterOptions&&e.filterOptions.renderValue?e.filterOptions.renderValue:e=>null!=e?e.toString():"",r=!0===(e.filterOptions&&e.filterOptions.fullWidth)?12:6;return(0,S.jsx)(Y.Ay,{item:!0,xs:r,classes:{"grid-xs-12":s.gridListTile,"grid-xs-6":s.gridListTile},children:(0,S.jsxs)(J.A,{variant:"standard",fullWidth:!0,children:[(0,S.jsx)(Z.A,{htmlFor:e.name,children:e.label}),(0,S.jsxs)(se.A,{fullWidth:!0,value:a[t].length?a[t].toString():n.all,name:e.name,onChange:s=>this.handleDropdownChange(s,t,e.name),input:(0,S.jsx)(Q.A,{name:e.name,id:e.name}),children:[(0,S.jsx)(te.A,{value:n.all,children:n.all},0),o[t].map(((e,t)=>(0,S.jsx)(te.A,{value:e,children:l(e)},t+1)))]})]},t)},t)}renderTextField(e,t){const{classes:s}=this.props,{filterList:o}=this.state;e.filterOptions&&e.filterOptions.renderValue&&console.warn("Custom renderValue not supported for textField filters");const i=!0===(e.filterOptions&&e.filterOptions.fullWidth)?12:6;return(0,S.jsx)(Y.Ay,{item:!0,xs:i,classes:{"grid-xs-12":s.gridListTile,"grid-xs-6":s.gridListTile},children:(0,S.jsx)(J.A,{fullWidth:!0,children:(0,S.jsx)(oe.A,{fullWidth:!0,variant:"standard",label:e.label,value:o[t].toString()||"","data-testid":"filtertextfield-"+e.name,onChange:s=>this.handleTextFieldChange(s,t,e.name)})},t)},t)}renderMultiselect(e,t){const s=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).Checkbox||D.A,{classes:o,filterData:i}=this.props,{filterList:a}=this.state,n=e.filterOptions&&e.filterOptions.renderValue?e.filterOptions.renderValue:e=>e,l=!0===(e.filterOptions&&e.filterOptions.fullWidth)?12:6;return(0,S.jsx)(Y.Ay,{item:!0,xs:l,classes:{"grid-xs-12":o.gridListTile,"grid-xs-6":o.gridListTile},children:(0,S.jsxs)(J.A,{variant:"standard",fullWidth:!0,children:[(0,S.jsx)(Z.A,{htmlFor:e.name,children:e.label}),(0,S.jsx)(se.A,{multiple:!0,fullWidth:!0,value:a[t]||[],renderValue:e=>e.map(n).join(", "),name:e.name,onChange:s=>this.handleMultiselectChange(t,s.target.value,e.name),input:(0,S.jsx)(Q.A,{name:e.name,id:e.name}),children:i[t].map(((e,i)=>(0,S.jsxs)(te.A,{value:e,children:[(0,S.jsx)(s,{"data-description":"table-filter",color:"primary",checked:a[t].indexOf(e)>=0,value:null!=e?e.toString():"",className:o.checkboxIcon,classes:{root:o.checkbox,checked:o.checked}}),(0,S.jsx)(ee.A,{primary:n(e)})]},i+1)))})]},t)},t)}renderCustomField(e,t){const{classes:s,filterData:o,options:i}=this.props,{filterList:a}=this.state,n=!0===(e.filterOptions&&e.filterOptions.fullWidth)?12:6,l=e.filterOptions&&e.filterOptions.display||i.filterOptions&&i.filterOptions.display;if(l)return e.filterListOptions&&e.filterListOptions.renderValue&&console.warning('"renderValue" is ignored for custom filter fields'),(0,S.jsx)(Y.Ay,{item:!0,xs:n,classes:{"grid-xs-12":s.gridListTile,"grid-xs-6":s.gridListTile},children:(0,S.jsx)(J.A,{fullWidth:!0,children:l(a,this.handleCustomChange,t,e,o)},t)},t);console.error('Property "display" is required when using custom filter type.')}render(){const{classes:e,columns:t,options:s,customFooter:o,filterList:i,components:a={}}=this.props,n=s.textLabels.filter;return(0,S.jsxs)("div",{className:e.root,children:[(0,S.jsxs)("div",{className:e.header,children:[(0,S.jsxs)("div",{className:e.reset,children:[(0,S.jsx)(R.A,{variant:"body2",className:(0,l.A)({[e.title]:!0}),children:n.title}),(0,S.jsx)(X.A,{color:"primary",className:e.resetLink,tabIndex:0,"aria-label":n.reset,"data-testid":"filterReset-button",onClick:this.resetFilters,children:n.reset})]}),(0,S.jsx)("div",{className:e.filtersSelected})]}),(0,S.jsx)(Y.Ay,{container:!0,direction:"row",justifyContent:"flex-start",alignItems:"center",spacing:4,children:t.map(((e,t)=>{if(e.filter){const o=e.filterType||s.filterType;return"checkbox"===o?this.renderCheckbox(e,t,a):"multiselect"===o?this.renderMultiselect(e,t,a):"textField"===o?this.renderTextField(e,t):"custom"===o?this.renderCustomField(e,t):this.renderSelect(e,t)}}))}),o?o(i,this.applyFilters):""]})}}const ae=(0,n.withStyles)(ie,(e=>({root:{backgroundColor:e.palette.background.default,padding:"24px 24px 36px 24px",fontFamily:"Roboto"},header:{flex:"0 0 auto",marginBottom:"16px",width:"100%",display:"flex",justifyContent:"space-between"},title:{display:"inline-block",marginLeft:"7px",color:e.palette.text.primary,fontSize:"14px",fontWeight:500},noMargin:{marginLeft:"0px"},reset:{alignSelf:"left"},resetLink:{marginLeft:"16px",fontSize:"12px",cursor:"pointer"},filtersSelected:{alignSelf:"right"},checkboxListTitle:{marginLeft:"7px",marginBottom:"8px",fontSize:"14px",color:e.palette.text.secondary,textAlign:"left",fontWeight:500},checkboxFormGroup:{marginTop:"8px"},checkboxFormControl:{margin:"0px"},checkboxFormControlLabel:{fontSize:"15px",marginLeft:"8px",color:e.palette.text.primary},checkboxIcon:{width:"32px",height:"32px"},checkbox:{},checked:{},gridListTile:{marginTop:"16px"}})),{name:"MUIDataTableFilter"});var ne=s(87299);const le=e=>{let{label:t,onDelete:s,className:o,filterProps:i}=e;return i=i||{},i.className&&(o=(0,l.A)(o,i.className)),(0,S.jsx)(ne.A,{label:t,onDelete:s,className:o,...i})},re=(0,n.makeStyles)({name:"MUIDataTableFilterList"})((()=>({root:{display:"flex",justifyContent:"left",flexWrap:"wrap",margin:"0px 16px 0px 16px"},chip:{margin:"8px 8px 0px 0px"}}))),de=e=>{let{options:t,filterList:s,filterUpdate:o,filterListRenderers:i,columnNames:a,serverSideFilterList:n,customFilterListUpdate:l,ItemComponent:r=le}=e;const{classes:d}=re(),{serverSide:c}=t,p=function(e,i,a,n){let l=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,r=i;Array.isArray(r)&&0===r.length&&(r=s[e]),o(e,i,a,n,l,(s=>{t.onFilterChipClose&&t.onFilterChipClose(e,r,s)}))},h=(e,s,o,i,n)=>{let c;return c=n?l[s]?"custom":"chip":a[s].filterType,(0,S.jsx)(r,{label:e,onDelete:()=>p(s,i[o]||[],a[s].name,c,l[s]),className:d.chip,itemKey:o,index:s,data:i,columnNames:a,filterProps:t.setFilterChipProps?t.setFilterChipProps(s,a[s].name,i[o]||[]):{}},o)},u=e=>e.map(((s,o)=>{if("custom"===a[o].filterType&&e[o].length){const e=i[o](s);return Array.isArray(e)?e.map(((e,t)=>h(e,o,t,s,!0))):h(e,o,o,s,!1)}return s.map(((e,s)=>((e,s,o)=>(0,S.jsx)(r,{label:i[e](s),onDelete:()=>p(e,s,a[e].name,"chip"),className:d.chip,itemKey:o,index:e,data:s,columnNames:a,filterProps:t.setFilterChipProps?t.setFilterChipProps(e,a[e].name,s):{}},o))(o,e,s)))}));return(0,S.jsx)("div",{className:d.root,children:u(c&&n?n:s)})};var ce=s(47041),pe=s(86547),he=s(22302),ue=s(34977);const me=(0,n.makeStyles)({name:"MUIDataTableJumpToPage"})((e=>({root:{color:e.palette.text.primary},caption:{flexShrink:0},selectRoot:{marginRight:32,marginLeft:8},select:{paddingTop:6,paddingBottom:7,paddingLeft:8,paddingRight:24,textAlign:"right",textAlignLast:"right",fontSize:e.typography.pxToRem(14)},selectIcon:{},input:{color:"inhert",fontSize:"inhert",flexShrink:0}})));const xe=function(e){const{classes:t}=me(),{count:s,textLabels:o,rowsPerPage:i,page:a,changePage:n}=e,r=o.pagination.jumpToPage;let d=[],c=Math.min(1e3,U(s,i,1e6));for(let l=0;l<=c;l++)d.push(l);const p=te.A;return(0,S.jsxs)(ue.A,{style:{display:"flex",minHeight:"52px",alignItems:"center"},className:t.root,children:[(0,S.jsx)(R.A,{color:"inherit",variant:"body2",className:t.caption,children:r}),(0,S.jsx)(se.A,{classes:{select:t.select,icon:t.selectIcon},input:(0,S.jsx)(he.Ay,{className:(0,l.A)(t.input,t.selectRoot)}),value:U(s,i,a),onChange:t=>{e.changePage(parseInt(t.target.value,10))},style:{marginRight:0},children:d.map((e=>(0,S.jsx)(p,{className:t.menuItem,value:e,children:e+1},e)))})]})},fe=(0,n.makeStyles)({name:"MUIDataTablePagination"})((e=>({root:{},tableCellContainer:{padding:"0px 24px 0px 24px"},navContainer:{display:"flex",justifyContent:"flex-end"},toolbar:{},selectRoot:{},"@media screen and (max-width: 400px)":{toolbar:{"& span:nth-of-type(2)":{display:"none"}},selectRoot:{marginRight:"8px"}}})));const be=function(e){const{classes:t}=fe(),{count:s,options:o,rowsPerPage:i,page:a}=e,n=o.textLabels.pagination;return(0,S.jsx)(ce.A,{children:(0,S.jsx)(k.A,{children:(0,S.jsx)(C.A,{colSpan:"1000",className:t.tableCellContainer,children:(0,S.jsxs)("div",{className:t.navContainer,children:[o.jumpToPage?(0,S.jsx)(xe,{count:s,page:a,rowsPerPage:i,textLabels:o.textLabels,changePage:e.changePage,changeRowsPerPage:e.changeRowsPerPage}):null,(0,S.jsx)(pe.A,{component:"div",className:t.root,classes:{caption:t.caption,toolbar:t.toolbar,selectRoot:t.selectRoot},count:s,rowsPerPage:i,page:U(s,i,a),labelRowsPerPage:n.rowsPerPage,labelDisplayedRows:e=>{let{from:t,to:s,count:o}=e;return`${t}-${s} ${n.displayRows} ${o}`},backIconButtonProps:{id:"pagination-back","data-testid":"pagination-back","aria-label":n.previous,title:n.previous||""},nextIconButtonProps:{id:"pagination-next","data-testid":"pagination-next","aria-label":n.next,title:n.next||""},SelectProps:{id:"pagination-input",SelectDisplayProps:{id:"pagination-rows","data-testid":"pagination-rows"},MenuProps:{id:"pagination-menu","data-testid":"pagination-menu",MenuListProps:{id:"pagination-menu-list","data-testid":"pagination-menu-list"}}},rowsPerPageOptions:o.rowsPerPageOptions,onPageChange:(t,s)=>{e.changePage(s)},onRowsPerPageChange:t=>{e.changeRowsPerPage(t.target.value)}})]})})})})},ge=(0,n.makeStyles)({name:"MUIDataTableFooter"})((()=>({root:{"@media print":{display:"none"}}}))),we=e=>{let{options:t,rowCount:s,page:o,rowsPerPage:a,changeRowsPerPage:n,changePage:l}=e;const{classes:r}=ge(),{customFooter:d,pagination:c=!0}=t;return d?(0,S.jsx)(i.A,{className:r.root,children:t.customFooter(s,o,a,n,l,t.textLabels.pagination)}):c?(0,S.jsx)(i.A,{className:r.root,children:(0,S.jsx)(be,{count:s,page:o,rowsPerPage:a,changeRowsPerPage:n,changePage:l,component:"div",options:t})}):null};var ye=s(24965),Re=s(13260),ve=s(66065),Ce=s(83564);const Se=(e,t,s)=>{let o=[],i=e[0]?e[0]:null;if(null===i){i={offsetLeft:1/0},Object.entries(e).forEach(((e,t)=>{let[s,o]=e;o&&o.offsetLeft<i.offsetLeft&&(i=o)})),i.offsetLeft===1/0&&(i={offsetParent:0,offsetWidth:0,offsetLeft:0})}let a=0,n=0,l=i.offsetParent;for(;l&&(n=n+(l.offsetLeft||0)-(l.scrollLeft||0),l=l.offsetParent,a++,!(a>1e3)););return e[0]&&(o[0]={left:n+i.offsetLeft,width:i.offsetWidth,columnIndex:null,ref:i}),t.forEach(((t,a)=>{let l=e[t+1],r=o.length-1;if(!s[t]||"true"===s[t].display){let e=-1!==r?o[r].left+o[r].width:n+i.offsetLeft;o.push({left:e,width:l.offsetWidth,columnIndex:t,ref:l})}})),o},Te=e=>{const[{isOver:t,canDrop:s},o]=(0,Ce.H)({accept:"HEADER",drop:"",hover:(t,s)=>(e=>{const{item:t,mon:s,index:o,headCellRefs:i,updateColumnOrder:a,columnOrder:n,transitionTime:l=300,tableRef:r,tableId:d,timers:c,columns:p}=e;let h=s.getItem().colIndex;if(i===s.getItem().headCellRefs&&h!==o){let e=((e,t,s)=>{let o=e.slice(),i=o.indexOf(t),a=o.indexOf(s);if(-1!==i&&-1!==a){let e=o[i];o=[...o.slice(0,i),...o.slice(i+1)],o=[...o.slice(0,a),e,...o.slice(a)]}return o})(n,s.getItem().colIndex,o),t=Se(i,e,p),h=s.getClientOffset().x,u=-1;for(let s=0;s<t.length;s++)if(h>t[s].left&&h<t[s].left+t[s].width){u=t[s].columnIndex;break}if(u===s.getItem().colIndex){clearTimeout(c.columnShift);let h=Se(i,n,p),u=[];t.forEach((e=>{u[e.columnIndex]=e.left})),h.forEach((e=>{u[e.columnIndex]=u[e.columnIndex]-e.left}));for(let e=1;e<n.length;e++){let t=n[e];p[t]&&"true"!==p[t].display||(i[e]&&(i[e].style.transition="280ms"),i[e]&&(i[e].style.transform="translateX("+u[e-1]+"px)"))}let m=[],x=[];for(let e=0;e<n.length;e++){let t=r?r.querySelectorAll('[data-colindex="'+e+'"][data-tableid="'+d+'"]'):[];for(let o=0;o<t.length;o++)t[o].style.transition=l+"ms",t[o].style.transform="translateX("+u[e]+"px)",m.push(t[o]);let s=r?r.querySelectorAll('[data-divider-index="'+(e+1)+'"][data-tableid="'+d+'"]'):[];for(let o=0;o<s.length;o++)s[o].style.transition=l+"ms",s[o].style.transform="translateX("+u[n[e]]+"px)",x.push(s[o])}let f=s.getItem().colIndex;c.columnShift=setTimeout((()=>{m.forEach((e=>{e.style.transition="0s",e.style.transform="translateX(0)"})),x.forEach((e=>{e.style.transition="0s",e.style.transform="translateX(0)"})),a(e,f,o)}),l)}}})(Object.assign({},e,{item:t,mon:s})),collect:e=>({isOver:!!e.isOver(),canDrop:!!e.canDrop()})});return[o]};var Ae=s(47017);const ke=(0,n.makeStyles)({name:"MUIDataTableHeadCell"})((e=>({root:{},fixedHeader:{position:"sticky",top:"0px",zIndex:100,backgroundColor:e.palette.background.paper},tooltip:{cursor:"pointer"},mypopper:{"&[data-x-out-of-boundaries]":{display:"none"}},data:{display:"inline-block"},sortAction:{display:"flex",cursor:"pointer"},dragCursor:{cursor:"grab"},sortLabelRoot:{height:"20px"},sortActive:{color:e.palette.text.primary},toolButton:{textTransform:"none",marginLeft:"-8px",minWidth:0,marginRight:"8px",paddingLeft:"8px",paddingRight:"8px"},contentWrapper:{display:"flex",alignItems:"center"},hintIconAlone:{marginTop:"-3px",marginLeft:"3px"},hintIconWithSortIcon:{marginTop:"-3px"}}))),Ie=e=>{let{cellHeaderProps:t={},children:s,colPosition:o,column:i,columns:n,columnOrder:r=[],components:d={},draggableHeadCellRefs:c,draggingHook:p,hint:h,index:u,options:m,print:x,setCellRef:f,sort:b,sortDirection:g,tableRef:w,tableId:R,timers:v,toggleSort:T,updateColumnOrder:A}=e;const[k,I]=(0,y.useState)(!1),[O,D]=(0,y.useState)(!1),{classes:j}=ke(),[P,L]=p||[],{className:F,...H}=t,E=d.Tooltip||a.A,N="none"!==g&&void 0!==g,z="none"!==g&&g,W=()=>!!p&&(m.draggableColumns&&m.draggableColumns.enabled&&!1!==i.draggable),U={classes:{root:j.sortLabelRoot},tabIndex:-1,active:N,hideSortIcon:!0,...z?{direction:g}:{}},[{opacity:M},B,_]=(0,Ae.i)({item:{type:"HEADER",colIndex:u,headCellRefs:c},begin:e=>(setTimeout((()=>{D(!1),I(!1),L(!0)}),0),null),end:(e,t)=>{L(!1)},collect:e=>({opacity:e.isDragging()?1:0})}),[V]=Te({drop:(e,t)=>{I(!1),D(!1),L(!1)},index:u,headCellRefs:c,updateColumnOrder:A,columnOrder:r,columns:n,transitionTime:m.draggableColumns?m.draggableColumns.transitionTime:300,tableRef:w?w():null,tableId:R||"none",timers:v}),$=(0,l.A)({[j.root]:!0,[j.fixedHeader]:m.fixedHeader,"datatables-noprint":!x,[F]:F});return(0,S.jsx)(C.A,{ref:e=>{V&&V(e),f&&f(u+1,o+1,e)},className:$,scope:"col",sortDirection:z,"data-colindex":u,"data-tableid":R,onMouseDown:()=>{I(!1)},...H,children:m.sort&&b?(0,S.jsxs)("span",{className:j.contentWrapper,children:[(0,S.jsx)(E,{title:P?"":m.textLabels?m.textLabels.body.columnHeaderTooltip?m.textLabels.body.columnHeaderTooltip(i):m.textLabels.body.toolTip:"",placement:"bottom",open:k,onOpen:()=>I(!P),onClose:()=>I(!1),classes:{tooltip:j.tooltip,popper:j.mypopper},children:(0,S.jsx)(X.A,{variant:"",onKeyUp:e=>("Enter"===e.key&&T(u),!1),onClick:()=>{T(u)},className:j.toolButton,"data-testid":`headcol-${u}`,ref:W()?B:null,children:(0,S.jsxs)("div",{className:j.sortAction,children:[(0,S.jsx)("div",{className:(0,l.A)({[j.data]:!0,[j.sortActive]:N,[j.dragCursor]:W()}),children:s}),(0,S.jsx)("div",{className:j.sortAction,children:(0,S.jsx)(ve.A,{...U})})]})})}),h&&(0,S.jsx)(E,{title:h,children:(0,S.jsx)(Re.default,{className:N?j.hintIconWithSortIcon:j.hintIconAlone,fontSize:"small"})})]}):(0,S.jsxs)("div",{className:h?j.sortAction:null,ref:W()?B:null,children:[s,h&&(0,S.jsx)(E,{title:h,placement:"bottom-end",open:O,onOpen:()=>(I(!1),void D(!0)),onClose:()=>D(!1),classes:{tooltip:j.tooltip,popper:j.mypopper},enterDelay:300,children:(0,S.jsx)(Re.default,{className:j.hintIconAlone,fontSize:"small"})})]})})},Oe=(0,n.makeStyles)({name:"MUIDataTableHeadRow"})((()=>({root:{}}))),De=e=>{let{children:t}=e;const{classes:s}=Oe();return(0,S.jsx)(k.A,{className:(0,l.A)({[s.root]:!0}),children:t})},je=(0,n.makeStyles)({name:"MUIDataTableHead"})((e=>({main:{},responsiveStacked:{[e.breakpoints.down("md")]:{display:"none"}},responsiveStackedAlways:{display:"none"},responsiveSimple:{[e.breakpoints.down("sm")]:{display:"none"}}}))),Pe=e=>{let{columnOrder:t=null,columns:s,components:o={},count:i,data:a,draggableHeadCellRefs:n,expandedRows:r,options:d,selectedRows:c,selectRowUpdate:p,setCellRef:h,sortOrder:u={},tableRef:m,tableId:x,timers:f,toggleAllExpandableRows:b,toggleSort:g,updateColumnOrder:w}=e;const{classes:R}=je();null===t&&(t=s?s.map(((e,t)=>t)):[]);const[v,C]=(0,y.useState)(!1),T=e=>{g(e)},A=c&&c.data.length||0;let k=A>0&&A<i,I=A>0&&A>=i;if(!0===d.disableToolbarSelect||"none"===d.selectToolbarPlacement||"above"===d.selectToolbarPlacement)if(I){for(let l=0;l<a.length;l++)if(!c.lookup[a[l].dataIndex]){I=!1,k=!0;break}}else A>i&&(k=!0);let O=t.map(((e,t)=>({column:s[e],index:e,colPos:t})));return(0,S.jsx)(ye.A,{className:(0,l.A)({[R.responsiveStacked]:"vertical"===d.responsive||"stacked"===d.responsive||"stackedFullWidth"===d.responsive,[R.responsiveStackedAlways]:"verticalAlways"===d.responsive,[R.responsiveSimple]:"simple"===d.responsive,[R.main]:!0}),children:(0,S.jsxs)(De,{children:[(0,S.jsx)(E,{setHeadCellRef:h,onChange:(()=>{p("head",null)}).bind(null),indeterminate:k,checked:I,isHeaderCell:!0,expandedRows:r,expandableRowsHeader:d.expandableRowsHeader,expandableOn:d.expandableRows,selectableOn:d.selectableRows,fixedHeader:d.fixedHeader,fixedSelectColumn:d.fixedSelectColumn,selectableRowsHeader:d.selectableRowsHeader,selectableRowsHideCheckboxes:d.selectableRowsHideCheckboxes,onExpand:b,isRowSelectable:!0,components:o}),O.map((e=>{let{column:i,index:a,colPos:l}=e;return"true"===i.display&&(i.customHeadRender?i.customHeadRender({index:a,...i},T,u):(0,S.jsx)(Ie,{cellHeaderProps:s[a].setCellHeaderProps&&s[a].setCellHeaderProps({index:a,...i})||{},index:a,colPosition:l,type:"cell",setCellRef:h,sort:i.sort,sortDirection:i.name===u.name?u.direction:"none",toggleSort:T,hint:i.hint,print:i.print,options:d,column:i,columns:s,updateColumnOrder:w,columnOrder:t,timers:f,draggingHook:[v,C],draggableHeadCellRefs:n,tableRef:m,tableId:x,components:o,children:i.customHeadLabelRender?i.customHeadLabelRender({index:a,colPos:l,...i}):i.label},a))}))]})})};function Le(e){let t=0,s=0,o=e.offsetParent;for(;o&&(s=s+(o.offsetLeft||0)-(o.scrollLeft||0),o=o.offsetParent,t++,!(t>1e3)););return s}class Fe extends y.Component{constructor(){super(...arguments),this.state={resizeCoords:{},priorPosition:{},tableWidth:"100%",tableHeight:"100%"},this.handleResize=()=>{window.innerWidth!==this.windowWidth&&(this.windowWidth=window.innerWidth,this.setDividers())},this.setCellRefs=(e,t)=>{this.cellsRef=e,this.tableRef=t,this.setDividers()},this.setDividers=()=>{const e=this.tableRef,{width:t,height:s}=e.getBoundingClientRect(),{resizeCoords:o}=this.state;for(let n in o)delete o[n];let i=Le(e),a=Object.entries(this.cellsRef);a.filter(((e,t)=>t+1<a.length)).forEach(((e,t)=>{let[s,a]=e;if(!a)return;let n=a.getBoundingClientRect().left;n=(n||0)-i;window.getComputedStyle(a,null);o[s]={left:n+a.offsetWidth}})),this.setState({tableWidth:t,tableHeight:s,resizeCoords:o},this.updateWidths)},this.updateWidths=()=>{let e=0;const{resizeCoords:t,tableWidth:s}=this.state;Object.entries(t).forEach((t=>{let[o,i]=t,a=Number((i.left-e)/s*100);"object"===typeof this.props.resizableColumns&&this.props.resizableColumns.roundWidthPercentages&&(a=a.toFixed(2)),e=i.left;const n=this.cellsRef[o];n&&(n.style.width=a+"%")}))},this.onResizeStart=(e,t)=>{const s=this.tableRef,o=s.style.width;let i=0;s.style.width="1px",Object.entries(this.cellsRef).forEach(((e,t)=>{let[s,o]=e,a=o?o.getBoundingClientRect():{width:0,left:0};this.minWidths[s]=a.width,i=Math.max(s,i)})),s.style.width=o,this.setState({isResize:!0,id:e,lastColumn:i})},this.onResizeMove=(e,t)=>{const{isResize:s,resizeCoords:o,lastColumn:i}=this.state,a=this.minWidths[e],n=this.minWidths[(e=>{let t=e+1,s=0;for(;"undefined"===typeof o[t]&&s<20;)t++,s++;return t})(parseInt(e,10))]||this.minWidths[e],l=parseInt(e,10),r=Object.entries(this.cellsRef),d=this.tableRef,{width:c,height:p}=d.getBoundingClientRect(),{selectableRows:h}=this.props.options;let u=Le(d);const m=e=>{let t=e+1,s=0;for(;"undefined"===typeof o[t]&&s<20;)t++,s++;return o[t]},x=e=>{let t=e-1;for(;"undefined"===typeof o[t]&&t>=0;)t--;return o[t]};if(s){let s=t.clientX-u;const d=(e,t,s)=>e>t-s?t-s:e,f=(e,t)=>e<t?t:e,b=(e,t,s,o)=>"undefined"===typeof m(s)?e:e>m(s).left-o?m(s).left-o:e,g=(e,t,s,o)=>"undefined"===typeof x(s)?e:e<x(s).left+o?x(s).left+o:e,w=(e,t)=>{let s=1;for(;!o[s]&&s<20;)s++;return"none"!==e&&0===t||"none"===e&&t===s},y=(e,t)=>e===(e=>{let t=e-1;for(;"undefined"===typeof o[t]&&t>=0;)t--;return t})(i);w(h,l)&&y(l,r)?(s=f(s,a),s=d(s,c,n)):!w(h,l)&&y(l,r)?(s=d(s,c,n),s=g(s,o,l,a)):w(h,l)&&!y(l,r)?(s=f(s,a),s=b(s,o,l,n)):w(h,l)||y(l,r)||(s=g(s,o,l,a),s=b(s,o,l,n));const R={...o[e],left:s},v={...o,[e]:R};this.setState({resizeCoords:v,tableHeight:p},this.updateWidths)}},this.onResizeEnd=(e,t)=>{this.setState({isResize:!1,id:null})}}componentDidMount(){this.minWidths=[],this.windowWidth=null,this.props.setResizeable(this.setCellRefs),this.props.updateDividers((()=>this.setState({updateCoords:!0},(()=>this.updateWidths)))),window.addEventListener("resize",this.handleResize,!1)}componentWillUnmount(){window.removeEventListener("resize",this.handleResize,!1)}render(){const{classes:e,tableId:t}=this.props,{id:s,isResize:o,resizeCoords:i,tableWidth:a,tableHeight:n}=this.state;return(0,S.jsx)("div",{className:e.root,style:{width:a},children:Object.entries(i).map((i=>{let[l,r]=i;return(0,S.jsx)("div",{"data-divider-index":l,"data-tableid":t,"aria-hidden":"true",onMouseMove:this.onResizeMove.bind(null,l),onMouseUp:this.onResizeEnd.bind(null,l),style:{width:o&&s==l?a:"auto",position:"absolute",height:n-2,cursor:"ew-resize",zIndex:1e3},children:(0,S.jsx)("div",{"aria-hidden":"true",onMouseDown:this.onResizeStart.bind(null,l),className:e.resizer,style:{left:r.left}})},l)}))})}}const He=(0,n.withStyles)(Fe,{root:{position:"absolute"},resizer:{position:"absolute",width:"1px",height:"100%",left:"100px",cursor:"ew-resize",border:"0.1px solid rgba(224, 224, 224, 1)"}},{name:"MUIDataTableResize"});var Ee=s(11001),Ne=s(96319);const ze=e=>{let{className:t,trigger:s,refExit:o,hide:i,content:a,...n}=e;const[l,r]=(0,y.useState)(!1),d=(0,y.useRef)(null);(0,y.useEffect)((()=>{if(l){"boolean"===typeof i&&i&&r(!1)}}),[i,l,r]);const c=()=>{r(!1)},p=n.classes.closeIcon;delete n.classes.closeIcon;const h={key:"content",onClick:e=>{s.props.onClick&&s.props.onClick(),(e=>{d.current=e.currentTarget,r(!0)})(e)}};return(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)("span",{...h,children:s}),(0,S.jsxs)(Ee.Ay,{elevation:2,open:l,TransitionProps:{onExited:()=>{o&&o()}},onClose:c,anchorEl:d.current,anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"},...n,children:[(0,S.jsx)(j.A,{"aria-label":"Close",onClick:c,className:p,style:{position:"absolute",right:"4px",top:"4px",zIndex:"1000"},children:(0,S.jsx)(Ne.default,{})}),a]})]})},We=(0,n.makeStyles)({name:"MUIDataTableViewCol"})((e=>({root:{padding:"16px 24px 16px 24px",fontFamily:"Roboto"},title:{marginLeft:"-7px",marginRight:"24px",fontSize:"14px",color:e.palette.text.secondary,textAlign:"left",fontWeight:500},formGroup:{marginTop:"8px"},formControl:{},checkbox:{padding:"0px",width:"32px",height:"32px"},checkboxRoot:{},checked:{},label:{fontSize:"15px",marginLeft:"8px",color:e.palette.text.primary}}))),Ue=e=>{let{columns:t,options:s,components:o={},onColumnUpdate:i,updateColumns:a}=e;const{classes:n}=We(),l=s.textLabels.viewColumns,r=o.Checkbox||D.A;return(0,S.jsxs)(J.A,{component:"fieldset",className:n.root,"aria-label":l.titleAria,children:[(0,S.jsx)(R.A,{variant:"caption",className:n.title,children:l.title}),(0,S.jsx)(G.A,{className:n.formGroup,children:t.map(((e,t)=>"excluded"!==e.display&&!1!==e.viewColumns&&(0,S.jsx)(q.A,{classes:{root:n.formControl,label:n.label},control:(0,S.jsx)(r,{color:"primary","data-description":"table-view-col",className:n.checkbox,classes:{root:n.checkboxRoot,checked:n.checked},onChange:()=>(e=>{i(e)})(t),checked:"true"===e.display,value:e.name}),label:e.label},t)))})]})};var Me=s(40165),Be=s(19223),_e=s(7762);const Ve=(0,n.makeStyles)({name:"MUIDataTableSearch"})((e=>({main:{display:"flex",flex:"1 0 auto",alignItems:"center"},searchIcon:{color:e.palette.text.secondary,marginRight:"8px"},searchText:{flex:"0.8 0"},clearIcon:{"&:hover":{color:e.palette.error.main}}}))),$e=e=>{let{options:t,searchText:s,onSearch:o,onHide:i}=e;const{classes:a}=Ve(),n=t.searchAlwaysOpen?"hidden":"visible";return(0,S.jsx)(Me.A,{appear:!0,in:!0,timeout:300,children:(0,S.jsxs)("div",{className:a.main,children:[(0,S.jsx)(Be.default,{className:a.searchIcon}),(0,S.jsx)(oe.A,{className:a.searchText,variant:"standard",InputProps:{"data-test-id":t.textLabels.toolbar.search},inputProps:{"aria-label":t.textLabels.toolbar.search},value:s||"",onKeyDown:e=>{"Escape"===e.key&&i()},onChange:e=>{o(e.target.value)},fullWidth:!0,placeholder:t.searchPlaceholder,...t.searchProps?t.searchProps:{}}),(0,S.jsx)(j.A,{className:a.clearIcon,style:{visibility:n},onClick:i,children:(0,S.jsx)(_e.default,{})})]})})};var Ke=s(95122),Xe=s(16632),Je=s(18116),qe=s(51069),Ge=s(45418),Ye=s.n(Ge);const Qe="scrollFullHeightFullWidth";class Ze extends y.Component{constructor(){super(...arguments),this.state={iconActive:null,showSearch:Boolean(this.props.searchText||this.props.options.searchText||this.props.options.searchOpen||this.props.options.searchAlwaysOpen),searchText:this.props.searchText||null},this.handleCSVDownload=()=>{const{data:e,displayData:t,columns:s,options:o,columnOrder:i}=this.props;let a=[],n=[],l=Array.isArray(i)?i.slice(0):[];if(0===l.length&&(l=s.map(((e,t)=>t))),e.forEach((e=>{let t={index:e.index,data:[]};l.forEach((s=>{t.data.push(e.data[s])})),a.push(t)})),l.forEach((e=>{n.push(s[e])})),o.downloadOptions&&o.downloadOptions.filterOptions){if(o.downloadOptions.filterOptions.useDisplayedRowsOnly){let s=t.map(((t,s)=>{let o=-1;return t.index=s,{data:t.data.map((s=>{o+=1;let i="object"!==typeof s||null===s||Array.isArray(s)?s:u()(e,(e=>e.index===t.dataIndex)).data[o];return i="function"===typeof i?u()(e,(e=>e.index===t.dataIndex)).data[o]:i,i}))}}));a=[],s.forEach((e=>{let t={index:e.index,data:[]};l.forEach((s=>{t.data.push(e.data[s])})),a.push(t)}))}o.downloadOptions.filterOptions.useDisplayedColumnsOnly&&(n=n.filter((e=>"true"===e.display)),a=a.map((e=>(e.data=e.data.filter(((e,t)=>"true"===s[l[t]].display)),e))))}V(n,a,o,_)},this.setActiveIcon=e=>{this.setState((t=>({showSearch:this.isSearchShown(e),iconActive:e,prevIconActive:t.iconActive})),(()=>{const{iconActive:e,prevIconActive:t}=this.state;"filter"===e&&(this.props.setTableAction("onFilterDialogOpen"),this.props.options.onFilterDialogOpen&&this.props.options.onFilterDialogOpen()),void 0===e&&"filter"===t&&(this.props.setTableAction("onFilterDialogClose"),this.props.options.onFilterDialogClose&&this.props.options.onFilterDialogClose())}))},this.isSearchShown=e=>{if(this.props.options.searchAlwaysOpen)return!0;let t=!1;if(this.state.showSearch)if(this.state.searchText)t=!0;else{const{onSearchClose:e}=this.props.options;this.props.setTableAction("onSearchClose"),e&&e(),t=!1}else"search"===e&&(t=this.showSearch());return t},this.getActiveIcon=(e,t)=>{let s=this.state.iconActive===t;if("search"===t){const{showSearch:e,searchText:t}=this.state;s=s||e||t}return s?e.iconActive:e.icon},this.showSearch=()=>(this.props.setTableAction("onSearchOpen"),this.props.options.onSearchOpen&&this.props.options.onSearchOpen(),!0),this.hideSearch=()=>{const{onSearchClose:e}=this.props.options;this.props.setTableAction("onSearchClose"),e&&e(),this.props.searchClose(),this.setState((()=>({iconActive:null,showSearch:!1,searchText:null})))},this.handleSearch=e=>{this.setState({searchText:e}),this.props.searchTextUpdate(e)},this.handleSearchIconClick=()=>{const{showSearch:e,searchText:t}=this.state;e&&!t?this.hideSearch():this.setActiveIcon("search")}}componentDidUpdate(e){this.props.searchText!==e.searchText&&this.setState({searchText:this.props.searchText})}render(){const{data:e,options:t,classes:s,columns:o,filterData:i,filterList:n,filterUpdate:l,resetFilters:r,toggleViewColumn:d,updateColumns:c,title:p,components:h={},updateFilterByType:u}=this.props,{icons:m={}}=h,x=h.Tooltip||a.A,f=h.TableViewCol||Ue,b=h.TableFilter||ae,g=m.SearchIcon||Be.default,w=m.DownloadIcon||Ke.default,y=m.PrintIcon||Xe.default,v=m.ViewColumnIcon||Je.default,C=m.FilterIcon||qe.default,{search:T,downloadCsv:A,print:k,viewColumns:I,filterTable:O}=t.textLabels.toolbar,{showSearch:D,searchText:P}=this.state;return(0,S.jsxs)(ue.A,{className:t.responsive!==Qe?s.root:s.fullWidthRoot,role:"toolbar","aria-label":"Table Toolbar",children:[(0,S.jsx)("div",{className:t.responsive!==Qe?s.left:s.fullWidthLeft,children:!0===D?t.customSearchRender?t.customSearchRender(P,this.handleSearch,this.hideSearch,t):(0,S.jsx)($e,{searchText:P,onSearch:this.handleSearch,onHide:this.hideSearch,options:t}):"string"!==typeof p?p:(0,S.jsx)("div",{className:s.titleRoot,"aria-hidden":"true",children:(0,S.jsx)(R.A,{variant:"h6",className:t.responsive!==Qe?s.titleText:s.fullWidthTitleText,children:p})})}),(0,S.jsxs)("div",{className:t.responsive!==Qe?s.actions:s.fullWidthActions,children:[!(!1===t.search||"false"===t.search||!0===t.searchAlwaysOpen)&&(0,S.jsx)(x,{title:T,disableFocusListener:!0,children:(0,S.jsx)(j.A,{"aria-label":T,"data-testid":T+"-iconButton",ref:e=>this.searchButton=e,classes:{root:this.getActiveIcon(s,"search")},disabled:"disabled"===t.search,onClick:this.handleSearchIconClick,children:(0,S.jsx)(g,{})})}),!(!1===t.download||"false"===t.download)&&(0,S.jsx)(x,{title:A,children:(0,S.jsx)(j.A,{"data-testid":A.replace(/\s/g,"")+"-iconButton","aria-label":A,classes:{root:s.icon},disabled:"disabled"===t.download,onClick:this.handleCSVDownload,children:(0,S.jsx)(w,{})})}),!(!1===t.print||"false"===t.print)&&(0,S.jsx)("span",{children:(0,S.jsx)(Ye(),{content:()=>this.props.tableRef(),children:(0,S.jsx)(Ge.PrintContextConsumer,{children:e=>{let{handlePrint:o}=e;return(0,S.jsx)("span",{children:(0,S.jsx)(x,{title:k,children:(0,S.jsx)(j.A,{"data-testid":k+"-iconButton","aria-label":k,disabled:"disabled"===t.print,onClick:o,classes:{root:s.icon},children:(0,S.jsx)(y,{})})})})}})})}),!(!1===t.viewColumns||"false"===t.viewColumns)&&(0,S.jsx)(ze,{refExit:this.setActiveIcon.bind(null),classes:{closeIcon:s.filterCloseIcon},hide:"disabled"===t.viewColumns,trigger:(0,S.jsx)(x,{title:I,disableFocusListener:!0,children:(0,S.jsx)(j.A,{"data-testid":I+"-iconButton","aria-label":I,classes:{root:this.getActiveIcon(s,"viewcolumns")},disabled:"disabled"===t.viewColumns,onClick:this.setActiveIcon.bind(null,"viewcolumns"),children:(0,S.jsx)(v,{})})}),content:(0,S.jsx)(f,{data:e,columns:o,options:t,onColumnUpdate:d,updateColumns:c,components:h})}),!(!1===t.filter||"false"===t.filter)&&(0,S.jsx)(ze,{refExit:()=>{this.setState({hideFilterPopover:!1}),this.setActiveIcon()},hide:this.state.hideFilterPopover||"disabled"===t.filter,classes:{paper:s.filterPaper,closeIcon:s.filterCloseIcon},trigger:(0,S.jsx)(x,{title:O,disableFocusListener:!0,children:(0,S.jsx)(j.A,{"data-testid":O+"-iconButton","aria-label":O,classes:{root:this.getActiveIcon(s,"filter")},disabled:"disabled"===t.filter,onClick:this.setActiveIcon.bind(null,"filter"),children:(0,S.jsx)(C,{})})}),content:(0,S.jsx)(b,{customFooter:t.customFilterDialogFooter,columns:o,options:t,filterList:n,filterData:i,onFilterUpdate:l,onFilterReset:r,handleClose:()=>{this.setState({hideFilterPopover:!0})},updateFilterByType:u,components:h})}),t.customToolbar&&t.customToolbar({displayData:this.props.displayData})]})]})}}const et=(0,n.withStyles)(Ze,(e=>({root:{"@media print":{display:"none"}},fullWidthRoot:{},left:{flex:"1 1 auto"},fullWidthLeft:{flex:"1 1 auto"},actions:{flex:"1 1 auto",textAlign:"right"},fullWidthActions:{flex:"1 1 auto",textAlign:"right"},titleRoot:{},titleText:{},fullWidthTitleText:{textAlign:"left"},icon:{"&:hover":{color:e.palette.primary.main}},iconActive:{color:e.palette.primary.main},filterPaper:{maxWidth:"50%"},filterCloseIcon:{position:"absolute",right:0,top:0,zIndex:100},searchIcon:{display:"inline-flex",marginTop:"10px",marginRight:"8px"},[e.breakpoints.down("md")]:{titleRoot:{},titleText:{fontSize:"16px"},spacer:{display:"none"},left:{padding:"8px 0px"},actions:{textAlign:"right"}},[e.breakpoints.down("sm")]:{root:{display:"block","@media print":{display:"none !important"}},left:{padding:"8px 0px 0px 0px"},titleText:{textAlign:"center"},actions:{textAlign:"center"}},"@media screen and (max-width: 480px)":{}})),{name:"MUIDataTableToolbar"});var tt=s(35607);class st extends y.Component{constructor(){super(...arguments),this.handleCustomSelectedRows=e=>{if(!Array.isArray(e))throw new TypeError(`"selectedRows" must be an "array", but it's "${typeof e}"`);if(e.some((e=>"number"!==typeof e)))throw new TypeError('Array "selectedRows" must contain only numbers');const{options:t}=this.props;if(e.length>1&&"single"===t.selectableRows)throw new Error('Can not select more than one row when "selectableRows" is "single"');this.props.selectRowUpdate("custom",e)}}render(){const{classes:e,onRowsDelete:t,selectedRows:s,options:i,displayData:n,components:l={}}=this.props,r=i.textLabels.selectedRows,d=l.Tooltip||a.A;return(0,S.jsxs)(o.A,{className:e.root,children:[(0,S.jsx)("div",{children:(0,S.jsxs)(R.A,{variant:"subtitle1",className:e.title,children:[s.data.length," ",r.text]})}),i.customToolbarSelect?i.customToolbarSelect(s,n,this.handleCustomSelectedRows):(0,S.jsx)(d,{title:r.delete,children:(0,S.jsx)(j.A,{className:e.iconButton,onClick:t,"aria-label":r.deleteAria,children:(0,S.jsx)(tt.default,{className:e.deleteIcon})})})]})}}const ot=(0,n.withStyles)(st,(e=>({root:{backgroundColor:e.palette.background.default,flex:"1 1 100%",display:"flex",position:"relative",zIndex:120,justifyContent:"space-between",alignItems:"center",paddingTop:"function"===typeof e.spacing?e.spacing(1):e.spacing.unit,paddingBottom:"function"===typeof e.spacing?e.spacing(1):e.spacing.unit,"@media print":{display:"none"}},title:{paddingLeft:"26px"},iconButton:{marginRight:"24px"},deleteIcon:{}})),{name:"MUIDataTableToolbarSelect"}),it=()=>({body:{noMatch:"Sorry, no matching records found",toolTip:"Sort"},pagination:{next:"Next Page",previous:"Previous Page",rowsPerPage:"Rows per page:",displayRows:"of",jumpToPage:"Jump to Page:"},toolbar:{search:"Search",downloadCsv:"Download CSV",print:"Print",viewColumns:"View Columns",filterTable:"Filter Table"},filter:{all:"All",title:"FILTERS",reset:"RESET"},viewColumns:{title:"Show Columns",titleAria:"Show/Hide Table Columns"},selectedRows:{text:"row(s) selected",delete:"Delete",deleteAria:"Delete Selected Rows"}});var at=s(11201),nt=s(66325);const lt="undefined"!==typeof window&&"undefined"!==typeof window.document,rt=1,dt=2,ct=["title","filter","search","print","download","viewColumns","customToolbar"],pt={REPLACE:"replace",ABOVE:"above",NONE:"none",ALWAYS:"always"};class ht extends y.Component{constructor(e){var t;super(e),t=this,this.getDefaultOptions=()=>({caseSensitive:!1,consoleWarnings:!0,disableToolbarSelect:!1,download:!0,downloadOptions:{filename:"tableDownload.csv",separator:","},draggableColumns:{enabled:!1,transitionTime:300},elevation:4,enableNestedDataAccess:"",expandableRows:!1,expandableRowsHeader:!0,expandableRowsOnClick:!1,filter:!0,filterArrayFullMatch:!0,filterType:"dropdown",fixedHeader:!0,fixedSelectColumn:!0,pagination:!0,print:!0,resizableColumns:!1,responsive:"vertical",rowHover:!0,rowsPerPageOptions:[10,15,100],search:!0,selectableRows:"multiple",selectableRowsHideCheckboxes:!1,selectableRowsOnClick:!1,selectableRowsHeader:!0,serverSide:!1,serverSideFilterList:null,setTableProps:()=>({}),sort:!0,sortFilterList:!0,tableBodyHeight:"auto",tableBodyMaxHeight:null,sortOrder:{},textLabels:it(),viewColumns:!0,selectToolbarPlacement:pt.REPLACE}),this.warnDep=(e,t)=>{!function(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],s="function"===typeof t?t:console.warn;t&&s(`Deprecation Notice:  ${e}`)}(e,this.options.consoleWarnings)},this.warnInfo=(e,t)=>{W(e,this.options.consoleWarnings)},this.handleOptionDeprecation=e=>{"boolean"===typeof this.options.selectableRows&&(this.warnDep("Using a boolean for selectableRows has been deprecated. Please use string option: multiple | single | none"),this.options.selectableRows=this.options.selectableRows?"multiple":"none"),-1===["standard","vertical","verticalAlways","simple"].indexOf(this.options.responsive)&&(-1!==["scrollMaxHeight","scrollFullHeight","stacked","stackedFullWidth","scrollFullHeightFullWidth","scroll"].indexOf(this.options.responsive)?this.warnDep(this.options.responsive+" has been deprecated, but will still work in version 3.x. Please use string option: standard | vertical | simple. More info: https://github.com/gregnb/mui-datatables/tree/master/docs/v2_to_v3_guide.md"):this.warnInfo(this.options.responsive+" is not recognized as a valid input for responsive option. Please use string option: standard | vertical | simple. More info: https://github.com/gregnb/mui-datatables/tree/master/docs/v2_to_v3_guide.md")),this.options.onRowsSelect&&this.warnDep("onRowsSelect has been renamed onRowSelectionChange. More info: https://github.com/gregnb/mui-datatables/tree/master/docs/v2_to_v3_guide.md"),this.options.onRowsExpand&&this.warnDep("onRowsExpand has been renamed onRowExpansionChange. More info: https://github.com/gregnb/mui-datatables/tree/master/docs/v2_to_v3_guide.md"),this.options.fixedHeaderOptions&&("undefined"!==typeof this.options.fixedHeaderOptions.yAxis&&"undefined"===typeof this.options.fixedHeader&&(this.options.fixedHeader=this.options.fixedHeaderOptions.yAxis),"undefined"!==typeof this.options.fixedHeaderOptions.xAxis&&"undefined"===typeof this.options.fixedSelectColumn&&(this.options.fixedSelectColumn=this.options.fixedHeaderOptions.xAxis),this.warnDep("fixedHeaderOptions will still work but has been deprecated in favor of fixedHeader and fixedSelectColumn. More info: https://github.com/gregnb/mui-datatables/tree/master/docs/v2_to_v3_guide.md")),this.options.serverSideFilterList&&this.warnDep("serverSideFilterList will still work but has been deprecated in favor of the confirmFilters option. See this example for details: https://github.com/gregnb/mui-datatables/blob/master/examples/serverside-filters/index.js More info here: https://github.com/gregnb/mui-datatables/tree/master/docs/v2_to_v3_guide.md"),e.columns.map((e=>{e.options&&e.options.customFilterListRender&&this.warnDep("The customFilterListRender option has been deprecated. It is being replaced by customFilterListOptions.render (Specify customFilterListOptions: { render: Function } in column options.)")})),!0===this.options.disableToolbarSelect&&this.warnDep('disableToolbarSelect has been deprecated but will still work in version 3.x. It is being replaced by "selectToolbarPlacement"="none". More info: https://github.com/gregnb/mui-datatables/tree/master/docs/v2_to_v3_guide.md'),Object.values&&-1===Object.values(pt).indexOf(this.options.selectToolbarPlacement)&&this.warnDep("Invalid option value for selectToolbarPlacement. Please check the documentation: https://github.com/gregnb/mui-datatables#options")},this.setTableAction=e=>{"function"===typeof this.options.onTableChange&&this.options.onTableChange(e,this.state),this.options.storageKey&&((e,t)=>{const{selectedRows:s,data:o,displayData:i,...a}=t;window.localStorage.setItem(e,JSON.stringify(a))})(this.options.storageKey,this.state)},this.setTableInit=e=>{"function"===typeof this.options.onTableInit&&this.options.onTableInit(e,this.state)},this.setHeadCellRef=(e,t,s)=>{this.draggableHeadCellRefs[e]=s,this.resizeHeadCellRefs[t]=s},this.getTableContentRef=()=>this.tableContent.current,this.buildColumns=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],o=arguments.length>2?arguments[2]:void 0,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],a=[],n=[],l=[],r=[];return e.forEach(((e,o)=>{let i={display:"true",empty:!1,filter:!0,sort:!0,print:!0,searchable:!0,download:!0,viewColumns:!0,sortCompare:null,sortThirdClickReset:!1,sortDescFirst:!1};r.push(o);const d={...e.options};"object"===typeof e?(d&&(void 0!==d.display&&(d.display=d.display.toString()),(null===d.sortDirection||d.sortDirection)&&t.warnDep("The sortDirection column field has been deprecated. Please use the sortOrder option on the options object. More info: https://github.com/gregnb/mui-datatables/tree/master/docs/v2_to_v3_guide.md")),"undefined"===typeof d.display&&s[o]&&s[o].name===e.name&&s[o].display&&(d.display=s[o].display),i={name:e.name,label:e.label?e.label:e.name,...i,...d}):(s[o]&&s[o].display&&(d.display=s[o].display),i={...i,...d,name:e,label:e}),a.push(i),n[o]=[],l[o]=[]})),Array.isArray(o)?r=o:Array.isArray(i)&&Array.isArray(e)&&Array.isArray(s)&&e.length===s.length&&(r=i),{columns:a,filterData:n,filterList:l,columnOrder:r}},this.transformData=(e,t)=>{const{enableNestedDataAccess:s}=this.options;return Array.isArray(t[0])?t.map((t=>{let s=-1;return e.map((e=>(e.empty||s++,e.empty?void 0:t[s])))})):t.map((t=>e.map((e=>{return o=t,i=e.name,(s?i.split(s):i.split()).reduce(((e,t)=>e?e[t]:void 0),o);var o,i}))))},this.hasSearchText=(e,t,s)=>{let o=e.toString(),i=t.toString();return s||(i=i.toLowerCase(),o=o.toLowerCase()),o.indexOf(i)>=0},this.updateDataCol=(e,t,s)=>{this.setState((o=>{let i=p()(o.data),a=p()(o.filterData);const n=this.getTableMeta(e,t,e,o.columns[t],o.data,o,o.data),l=o.columns[t].customBodyRender(s,n),r=y.isValidElement(l)&&l.props.value?l.props.value:o.data[e][t],d=a[t].indexOf(r);if(a[t].splice(d,1,r),i[e].data[t]=s,this.options.sortFilterList){const e=M();a[t].sort(e)}return{data:i,filterData:a,displayData:this.getDisplayData(o.columns,i,o.filterList,o.searchText,null,this.props)}}))},this.getTableMeta=(e,t,s,o,i,a,n)=>{const{columns:l,data:r,displayData:d,filterData:c,...p}=a;return{rowIndex:e,columnIndex:t,columnData:o,rowData:s,tableData:i,tableState:p,currentTableData:n}},this.toggleViewColumn=e=>{this.setState((t=>{const s=p()(t.columns);return s[e].display="true"===s[e].display?"false":"true",{columns:s}}),(()=>{this.setTableAction("viewColumnsChange");var t=this.options.onViewColumnsChange||this.options.onColumnViewChange;t&&t(this.state.columns[e].name,"true"===this.state.columns[e].display?"add":"remove")}))},this.updateColumns=e=>{this.setState((t=>({columns:e})),(()=>{this.setTableAction("viewColumnsChange");var t=this.options.onViewColumnsChange||this.options.onColumnViewChange;t&&t(null,"update",e)}))},this.toggleSortColumn=e=>{this.setState((t=>{let s=p()(t.columns),o=t.data,i=s[e].sortDescFirst?"desc":"asc",a=["asc","desc"];if(s[e].sortDescFirst&&(a=["desc","asc"]),s[e].sortThirdClickReset&&a.push("none"),s[e].name===this.state.sortOrder.name){let e=a.indexOf(this.state.sortOrder.direction);-1!==e&&(e++,e>=a.length&&(e=0),i=a[e])}const n={name:s[e].name,direction:i},l=this.getSortDirectionLabel(n);let r={columns:s,announceText:`Table now sorted by ${s[e].name} : ${l}`,activeColumn:e};if(this.options.serverSide)r={...r,data:t.data,displayData:t.displayData,selectedRows:t.selectedRows,sortOrder:n};else{const a=this.sortTable(o,e,i,s[e].sortCompare);r={...r,data:a.data,displayData:this.getDisplayData(s,a.data,t.filterList,t.searchText,null,this.props),selectedRows:a.selectedRows,sortOrder:n,previousSelectedRow:null}}return r}),(()=>{this.setTableAction("sort"),this.options.onColumnSortChange&&this.options.onColumnSortChange(this.state.sortOrder.name,this.state.sortOrder.direction)}))},this.changeRowsPerPage=e=>{const t=this.options.count||this.state.displayData.length;this.setState((()=>({rowsPerPage:e,page:U(t,e,this.state.page)})),(()=>{this.setTableAction("changeRowsPerPage"),this.options.onChangeRowsPerPage&&this.options.onChangeRowsPerPage(this.state.rowsPerPage)}))},this.changePage=e=>{this.setState((()=>({page:e})),(()=>{this.setTableAction("changePage"),this.options.onChangePage&&this.options.onChangePage(this.state.page)}))},this.searchClose=()=>{this.setState((e=>({searchText:null,displayData:this.options.serverSide?e.displayData:this.getDisplayData(e.columns,e.data,e.filterList,null,null,this.props)})),(()=>{this.setTableAction("search"),this.options.onSearchChange&&this.options.onSearchChange(this.state.searchText)}))},this.searchTextUpdate=e=>{this.setState((t=>({searchText:e&&e.length?e:null,page:0,displayData:this.options.serverSide?t.displayData:this.getDisplayData(t.columns,t.data,t.filterList,e,null,this.props)})),(()=>{this.setTableAction("search"),this.options.onSearchChange&&this.options.onSearchChange(this.state.searchText)}))},this.resetFilters=()=>{this.setState((e=>{const t=e.columns.map((()=>[]));return{filterList:t,displayData:this.options.serverSide?e.displayData:this.getDisplayData(e.columns,e.data,t,e.searchText,null,this.props)}}),(()=>{this.setTableAction("resetFilters"),this.options.onFilterChange&&this.options.onFilterChange(null,this.state.filterList,"reset",null)}))},this.updateFilterByType=(e,t,s,o,i)=>{const a=e[t].findIndex((e=>x()(e,s)));switch(o){case"checkbox":case"chip":a>=0?e[t].splice(a,1):e[t].push(s);break;case"multiselect":e[t]=""===s?[]:s;break;case"dropdown":e[t]=s;break;case"custom":i?e=i(e,a,t):e[t]=s;break;default:e[t]=a>=0||""===s?[]:[s]}},this.filterUpdate=(e,t,s,o,i,a)=>{this.setState((s=>{const a=p()(s.filterList);return this.updateFilterByType(a,e,t,o,i),{page:0,filterList:a,displayData:this.options.serverSide?s.displayData:this.getDisplayData(s.columns,s.data,a,s.searchText,null,this.props),previousSelectedRow:null}}),(()=>{this.setTableAction("filterChange"),this.options.onFilterChange&&this.options.onFilterChange(s,this.state.filterList,o,e,this.state.displayData),a&&a(this.state.filterList)}))},this.toggleAllExpandableRows=()=>{let e=[...this.state.expandedRows.data];const{isRowExpandable:t}=this.options;let s=[];if(e.length>0)for(let o=e.length-1;o>=0;o--){let i=e[o];(!t||t&&t(i.dataIndex,this.state.expandedRows))&&s.push(e.splice(o,1))}else for(let o=0;o<this.state.data.length;o++){let i=this.state.data[o];if((!t||t&&t(i.dataIndex,this.state.expandedRows))&&!0!==this.state.expandedRows.lookup[i.index]){let t={index:o,dataIndex:i.index};e.push(t),s.push(t)}}this.setState({expandedRows:{lookup:N(e),data:e}},(()=>{this.setTableAction("expandRow"),this.options.onRowExpansionChange&&this.options.onRowExpansionChange(s,this.state.expandedRows.data,this.state.expandedRows.data.map((e=>e.dataIndex)))}))},this.areAllRowsExpanded=()=>this.state.expandedRows.data.length===this.state.data.length,this.updateColumnOrder=(e,t,s)=>{this.setState((t=>({columnOrder:e})),(()=>{this.setTableAction("columnOrderChange"),this.options.onColumnOrderChange&&this.options.onColumnOrderChange(this.state.columnOrder,t,s)}))},this.selectRowDelete=()=>{const{selectedRows:e,data:t,filterList:s}=this.state,o=N(e.data),i=t.filter((e=>{let{index:t}=e;return!o[t]}));this.options.onRowsDelete&&!1===this.options.onRowsDelete(e,i.map((e=>e.data)))||this.setTableData({columns:this.props.columns,data:i,options:{filterList:s}},dt,!0,(()=>{this.setTableAction("rowDelete")}))},this.toggleExpandRow=e=>{const{dataIndex:t}=e,{isRowExpandable:s}=this.options;let{expandedRows:o}=this.state;const i=[...o.data];let a=!1,n=!1,l=[];for(var r=0;r<i.length;r++)if(i[r].dataIndex===t){a=!0;break}a?(s&&s(t,o)||!s)&&(l=i.splice(r,1),n=!0):s&&s(t,o)?i.push(e):s||i.push(e),this.setState({curExpandedRows:n?l:[e],expandedRows:{lookup:N(i),data:i}},(()=>{if(this.setTableAction("rowExpansionChange"),this.options.onRowExpansionChange||this.options.onRowsExpand){(this.options.onRowExpansionChange||this.options.onRowsExpand)(this.state.curExpandedRows,this.state.expandedRows.data)}}))},this.selectRowUpdate=function(e,s){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];const{selectableRows:i}=t.options;if("none"!==i)if("head"===e){const{isRowSelectable:e}=t.options;t.setState((s=>{const{displayData:o,selectedRows:i}=s,a=s.selectedRows.data.length;let n=a===o.length||a<o.length&&a>0,l=[...o.reduce(((t,s,a)=>((!e||e(o[a].dataIndex,i))&&t.push({index:a,dataIndex:o[a].dataIndex}),t)),[])],r=N(l);if(t.options.selectToolbarPlacement===pt.NONE)if(a>o.length)n=!0;else for(let e=0;e<o.length;e++)r[o[e].dataIndex]||(n=!0);return n&&(l=s.selectedRows.data.filter((e=>{let{dataIndex:t}=e;return!r[t]})),r=N(l)),{curSelectedRows:l,selectedRows:{data:l,lookup:r},previousSelectedRow:null}}),(()=>{t.setTableAction("rowSelectionChange"),t.options.onRowSelectionChange?t.options.onRowSelectionChange(t.state.curSelectedRows,t.state.selectedRows.data,t.state.selectedRows.data.map((e=>e.dataIndex))):t.options.onRowsSelect&&t.options.onRowsSelect(t.state.curSelectedRows,t.state.selectedRows.data,t.state.selectedRows.data.map((e=>e.dataIndex)))}))}else if("cell"===e)t.setState((e=>{const{dataIndex:t}=s;let a=[...e.selectedRows.data],n=-1;for(let s=0;s<a.length;s++)if(a[s].dataIndex===t){n=s;break}if(n>=0){if(a.splice(n,1),o.length>0){let e=N(o);for(let t=a.length-1;t>=0;t--)e[a[t].dataIndex]&&a.splice(t,1)}}else if("single"===i)a=[s];else if(a.push(s),o.length>0){let e=N(a);o.forEach((t=>{e[t.dataIndex]||a.push(t)}))}return{selectedRows:{lookup:N(a),data:a},previousSelectedRow:s}}),(()=>{t.setTableAction("rowSelectionChange"),t.options.onRowSelectionChange?t.options.onRowSelectionChange([s],t.state.selectedRows.data,t.state.selectedRows.data.map((e=>e.dataIndex))):t.options.onRowsSelect&&t.options.onRowsSelect([s],t.state.selectedRows.data,t.state.selectedRows.data.map((e=>e.dataIndex)))}));else if("custom"===e){const{displayData:e}=t.state,o=s.map((t=>({index:t,dataIndex:e[t].dataIndex}))),i=N(o);t.setState({selectedRows:{data:o,lookup:i},previousSelectedRow:null},(()=>{t.setTableAction("rowSelectionChange"),t.options.onRowSelectionChange?t.options.onRowSelectionChange(t.state.selectedRows.data,t.state.selectedRows.data,t.state.selectedRows.data.map((e=>e.dataIndex))):t.options.onRowsSelect&&t.options.onRowsSelect(t.state.selectedRows.data,t.state.selectedRows.data,t.state.selectedRows.data.map((e=>e.dataIndex)))}))}},this.tableRef=y.createRef(),this.tableContent=y.createRef(),this.draggableHeadCellRefs={},this.resizeHeadCellRefs={},this.timers={},this.setHeadResizeable=()=>{},this.updateDividers=()=>{};this.mergeDefaultOptions(e);const s=(e=>{if(lt)return JSON.parse(window.localStorage.getItem(e));void 0===e||console.warn("storageKey support only on browser")})(e.options.storageKey);this.state=Object.assign({activeColumn:null,announceText:null,count:0,columns:[],expandedRows:{data:[],lookup:{}},data:[],displayData:[],filterData:[],filterList:[],page:0,previousSelectedRow:null,rowsPerPage:10,searchProps:{},searchText:null,selectedRows:{data:[],lookup:{}},showResponsive:!1,sortOrder:{}},s||this.getInitTableOptions()),this.setTableData=this.setTableData.bind(this),this.setTableData(e,rt,!0,null,!0)}componentDidMount(){this.setHeadResizeable(this.resizeHeadCellRefs,this.tableRef),this.props.options.searchText&&!this.props.options.serverSide&&this.setState({page:0}),this.setTableInit("tableInitialized")}componentDidUpdate(e){if(this.props.data!==e.data||this.props.columns!==e.columns||this.props.options!==e.options){this.updateOptions(this.options,this.props);var t=this.props.data!==e.data;this.props.data&&e.data&&(t=t&&this.props.data.length===e.data.length),this.setTableData(this.props,rt,t,(()=>{this.setTableAction("propsUpdate")}))}this.props.options.searchText===e.options.searchText||this.props.options.serverSide||this.setState({page:0}),(!0===this.options.resizableColumns||this.options.resizableColumns&&this.options.resizableColumns.enabled)&&(this.setHeadResizeable(this.resizeHeadCellRefs,this.tableRef),this.updateDividers())}updateOptions(e,t){!0===t.options.disableToolbarSelect&&void 0===t.options.selectToolbarPlacement&&(t.options.selectToolbarPlacement=pt.NONE),t.options.tableId||(t.options.tableId=(Math.random()+"").replace(/\./,"")),this.options=d()(e,t.options,((e,t,s)=>{if("textLabels"===s||"downloadOptions"===s)return w()(e,t)})),this.handleOptionDeprecation(t)}mergeDefaultOptions(e){const t=this.getDefaultOptions(),s=Object.assign({},e);s.options=s.options||{},this.updateOptions(t,s)}validateOptions(e){if(e.serverSide&&void 0===e.onTableChange)throw Error("onTableChange callback must be provided when using serverSide option");if(e.expandableRows&&void 0===e.renderExpandableRow)throw Error("renderExpandableRow must be provided when using expandableRows option");e.rowsSelected&&Array.isArray(e.rowsSelected)&&e.rowsSelected.some(isNaN)&&W("When using the rowsSelected option, must be provided an array of numbers only.")}getInitTableOptions(){const e=["rowsPerPage","page","rowsSelected","rowsPerPageOptions"].reduce(((e,t)=>(void 0!==this.options[t]&&(e[t]=this.options[t]),e)),{});return this.validateOptions(e),e}setTableData(e,t,s){let o,i,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:()=>{},n=arguments.length>4&&void 0!==arguments[4]&&arguments[4],l=[],{columns:r,filterData:d,filterList:c,columnOrder:h}=this.buildColumns(e.columns,this.state.columns,this.options.columnOrder,this.state.columnOrder),u=null,m="none";this.options.sortOrder&&this.options.sortOrder.direction&&this.options.sortOrder.name?i=Object.assign({},this.options.sortOrder):(i=Object.assign({},this.state.sortOrder),i.direction||e.columns.forEach(((e,t)=>{!e.options||"asc"!==e.options.sortDirection&&"desc"!==e.options.sortDirection||(i.name=e.name,i.sortDirection=e.sortDirection)})));const x=t===rt?this.transformData(r,e.data):e.data;let f=t===rt?this.options.searchText:null;"undefined"===typeof this.options.searchText&&"undefined"!==typeof this.state.searchText&&(f=this.state.searchText);let b=this.state.rowsPerPage;"number"===typeof this.options.rowsPerPage&&(b=this.options.rowsPerPage);let g=this.state.page;"number"===typeof this.options.page&&(g=this.options.page),r.forEach(((e,s)=>{for(let i=0;i<x.length;i++){let a=t===rt?x[i][s]:x[i].data[s];if("undefined"===typeof l[i]&&l.push({index:t===rt?i:x[i].index,data:t===rt?x[i]:x[i].data}),!1!==e.filter){if("function"===typeof e.customBodyRender){const t=l[i].data;o=this.getTableMeta(i,s,t,e,x,this.state,l);const n=e.customBodyRender(a,o);y.isValidElement(n)&&n.props.value?a=n.props.value:"string"===typeof n&&(a=n)}"object"!==typeof a||Array.isArray(a)||null===a||(a=a.toString?a.toString():""),d[s].indexOf(a)<0&&!Array.isArray(a)?d[s].push(a):Array.isArray(a)&&a.forEach((e=>{let t;t="object"===typeof e&&null!==e||"function"===typeof e?e.toString?e.toString():"":e,d[s].indexOf(t)<0&&d[s].push(t)}))}}if(e.filterOptions&&(Array.isArray(e.filterOptions)?(d[s]=p()(e.filterOptions),this.warnDep("filterOptions must now be an object. see https://github.com/gregnb/mui-datatables/tree/master/examples/customize-filter example")):Array.isArray(e.filterOptions.names)&&(d[s]=p()(e.filterOptions.names))),e.filterList?c[s]=p()(e.filterList):this.state.filterList&&this.state.filterList[s]&&this.state.filterList[s].length>0&&(c[s]=p()(this.state.filterList[s])),this.options.sortFilterList){const e=M();d[s].sort(e)}e.name===i.name&&(m=i.direction,u=s)}));let w={data:[],lookup:{}},R={data:[],lookup:{}};if(rt){if(this.options.rowsSelected&&this.options.rowsSelected.length&&"multiple"===this.options.selectableRows)this.options.rowsSelected.filter((e=>0===e||Number(e)&&e>0)).forEach((e=>{let t=e;for(let s=0;s<this.state.displayData.length;s++)if(this.state.displayData[s].dataIndex===e){t=s;break}w.data.push({index:t,dataIndex:e}),w.lookup[e]=!0}));else if(this.options.rowsSelected&&1===this.options.rowsSelected.length&&"single"===this.options.selectableRows){let e=this.options.rowsSelected[0];for(let t=0;t<this.state.displayData.length;t++)if(this.state.displayData[t].dataIndex===this.options.rowsSelected[0]){e=t;break}w.data.push({index:e,dataIndex:this.options.rowsSelected[0]}),w.lookup[this.options.rowsSelected[0]]=!0}else this.options.rowsSelected&&this.options.rowsSelected.length>1&&"single"===this.options.selectableRows?console.error('Multiple values provided for selectableRows, but selectableRows set to "single". Either supply only a single value or use "multiple".'):"undefined"===typeof this.options.rowsSelected&&!1===s&&this.state.selectedRows&&(w=Object.assign({},this.state.selectedRows));this.options.rowsExpanded&&this.options.rowsExpanded.length&&this.options.expandableRows?this.options.rowsExpanded.forEach((e=>{let t=e;for(let s=0;s<this.state.displayData.length;s++)if(this.state.displayData[s].dataIndex===e){t=s;break}R.data.push({index:t,dataIndex:e}),R.lookup[e]=!0})):"undefined"===typeof this.options.rowsExpanded&&!1===s&&this.state.expandedRows&&(R=Object.assign({},this.state.expandedRows))}if(!this.options.serverSide&&null!==u){const e=this.sortTable(l,u,m,r[u].sortCompare);l=e.data}let v={columns:r,filterData:d,filterList:c,searchText:f,selectedRows:w,expandedRows:R,count:this.options.count,data:l,sortOrder:i,rowsPerPage:b,page:g,displayData:this.getDisplayData(r,l,c,f,o,e),columnOrder:h};n?this.state=Object.assign({},this.state,v):this.setState(v,a)}computeDisplayRow(e,t,s,o,i,a,n,l,r){let d=!1,c=!1,p=[];for(let u=0;u<t.length;u++){let l=t[u],h=t[u],m=e[u];if(m.customBodyRenderLite)p.push(m.customBodyRenderLite);else if(m.customBodyRender){const e=this.getTableMeta(s,u,t,m,a,{...this.state,filterList:o,searchText:i},r),n=m.customBodyRender(h,e,this.updateDataCol.bind(null,s,u));l=n,h="string"!==typeof n&&n?n.props&&n.props.value?n.props.value:h:n,p.push(l)}else p.push(l);const x=null===h||void 0===h?"":h.toString(),f=o[u],b=n.caseSensitive,g=m.filterType||n.filterType;if(f.length||"custom"===g)if(m.filterOptions&&m.filterOptions.logic)m.filterOptions.logic(h,f,t)&&(d=!0);else if("textField"!==g||this.hasSearchText(x,f,b)){if("textField"!==g&&!1===Array.isArray(h)&&f.indexOf(h)<0)d=!0;else if("textField"!==g&&Array.isArray(h))if(n.filterArrayFullMatch){f.every((e=>h.indexOf(e)>=0))||(d=!0)}else{f.some((e=>h.indexOf(e)>=0))||(d=!0)}}else d=!0;i&&"excluded"!==m.display&&this.hasSearchText(x,i,b)&&"false"!==m.display&&m.searchable&&(c=!0)}const{customSearch:h}=l.options;if(i&&h){const s=h(i,t,e);"boolean"!==typeof s?console.error("customSearch must return a boolean"):c=s}return n.serverSide?(h&&console.warn("Server-side filtering is enabled, hence custom search will be ignored."),p):d||i&&!c?null:p}getDisplayData(e,t,s,o,i,a){let n=[];const l=i?i.tableData:a.data;for(let r=0;r<t.length;r++){const i=t[r].data,d=this.computeDisplayRow(e,i,r,s,o,l,this.options,a,t);d&&n.push({data:d,dataIndex:t[r].index})}return n}getSortDirectionLabel(e){switch(e.direction){case"asc":return"ascending";case"desc":return"descending";case"none":return"none";default:return""}}getTableProps(){const{classes:e}=this.props,t=this.options.setTableProps()||{};return t.className=(0,l.A)(e.tableRoot,t.className),t}sortTable(e,t,s){let o,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,a=this.options.customSort&&!i,n={selectedRows:this.state.selectedRows},l=a?this.options.customSort(e,t,s||(this.options.sortDescFirst?"desc":"asc"),n):e;"none"===s&&(o=e.reduce(((e,t)=>(e[t.index]=t,e)),[]));let r=l.map(((e,s)=>({data:e.data[t],rowData:e.data,position:s,rowSelected:!!this.state.selectedRows.lookup[e.index]})));if(!a){const e=i||B;r.sort(e(s))}let d=[],c=[];for(let p=0;p<r.length;p++){const e=r[p];d.push(l[e.position]),e.rowSelected&&c.push({index:p,dataIndex:l[e.position].index})}return{data:"none"===s?o:d,selectedRows:{lookup:N(c),data:c}}}render(){const{classes:e,className:t,title:s,components:{TableBody:a,TableFilterList:n,TableFooter:r,TableHead:d,TableResize:c,TableToolbar:p,TableToolbarSelect:h,DragDropBackend:m=nt.HTML5Backend}}=this.props,{announceText:x,activeColumn:f,data:g,displayData:w,columns:y,page:R,filterData:v,filterList:C,selectedRows:T,previousSelectedRow:A,expandedRows:k,searchText:I,sortOrder:O,serverSideFilterList:D,columnOrder:j}=this.state,P=a||K,L=n||de,F=r||we,H=d||Pe,E=c||He,N=p||et,z=h||ot,W=this.state.count||w.length,U=this.options.pagination?this.state.rowsPerPage:w.length,M=((e,t)=>(e.title=t,!b()(u()(ct,(t=>e[t])))))(this.options,s),B=y.map((e=>({name:e.name,filterType:e.filterType||this.options.filterType}))),_=this.options.responsive;let V,$=`${e.paper} ${t}`,X=this.options.tableBodyMaxHeight;switch(_){case"scroll":V=e.responsiveScroll,X="";break;case"scrollMaxHeight":V=e.responsiveScrollMaxHeight,X="";break;case"scrollFullHeight":V=e.responsiveScrollFullHeight,X="none";break;case"scrollFullHeightFullWidth":V=e.responsiveScrollFullHeight,$=`${e.paperResponsiveScrollFullHeightFullWidth} ${t}`;break;case"stacked":V=e.responsiveStacked,X="none";break;case"stackedFullWidth":V=e.responsiveStackedFullWidth,$=`${e.paperResponsiveScrollFullHeightFullWidth} ${t}`,X="none";break;default:V=e.responsiveBase}var J={};X&&(J.maxHeight=X),this.options.tableBodyHeight&&(J.height=this.options.tableBodyHeight);const q=this.options.setTableProps&&this.options.setTableProps()||{},G=(0,l.A)(e.tableRoot,q.className);delete q.className;const Y={};return"undefined"!==typeof window&&(Y.context=window),(0,S.jsxs)(o.A,{elevation:this.options.elevation,ref:this.tableContent,className:$,children:[(this.options.selectToolbarPlacement===pt.ALWAYS||T.data.length>0&&this.options.selectToolbarPlacement!==pt.NONE)&&(0,S.jsx)(z,{options:this.options,selectedRows:T,onRowsDelete:this.selectRowDelete,displayData:w,selectRowUpdate:this.selectRowUpdate,components:this.props.components}),(0===T.data.length||-1!==[pt.ABOVE,pt.NONE].indexOf(this.options.selectToolbarPlacement))&&M&&(0,S.jsx)(N,{columns:y,columnOrder:j,displayData:w,data:g,filterData:v,filterList:C,filterUpdate:this.filterUpdate,updateFilterByType:this.updateFilterByType,options:this.options,resetFilters:this.resetFilters,searchText:I,searchTextUpdate:this.searchTextUpdate,searchClose:this.searchClose,tableRef:this.getTableContentRef,title:s,toggleViewColumn:this.toggleViewColumn,updateColumns:this.updateColumns,setTableAction:this.setTableAction,components:this.props.components}),(0,S.jsx)(L,{options:this.options,serverSideFilterList:this.props.options.serverSideFilterList,filterListRenderers:y.map((e=>e.customFilterListOptions&&e.customFilterListOptions.render?e.customFilterListOptions.render:e.customFilterListRender?e.customFilterListRender:e=>e)),customFilterListUpdate:y.map((e=>e.customFilterListOptions&&e.customFilterListOptions.update?e.customFilterListOptions.update:null)),filterList:C,filterUpdate:this.filterUpdate,columnNames:B}),(0,S.jsxs)("div",{style:{position:"relative",...J},className:V,children:[(!0===this.options.resizableColumns||this.options.resizableColumns&&this.options.resizableColumns.enabled)&&(0,S.jsx)(E,{columnOrder:j,updateDividers:e=>this.updateDividers=e,setResizeable:e=>this.setHeadResizeable=e,options:this.props.options,tableId:this.options.tableId},W),(()=>{const t=(0,S.jsxs)(i.A,{ref:e=>this.tableRef=e,tabIndex:"0",role:"grid",className:G,...q,children:[(0,S.jsx)("caption",{className:e.caption,children:s}),(0,S.jsx)(H,{columns:y,activeColumn:f,data:w,count:W,page:R,rowsPerPage:U,selectedRows:T,selectRowUpdate:this.selectRowUpdate,toggleSort:this.toggleSortColumn,setCellRef:this.setHeadCellRef,expandedRows:k,areAllRowsExpanded:this.areAllRowsExpanded,toggleAllExpandableRows:this.toggleAllExpandableRows,options:this.options,sortOrder:O,columnOrder:j,updateColumnOrder:this.updateColumnOrder,draggableHeadCellRefs:this.draggableHeadCellRefs,tableRef:this.getTableContentRef,tableId:this.options.tableId,timers:this.timers,components:this.props.components}),(0,S.jsx)(P,{data:w,count:W,columns:y,page:R,rowsPerPage:U,selectedRows:T,selectRowUpdate:this.selectRowUpdate,previousSelectedRow:A,expandedRows:k,toggleExpandRow:this.toggleExpandRow,options:this.options,columnOrder:j,filterList:C,components:this.props.components,tableId:this.options.tableId}),this.options.customTableBodyFooterRender?this.options.customTableBodyFooterRender({data:w,count:W,columns:y,selectedRows:T,selectableRows:this.options.selectableRows}):null]});return m?(0,S.jsx)(at.Q,{backend:m,...Y,children:t}):t})()]}),(0,S.jsx)(F,{options:this.options,page:R,rowCount:W,rowsPerPage:U,changeRowsPerPage:this.changeRowsPerPage,changePage:this.changePage}),(0,S.jsx)("div",{className:e.liveAnnounce,"aria-live":"polite",children:x})]})}}ht.defaultProps={title:"",options:{},data:[],columns:[],components:{TableBody:K,TableFilter:ae,TableFilterList:de,TableFooter:we,TableHead:Pe,TableResize:He,TableToolbar:et,TableToolbarSelect:ot,Tooltip:a.A,icons:{}}};const ut=(0,n.withStyles)(ht,(e=>({root:{"& .datatables-noprint":{"@media print":{display:"none"}}},paper:{isolation:"isolate"},paperResponsiveScrollFullHeightFullWidth:{position:"absolute"},tableRoot:{outline:"none"},responsiveBase:{overflow:"auto","@media print":{height:"auto !important"}},responsiveScroll:{overflow:"auto",height:"100%"},responsiveScrollMaxHeight:{overflow:"auto",height:"100%"},responsiveScrollFullHeight:{height:"100%"},responsiveStacked:{overflow:"auto",[e.breakpoints.down("md")]:{overflow:"hidden"}},responsiveStackedFullWidth:{},caption:{position:"absolute",left:"-3000px"},liveAnnounce:{border:"0",clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:"0",position:"absolute",width:"1px"}})),{name:"MUIDataTable"});class mt extends y.Component{constructor(){super(...arguments),this.handleTextChangeWrapper=e=>function(t){e(t.target.value)},this.onKeyDown=e=>{27===e.keyCode&&this.props.onHide()}}componentDidMount(){document.addEventListener("keydown",this.onKeyDown,!1)}componentWillUnmount(){document.removeEventListener("keydown",this.onKeyDown,!1)}render(){const{classes:e,options:t,onHide:s,searchText:o,debounceWait:i}=this.props,a=function(e,t,s){var o;return function(){var i=this,a=arguments,n=s&&!o;clearTimeout(o),o=setTimeout((function(){o=null,s||e.apply(i,a)}),t),n&&e.apply(i,a)}}((e=>{this.props.onSearch(e)}),i),n=t.searchAlwaysOpen?"hidden":"visible";return(0,S.jsx)(Me.A,{appear:!0,in:!0,timeout:300,children:(0,S.jsxs)("div",{className:e.main,children:[(0,S.jsx)(Be.default,{className:e.searchIcon}),(0,S.jsx)(oe.A,{variant:"standard",className:e.searchText,InputProps:{"data-test-id":t.textLabels.toolbar.search,"aria-label":t.textLabels.toolbar.search},defaultValue:o,onChange:this.handleTextChangeWrapper(a),fullWidth:!0,inputRef:e=>this.searchField=e,placeholder:t.searchPlaceholder,...t.searchProps?t.searchProps:{}}),(0,S.jsx)(j.A,{className:e.clearIcon,style:{visibility:n},onClick:s,children:(0,S.jsx)(_e.default,{})})]})})}}(0,n.withStyles)(mt,(e=>({main:{display:"flex",flex:"1 0 auto",alignItems:"center"},searchIcon:{color:e.palette.text.secondary,marginRight:"8px"},searchText:{flex:"0.8 0"},clearIcon:{"&:hover":{color:e.palette.error.main}}})),{name:"MUIDataTableSearch"})}}]);
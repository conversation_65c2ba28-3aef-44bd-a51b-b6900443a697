import React, { useEffect, useState, useRef } from 'react';
import { Box, CircularProgress, Typography, Card, CardContent, Grid } from '@mui/material';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import ApiService from '../../../../utils/ApiService';

// Set Mapbox access token
mapboxgl.accessToken = process.env.REACT_APP_MAPBOX_ACCESS_TOKEN;

// Utility function to convert date to UTC format
const convertToUTC = (date) => {
    return date.toISOString();
};

// Utility function to format date for display
const formatDateDisplay = (date) => {
    return date.toLocaleString('en-US', {
        month: 'short',
        day: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

// Function to get color based on delay value (green to red scale)
const getDelayColor = (delay, maxDelay) => {
    if (maxDelay === 0) return '#00ff00'; // Green if no delays

    const ratio = Math.min(delay / maxDelay, 1);

    // Interpolate between green (0,255,0) and red (255,0,0)
    const red = Math.round(255 * ratio);
    const green = Math.round(255 * (1 - ratio));

    return `rgb(${red}, ${green}, 0)`;
};

// Function to get circle size based on delay value
const getCircleSize = (delay, maxDelay) => {
    const minSize = 15;
    const maxSize = 40;

    if (maxDelay === 0) return minSize;

    const ratio = delay / maxDelay;
    return minSize + (maxSize - minSize) * ratio;
};

const RouteSignalDelayReport = ({ routeId, headsign, startDate, endDate, routeName, onLoadComplete }) => {
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const mapContainerRef = useRef(null);
    const mapRef = useRef(null);
    const markersRef = useRef([]);

    useEffect(() => {
        if (routeId && headsign) {
            fetchData();
        }
    }, [routeId, headsign, startDate, endDate]);

    const fetchData = async () => {
        setLoading(true);
        setError(null);

        try {
            // Convert dates to UTC format
            const formattedStartDate = convertToUTC(startDate);
            const formattedEndDate = convertToUTC(endDate);

            // Use the new API method for route signal delay
            const result = await ApiService.getRouteSignalDelay(routeId, headsign, formattedStartDate, formattedEndDate);

            if (result.success) {
                if (!result.data || !result.data.signals || result.data.signals.length === 0) {
                    setError('No signal data available for the selected time range');
                    setLoading(false);
                    return;
                }

                setData(result.data);

                if (onLoadComplete) {
                    onLoadComplete();
                }
            } else {
                setError(result.errorMessage || 'Failed to fetch data');
            }
        } catch (err) {
            console.error('Error fetching route signal delay data:', err);
            setError('An error occurred while fetching data');
        } finally {
            setLoading(false);
        }
    };

    // Initialize map
    useEffect(() => {
        console.log('Map initialization useEffect triggered');
        console.log('mapContainerRef.current:', mapContainerRef.current);
        console.log('mapboxgl.accessToken:', mapboxgl.accessToken);

        if (!mapContainerRef.current) {
            console.log('Map container ref not available yet');
            return;
        }

        // Check if Mapbox access token is available
        if (!mapboxgl.accessToken) {
            console.error('Mapbox access token is not set');
            setError('Map configuration error: Missing access token');
            return;
        }

        try {
            console.log('Creating Mapbox map...');
            const map = new mapboxgl.Map({
                container: mapContainerRef.current,
                style: 'mapbox://styles/mapbox/navigation-night-v1',
                center: [-81.007666, 46.486551], // Default center
                zoom: 11
            });

            console.log('Map created, adding controls...');
            map.addControl(new mapboxgl.NavigationControl(), 'top-right');

            // Wait for map to load before storing reference
            map.on('load', () => {
                console.log('Map loaded successfully');
                mapRef.current = map;
            });

            // Handle map errors
            map.on('error', (e) => {
                console.error('Mapbox error:', e);
                setError('Map loading error');
            });

            // Store map reference immediately for cleanup
            mapRef.current = map;
        } catch (error) {
            console.error('Error initializing map:', error);
            setError('Failed to initialize map');
        }

        return () => {
            console.log('Cleaning up map...');
            if (mapRef.current) {
                mapRef.current.remove();
                mapRef.current = null;
            }
        };
    }, []);

    // Update map with signal data
    useEffect(() => {
        if (!mapRef.current || !data || !data.signals) return;

        // Clear existing markers
        markersRef.current.forEach((marker) => marker.remove());
        markersRef.current = [];

        const signals = data.signals;
        if (signals.length === 0) return;

        // Calculate max delay for color scaling
        const maxDelay = Math.max(...signals.map((signal) => signal.averageRedLightDelay));

        // Add markers for each signal
        signals.forEach((signal) => {
            const color = getDelayColor(signal.averageRedLightDelay, maxDelay);
            const size = getCircleSize(signal.averageRedLightDelay, maxDelay);

            // Create custom marker element
            const el = document.createElement('div');
            el.style.width = `${size}px`;
            el.style.height = `${size}px`;
            el.style.backgroundColor = color;
            el.style.border = '2px solid white';
            el.style.borderRadius = '50%';
            el.style.cursor = 'pointer';
            el.style.opacity = '0.8';

            // Create popup
            const popup = new mapboxgl.Popup({ offset: 25 }).setHTML(`
                    <div style="padding: 10px; background: white; color: black; border-radius: 5px;">
                        <h3 style="margin: 0 0 8px 0; color: #333;">${signal.signalName}</h3>
                        <p style="margin: 2px 0;"><strong>Signal ID:</strong> ${signal.signalId}</p>
                        <p style="margin: 2px 0;"><strong>Average Red Light Delay:</strong> ${signal.averageRedLightDelay.toFixed(1)}s</p>
                        <p style="margin: 2px 0;"><strong>Vehicle Count:</strong> ${signal.vehicleCount}</p>
                        <p style="margin: 2px 0;"><strong>Location:</strong> ${signal.latitude.toFixed(6)}, ${signal.longitude.toFixed(
                6
            )}</p>
                    </div>
                `);

            // Create and add marker
            const marker = new mapboxgl.Marker(el).setLngLat([signal.longitude, signal.latitude]).setPopup(popup).addTo(mapRef.current);

            markersRef.current.push(marker);
        });

        // Fit map to show all signals
        if (signals.length > 0) {
            const bounds = new mapboxgl.LngLatBounds();
            signals.forEach((signal) => {
                bounds.extend([signal.longitude, signal.latitude]);
            });
            mapRef.current.fitBounds(bounds, { padding: 50 });
        }
    }, [data]);

    if (loading) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
                <Typography color="error">{error}</Typography>
            </Box>
        );
    }

    return (
        <Box sx={{ width: '100%', height: '100%', backgroundColor: '#424242', borderRadius: 1, p: 2 }}>
            <Typography variant="h6" sx={{ textAlign: 'center', mb: 0.5 }}>
                Route Signal Delay
            </Typography>
            <Typography variant="subtitle2" sx={{ textAlign: 'center', mb: 0.5 }}>
                {routeName || `${routeId} - ${headsign}`}
            </Typography>
            <Typography variant="caption" sx={{ display: 'block', color: '#aaa', textAlign: 'center', mb: 1 }}>
                {formatDateDisplay(startDate)} - {formatDateDisplay(endDate)}
            </Typography>

            {/* Map Container */}
            <Box
                sx={{
                    width: '100%',
                    height: '400px',
                    mb: 2,
                    borderRadius: 1,
                    overflow: 'hidden',
                    backgroundColor: '#555', // Fallback background
                    border: '1px solid #666' // Visible border for debugging
                }}
            >
                <div
                    ref={mapContainerRef}
                    style={{
                        width: '100%',
                        height: '100%',
                        minHeight: '400px' // Ensure minimum height
                    }}
                />
            </Box>

            {/* Legend */}
            <Box sx={{ mb: 2, p: 1, backgroundColor: '#333', borderRadius: 1 }}>
                <Typography variant="subtitle2" sx={{ mb: 1, color: '#fff' }}>
                    Legend: Circle size and color indicate average red light delay (larger/redder = more delay)
                </Typography>
            </Box>

            {/* Signal Summary */}
            {data && data.signals && data.signals.length > 0 && (
                <Box sx={{ backgroundColor: '#333', borderRadius: 1, p: 2 }}>
                    <Typography variant="subtitle2" sx={{ mb: 2, color: '#fff' }}>
                        Signal Summary ({data.signals.length} signals)
                    </Typography>
                    <Grid container spacing={2}>
                        {data.signals.map((signal) => (
                            <Grid item xs={12} sm={6} md={4} key={signal.signalId}>
                                <Card sx={{ backgroundColor: '#555', color: '#fff' }}>
                                    <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                                        <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                                            {signal.signalName}
                                        </Typography>
                                        <Typography variant="caption" sx={{ display: 'block', color: '#ccc' }}>
                                            ID: {signal.signalId}
                                        </Typography>
                                        <Typography variant="caption" sx={{ display: 'block', color: '#ccc' }}>
                                            Avg Delay: {signal.averageRedLightDelay.toFixed(1)}s
                                        </Typography>
                                        <Typography variant="caption" sx={{ display: 'block', color: '#ccc' }}>
                                            Vehicles: {signal.vehicleCount}
                                        </Typography>
                                    </CardContent>
                                </Card>
                            </Grid>
                        ))}
                    </Grid>
                </Box>
            )}
        </Box>
    );
};

export default RouteSignalDelayReport;

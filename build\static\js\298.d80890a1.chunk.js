"use strict";(self.webpackChunkmantis_free_react_admin_template=self.webpackChunkmantis_free_react_admin_template||[]).push([[298],{85298:(r,e,t)=>{t.r(e),t.d(e,{default:()=>g});var a=t(48089),l=t(16491),i=t(4139),o=t(93230),n=t(87233),s=t(55515),d=t(88341),c=t(44414);function h(r){let{bgcolor:e,title:t,data:s,dark:d,main:h}=r;return(0,c.jsx)(c.Fragment,{children:(0,c.jsx)(a.A,{sx:{"&.MuiPaper-root":{borderRadius:"0px"}},children:(0,c.jsx)(l.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",py:2.5,bgcolor:e,color:d?"grey.800":"#ffffff",border:h?"1px dashed":"1px solid transparent"},children:t&&(0,c.jsxs)(i.Ay,{container:!0,justifyContent:"space-around",alignItems:"center",children:[(0,c.jsx)(i.Ay,{item:!0,children:s&&(0,c.jsxs)(o.A,{spacing:.75,alignItems:"center",children:[(0,c.jsx)(n.A,{variant:"subtitle2",children:s.label}),(0,c.jsx)(n.A,{variant:"subtitle1",children:s.color})]})}),(0,c.jsx)(i.Ay,{item:!0,children:(0,c.jsx)(n.A,{variant:"subtitle1",color:"inherit",children:t})})]})})})})}const g=()=>(0,c.jsx)(d.A,{children:(0,c.jsxs)(i.Ay,{container:!0,spacing:3,children:[(0,c.jsx)(i.Ay,{item:!0,xs:12,sm:6,md:4,children:(0,c.jsx)(s.A,{title:"Primary Color",codeHighlight:!0,children:(0,c.jsxs)(o.A,{children:[(0,c.jsx)(h,{bgcolor:"primary.lighter",data:{label:"Blue-1",color:"#e6f7ff"},title:"primary.lighter",dark:!0}),(0,c.jsx)(h,{bgcolor:"primary.100",data:{label:"Blue-2",color:"#bae7ff"},title:"primary[100]",dark:!0}),(0,c.jsx)(h,{bgcolor:"primary.200",data:{label:"Blue-3",color:"#91d5ff"},title:"primary[200]",dark:!0}),(0,c.jsx)(h,{bgcolor:"primary.light",data:{label:"Blue-4",color:"#69c0ff"},title:"primary.light",dark:!0}),(0,c.jsx)(h,{bgcolor:"primary.400",data:{label:"Blue-5",color:"#40a9ff"},title:"primary[400]"}),(0,c.jsx)(h,{bgcolor:"primary.main",data:{label:"Blue-6",color:"#1890ff"},title:"primary.main",main:!0}),(0,c.jsx)(h,{bgcolor:"primary.dark",data:{label:"Blue-7",color:"#096dd9"},title:"primary.dark"}),(0,c.jsx)(h,{bgcolor:"primary.700",data:{label:"Blue-8",color:"#0050b3"},title:"primary[700]"}),(0,c.jsx)(h,{bgcolor:"primary.darker",data:{label:"Blue-9",color:"#003a8c"},title:"primary.darker"}),(0,c.jsx)(h,{bgcolor:"primary.900",data:{label:"Blue-10",color:"#002766"},title:"primary.900"})]})})}),(0,c.jsx)(i.Ay,{item:!0,xs:12,sm:6,md:4,children:(0,c.jsx)(s.A,{title:"Secondary Color",codeHighlight:!0,children:(0,c.jsxs)(o.A,{children:[(0,c.jsx)(h,{bgcolor:"secondary.lighter",data:{label:"Grey-1",color:"#fafafa"},title:"secondary.lighter",dark:!0}),(0,c.jsx)(h,{bgcolor:"secondary.100",data:{label:"Grey-2",color:"#f5f5f5"},title:"secondary[100]",dark:!0}),(0,c.jsx)(h,{bgcolor:"secondary.200",data:{label:"Grey-3",color:"#f0f0f0"},title:"secondary[200]",dark:!0}),(0,c.jsx)(h,{bgcolor:"secondary.light",data:{label:"Grey-4",color:"#d9d9d9"},title:"secondary.light",dark:!0}),(0,c.jsx)(h,{bgcolor:"secondary.400",data:{label:"Grey-5",color:"#bfbfbf"},title:"secondary[400]",dark:!0}),(0,c.jsx)(h,{bgcolor:"secondary.main",data:{label:"Grey-6",color:"#8c8c8c"},title:"secondary.main",main:!0}),(0,c.jsx)(h,{bgcolor:"secondary.600",data:{label:"Grey-7",color:"#595959"},title:"secondary.600"}),(0,c.jsx)(h,{bgcolor:"secondary.dark",data:{label:"Grey-8",color:"#262626"},title:"secondary.dark"}),(0,c.jsx)(h,{bgcolor:"secondary.800",data:{label:"Grey-9",color:"#141414"},title:"secondary[800]"}),(0,c.jsx)(h,{bgcolor:"secondary.darker",data:{label:"Grey-10",color:"#000000"},title:"secondary.darker"})]})})}),(0,c.jsx)(i.Ay,{item:!0,xs:12,sm:6,md:4,children:(0,c.jsx)(s.A,{title:"Other Color",codeHighlight:!0,children:(0,c.jsxs)(o.A,{children:[(0,c.jsx)(h,{bgcolor:"secondary.A100",data:{label:"Grey-A1",color:"#ffffff"},title:"secondary.A100",dark:!0}),(0,c.jsx)(h,{bgcolor:"secondary.A200",data:{label:"Grey-A2",color:"#434343"},title:"secondary.A200"}),(0,c.jsx)(h,{bgcolor:"secondary.A300",data:{label:"Grey-A3",color:"#1f1f1f"},title:"secondary.A300"})]})})}),(0,c.jsx)(i.Ay,{item:!0,xs:12,sm:6,md:4,children:(0,c.jsx)(s.A,{title:"Success Color",codeHighlight:!0,children:(0,c.jsxs)(o.A,{children:[(0,c.jsx)(h,{bgcolor:"success.lighter",data:{label:"Green-1",color:"#f6ffed"},title:"success.lighter",dark:!0}),(0,c.jsx)(h,{bgcolor:"success.light",data:{label:"Green-4",color:"#95de64"},title:"success.light",dark:!0}),(0,c.jsx)(h,{bgcolor:"success.main",data:{label:"Green-6",color:"#52c41a"},title:"success.main",main:!0}),(0,c.jsx)(h,{bgcolor:"success.dark",data:{label:"Green-8",color:"#237804"},title:"success.dark"}),(0,c.jsx)(h,{bgcolor:"success.darker",data:{label:"Green-10",color:"#092b00"},title:"success.darker"})]})})}),(0,c.jsx)(i.Ay,{item:!0,xs:12,sm:6,md:4,children:(0,c.jsx)(s.A,{title:"Error Color",codeHighlight:!0,children:(0,c.jsxs)(o.A,{children:[(0,c.jsx)(h,{bgcolor:"error.lighter",data:{label:"Red-1",color:"#fff1f0"},title:"error.lighter",dark:!0}),(0,c.jsx)(h,{bgcolor:"error.light",data:{label:"Red-4",color:"#ff7875"},title:"error.light",dark:!0}),(0,c.jsx)(h,{bgcolor:"error.main",data:{label:"Red-6",color:"#f5222d"},title:"error.main",main:!0}),(0,c.jsx)(h,{bgcolor:"error.dark",data:{label:"Red-8",color:"#a8071a"},title:"error.dark"}),(0,c.jsx)(h,{bgcolor:"error.darker",data:{label:"Red-10",color:"#5c0011"},title:"error.darker"})]})})}),(0,c.jsx)(i.Ay,{item:!0,xs:12,sm:6,md:4,children:(0,c.jsx)(s.A,{title:"Warning Color",codeHighlight:!0,children:(0,c.jsxs)(o.A,{children:[(0,c.jsx)(h,{bgcolor:"warning.lighter",data:{label:"Gold-1",color:"#fffbe6"},title:"warning.lighter",dark:!0}),(0,c.jsx)(h,{bgcolor:"warning.light",data:{label:"Gold-4",color:"#ffd666"},title:"warning.light",dark:!0}),(0,c.jsx)(h,{bgcolor:"warning.main",data:{label:"Gold-6",color:"#faad14"},title:"warning.main",main:!0}),(0,c.jsx)(h,{bgcolor:"warning.dark",data:{label:"Gold-8",color:"#ad6800"},title:"warning.dark"}),(0,c.jsx)(h,{bgcolor:"warning.darker",data:{label:"Gold-10",color:"#613400"},title:"warning.darker"})]})})})]})})},88341:(r,e,t)=>{t.d(e,{A:()=>$});var a=t(9950),l=t(98587),i=t(58168),o=t(72004),n=t(88283),s=t(88465),d=t(97161),c=t(97497),h=t(59254),g=t(18463),m=t(1763),b=t(423);function x(r){return(0,b.Ay)("MuiSkeleton",r)}(0,m.A)("MuiSkeleton",["root","text","rectangular","rounded","circular","pulse","wave","withChildren","fitContent","heightAuto"]);var f=t(44414);const u=["animation","className","component","height","style","variant","width"];let y,j,p,A,k=r=>r;const w=(0,n.keyframes)(y||(y=k`
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.4;
  }

  100% {
    opacity: 1;
  }
`)),v=(0,n.keyframes)(j||(j=k`
  0% {
    transform: translateX(-100%);
  }

  50% {
    /* +0.5s of delay between each loop */
    transform: translateX(100%);
  }

  100% {
    transform: translateX(100%);
  }
`)),C=(0,h.Ay)("span",{name:"MuiSkeleton",slot:"Root",overridesResolver:(r,e)=>{const{ownerState:t}=r;return[e.root,e[t.variant],!1!==t.animation&&e[t.animation],t.hasChildren&&e.withChildren,t.hasChildren&&!t.width&&e.fitContent,t.hasChildren&&!t.height&&e.heightAuto]}})((r=>{let{theme:e,ownerState:t}=r;const a=(0,d.l_)(e.shape.borderRadius)||"px",l=(0,d.db)(e.shape.borderRadius);return(0,i.A)({display:"block",backgroundColor:e.vars?e.vars.palette.Skeleton.bg:(0,c.X4)(e.palette.text.primary,"light"===e.palette.mode?.11:.13),height:"1.2em"},"text"===t.variant&&{marginTop:0,marginBottom:0,height:"auto",transformOrigin:"0 55%",transform:"scale(1, 0.60)",borderRadius:`${l}${a}/${Math.round(l/.6*10)/10}${a}`,"&:empty:before":{content:'"\\00a0"'}},"circular"===t.variant&&{borderRadius:"50%"},"rounded"===t.variant&&{borderRadius:(e.vars||e).shape.borderRadius},t.hasChildren&&{"& > *":{visibility:"hidden"}},t.hasChildren&&!t.width&&{maxWidth:"fit-content"},t.hasChildren&&!t.height&&{height:"auto"})}),(r=>{let{ownerState:e}=r;return"pulse"===e.animation&&(0,n.css)(p||(p=k`
      animation: ${0} 2s ease-in-out 0.5s infinite;
    `),w)}),(r=>{let{ownerState:e,theme:t}=r;return"wave"===e.animation&&(0,n.css)(A||(A=k`
      position: relative;
      overflow: hidden;

      /* Fix bug in Safari https://bugs.webkit.org/show_bug.cgi?id=68196 */
      -webkit-mask-image: -webkit-radial-gradient(white, black);

      &::after {
        animation: ${0} 2s linear 0.5s infinite;
        background: linear-gradient(
          90deg,
          transparent,
          ${0},
          transparent
        );
        content: '';
        position: absolute;
        transform: translateX(-100%); /* Avoid flash during server-side hydration */
        bottom: 0;
        left: 0;
        right: 0;
        top: 0;
      }
    `),v,(t.vars||t).palette.action.hover)})),G=a.forwardRef((function(r,e){const t=(0,g.b)({props:r,name:"MuiSkeleton"}),{animation:a="pulse",className:n,component:d="span",height:c,style:h,variant:m="text",width:b}=t,y=(0,l.A)(t,u),j=(0,i.A)({},t,{animation:a,component:d,variant:m,hasChildren:Boolean(y.children)}),p=(r=>{const{classes:e,variant:t,animation:a,hasChildren:l,width:i,height:o}=r,n={root:["root",t,a,l&&"withChildren",l&&!i&&"fitContent",l&&!o&&"heightAuto"]};return(0,s.A)(n,x,e)})(j);return(0,f.jsx)(C,(0,i.A)({as:d,ref:e,className:(0,o.A)(p.root,n),ownerState:j},y,{style:(0,i.A)({width:b,height:c},h)}))}));var S=t(93230),R=t(4139),B=t(55515);const $=r=>{let{children:e}=r;const[t,l]=(0,a.useState)(!0);(0,a.useEffect)((()=>{l(!1)}),[]);const i=(0,f.jsx)(B.A,{title:(0,f.jsx)(G,{sx:{width:{xs:120,md:180}}}),secondary:(0,f.jsx)(G,{animation:"wave",variant:"circular",width:24,height:24}),children:(0,f.jsxs)(S.A,{spacing:1,children:[(0,f.jsx)(G,{}),(0,f.jsx)(G,{sx:{height:64},animation:"wave",variant:"rectangular"}),(0,f.jsx)(G,{}),(0,f.jsx)(G,{})]})});return(0,f.jsxs)(f.Fragment,{children:[t&&(0,f.jsxs)(R.Ay,{container:!0,spacing:3,children:[(0,f.jsx)(R.Ay,{item:!0,xs:12,md:6,children:i}),(0,f.jsx)(R.Ay,{item:!0,xs:12,md:6,children:i}),(0,f.jsx)(R.Ay,{item:!0,xs:12,md:6,children:i}),(0,f.jsx)(R.Ay,{item:!0,xs:12,md:6,children:i})]}),!t&&e]})}},97161:(r,e,t)=>{function a(r){return String(parseFloat(r)).length===String(r).length}function l(r){return String(r).match(/[\d.\-+]*\s*(.*)/)[1]||""}function i(r){return parseFloat(r)}function o(r){return(e,t)=>{const a=l(e);if(a===t)return e;let o=i(e);"px"!==a&&("em"===a||"rem"===a)&&(o=i(e)*i(r));let n=o;if("px"!==t)if("em"===t)n=o/i(r);else{if("rem"!==t)return e;n=o/i(r)}return parseFloat(n.toFixed(5))+t}}function n(r){let{size:e,grid:t}=r;const a=e-e%t,l=a+t;return e-a<l-e?a:l}function s(r){let{lineHeight:e,pixels:t,htmlFontSize:a}=r;return t/(e*a)}function d(r){let{cssProperty:e,min:t,max:a,unit:l="rem",breakpoints:i=[600,900,1200],transform:o=null}=r;const n={[e]:`${t}${l}`},s=(a-t)/i[i.length-1];return i.forEach((r=>{let a=t+s*r;null!==o&&(a=o(a)),n[`@media (min-width:${r}px)`]={[e]:`${Math.round(1e4*a)/1e4}${l}`}})),n}t.d(e,{I3:()=>o,VR:()=>n,a9:()=>a,db:()=>i,l_:()=>l,qW:()=>s,yL:()=>d})}}]);
import React, { useEffect, useState } from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';
import ApiService from '../../../../utils/ApiService';
import Chart from 'react-apexcharts';

const TripLatencyHeatmapReport = ({ routeId, headsign, startDate, endDate, routeName, onLoadComplete }) => {
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [chartOptions, setChartOptions] = useState({});
    const [series, setSeries] = useState([]);
    const [chartHeight, setChartHeight] = useState(500); // Default height

    useEffect(() => {
        if (routeId && headsign) {
            fetchData();
        }
    }, [routeId, headsign, startDate, endDate]);

    const fetchData = async () => {
        setLoading(true);
        setError(null);

        try {
            const formattedStartDate = startDate.toISOString();
            const formattedEndDate = endDate.toISOString();

            const result = await ApiService.getAvgLatenessByRouteIdAndHeadsign(routeId, headsign, formattedStartDate, formattedEndDate);

            if (result.success) {
                if (!result.data || !result.data.tripLatenessResults || result.data.tripLatenessResults.length === 0) {
                    setError('No data available for the selected time range');
                    setLoading(false);
                    return;
                }

                setData(result.data);
                processChartData(result.data);

                if (onLoadComplete) {
                    onLoadComplete();
                }
            } else {
                setError(result.errorMessage || 'Failed to fetch data');
            }
        } catch (err) {
            console.error('Error fetching trip latency data:', err);
            setError('An error occurred while fetching data');
        } finally {
            setLoading(false);
        }
    };

    // Format timestamp for display
    const formatTime = (timestamp) => {
        if (!timestamp) return '';
        const date = new Date(timestamp);
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    };

    const processChartData = (data) => {
        if (!data || !data.stopTimes || !data.tripLatenessResults) return;

        // Sort stops by sequence
        const sortedStops = [...data.stopTimes].sort((a, b) => a.stopSequence - b.stopSequence);

        // Create stop segment labels
        const stopSegments = [];
        for (let i = 0; i < sortedStops.length - 1; i++) {
            const fromStop = sortedStops[i];
            const toStop = sortedStops[i + 1];
            stopSegments.push(`${fromStop.stopName} → ${toStop.stopName}`);
        }

        // Sort trips by start timestamp
        const sortedTrips = [...data.tripLatenessResults].sort((a, b) => {
            return new Date(a.tripStartTimestamp) - new Date(b.tripStartTimestamp);
        });

        // Calculate dynamic height based on data
        // Base height + additional height per trip and per stop segment
        const baseHeight = 200;
        const heightPerTrip = 20;
        const calculatedHeight = baseHeight + Math.max(sortedTrips.length * heightPerTrip);

        // Set minimum and maximum heights
        const minHeight = 400;
        const maxHeight = 1200;
        const finalHeight = Math.max(minHeight, Math.min(calculatedHeight, maxHeight));

        setChartHeight(finalHeight);

        // Create series data for heatmap
        const seriesData = [];

        // Process each trip and create a series for each
        sortedTrips.forEach((trip) => {
            if (!trip.latenessBetweenStops || trip.latenessBetweenStops.length === 0) return;

            const tripTime = formatTime(trip.tripStartTimestamp);
            const tripData = [];

            // For each stop segment, find the corresponding lateness data
            for (let i = 0; i < sortedStops.length - 1; i++) {
                const fromStop = sortedStops[i];
                const toStop = sortedStops[i + 1];

                const segment = trip.latenessBetweenStops.find(
                    (seg) => seg.fromStopId === fromStop.stopId && seg.toStopId === toStop.stopId
                );

                if (segment) {
                    tripData.push({
                        x: `${fromStop.stopName} → ${toStop.stopName}`,
                        y: Math.round(segment.averageLateness)
                    });
                } else {
                    // Add null for missing segments to maintain grid structure
                    tripData.push({
                        x: `${fromStop.stopName} → ${toStop.stopName}`,
                        y: null
                    });
                }
            }

            // Add this trip as a series
            seriesData.push({
                name: tripTime,
                data: tripData
            });
        });

        // Create chart options
        const options = {
            chart: {
                type: 'heatmap',
                height: 800,
                toolbar: {
                    show: true,
                    tools: {
                        download: true,
                        selection: true,
                        zoom: true,
                        zoomin: true,
                        zoomout: true,
                        pan: true,
                        reset: true
                    },
                    autoSelected: 'zoom'
                },
                background: 'transparent',
                foreColor: '#a9a9a9'
            },
            theme: {
                mode: 'dark',
                palette: 'palette1'
            },
            dataLabels: {
                enabled: false
            },
            xaxis: {
                type: 'category',
                labels: {
                    rotate: -45,
                    style: {
                        colors: '#a9a9a9',
                        fontSize: '10px'
                    }
                }
            },
            yaxis: {
                labels: {
                    style: {
                        colors: '#a9a9a9'
                    }
                }
            },
            plotOptions: {
                heatmap: {
                    enableShades: false,
                    shadeIntensity: 0.7,
                    radius: 0,
                    useFillColorAsStroke: false,
                    colorScale: {
                        ranges: [
                            { from: -999, to: -60, color: '#b2dfb0', name: 'Early' },
                            { from: -60, to: 120, color: '#8bc34a', name: 'On Time' },
                            { from: 120, to: 180, color: '#ffecb3', name: 'Slight Delay' },
                            { from: 180, to: 300, color: '#ffccbc', name: 'Moderate Delay' },
                            { from: 300, to: 9999, color: '#ef5350', name: 'Severe Delay' }
                        ]
                    }
                }
            },
            stroke: {
                width: 0.25,
                colors: ['#333']
            },
            grid: {
                borderColor: '#555555',
                xaxis: {
                    lines: {
                        show: true,
                        color: '#555555' // Lighter color for x-axis grid lines
                    }
                },
                yaxis: {
                    lines: {
                        show: true,
                        color: '#555555' // Lighter color for y-axis grid lines
                    }
                },
                row: {
                    colors: ['transparent'],
                    opacity: 0.5
                }
            },
            tooltip: {
                enabled: true,
                enabledOnSeries: undefined,
                shared: true,
                followCursor: false,
                intersect: false,
                inverseOrder: false,
                custom: function ({ series, seriesIndex, dataPointIndex, w }) {
                    const data = w.config.series[seriesIndex].data[dataPointIndex];
                    const value = data.y !== null ? `${data.y}s` : 'No data';
                    const tripData = sortedTrips[seriesIndex];
                    const tripId = tripData ? tripData.tripId : 'Unknown';

                    return `
                        <div class="apexcharts-tooltip-box" style="padding: 8px; background: #222; color: #fff; border: 1px solid #444;">
                            <div style="font-weight: bold; margin-bottom: 5px;">${w.config.series[seriesIndex].name}</div>
                            <div style="margin-bottom: 5px;">Trip: ${tripId}</div>
                            <div>Segment: ${data.x}</div>
                            <div style="margin-top: 5px; color: ${
                                data.y !== null ? (data.y > 180 ? '#ef5350' : data.y > 120 ? '#ffecb3' : '#8bc34a') : '#aaa'
                            }">
                                <strong>Lateness: ${value}</strong>
                            </div>
                        </div>
                    `;
                }
            }
        };

        setChartOptions(options);
        setSeries(seriesData);
    };

    const formatDateDisplay = (date) => {
        return date.toLocaleString('en-US', {
            month: 'short',
            day: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return (
        <Box sx={{ width: '100%', height: '100%', backgroundColor: '#424242', borderRadius: 1 }}>
            <Box
                sx={{
                    padding: '10px',
                    textAlign: 'center',
                    mb: 2
                }}
            >
                <Typography variant="h6" sx={{ color: '#fff', mb: 0.5 }}>
                    Schedule Adherence Heatmap
                </Typography>
                <Typography variant="subtitle2" sx={{ textAlign: 'center', mb: 0.5 }}>
                    {routeName || headsign}
                </Typography>
                <Typography variant="caption" sx={{ display: 'block', color: '#aaa', textAlign: 'center', mb: 1 }}>
                    {formatDateDisplay(startDate)} - {formatDateDisplay(endDate)}
                </Typography>
            </Box>

            <Box sx={{ width: '100%', height: `${chartHeight}px` }}>
                {loading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <CircularProgress />
                    </Box>
                ) : error ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <Typography color="error">{error}</Typography>
                    </Box>
                ) : series.length > 0 ? (
                    <Chart options={chartOptions} series={series} type="heatmap" height="100%" />
                ) : (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <Typography>No data available for the selected time range</Typography>
                    </Box>
                )}
            </Box>
        </Box>
    );
};

export default TripLatencyHeatmapReport;

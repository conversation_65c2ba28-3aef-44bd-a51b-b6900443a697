import React from 'react';
import Chart from 'react-apexcharts';
import MainCard from 'components/MainCard';

const AverageSpeedChart = ({ data, compareOption, compareToOption }) => {
    const categories = data.map((item) => item.name || 'Unknown');
    const chartOptions = {
        chart: {
            type: 'bar',
            height: 250
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: '55%',
                endingShape: 'rounded'
            }
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            show: true,
            width: 2,
            colors: ['transparent']
        },
        xaxis: {
            categories: categories,
            labels: {
                style: {
                    colors: '#a9a9a9'
                }
            },
            axisTicks: {
                show: true,
                color: '#a9a9a9'
            }
        },
        yaxis: {
            labels: {
                style: {
                    colors: '#a9a9a9'
                }
            },
            title: {
                text: 'Speed (km/h)',
                style: {
                    color: '#a9a9a9'
                },
                labels: {
                    style: {
                        colors: '#a9a9a9'
                    }
                }
            },
            axisBorder: {
                show: true,
                color: '#a9a9a9'
            },
            axisTicks: {
                show: true,
                color: '#a9a9a9'
            }
        },
        fill: {
            opacity: 1
        },
        grid: {
            borderColor: '#a9a9a9',
            strokeDashArray: 3
        },
        legend: {
            labels: {
                colors: '#a9a9a9'
            }
        },
        tooltip: {
            theme: 'dark',
            style: {
                fontSize: '12px',
                fontFamily: undefined,
                colors: ['#a9a9a9']
            },
            y: {
                formatter: (val) => `${val} km/h`
            }
        }
    };

    const series = [
        {
            name: compareOption,
            data: data.map((item) => item[compareOption] || 0),
            color: '#71d3c8'
        },
        {
            name: compareToOption,
            data: data.map((item) => item[compareToOption] || 0),
            color: '#dbaa32'
        }
    ];

    return (
        <MainCard title="Average Speed">
            <Chart options={chartOptions} series={series} type="bar" height={250} />
        </MainCard>
    );
};

export default AverageSpeedChart;

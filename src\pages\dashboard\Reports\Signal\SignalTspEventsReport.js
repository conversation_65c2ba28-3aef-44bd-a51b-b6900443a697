import React, { useEffect, useState } from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';
import ApiService from '../../../../utils/ApiService';
import ApexCharts from 'react-apexcharts';

const SignalTspEventsReport = ({
    signalId,
    signalName,
    startDate,
    endDate,
    primaryRoad, // Add primaryRoad prop
    crossRoad, // Add crossRoad prop
    onLoadComplete
}) => {
    const [series, setSeries] = useState([]);
    const [eventLabels, setEventLabels] = useState([]); // Add state for event labels
    const [chartHeight, setChartHeight] = useState(550); // Default height

    const [options, setOptions] = useState({
        chart: {
            height: 550,
            type: 'scatter',
            zoom: {
                enabled: true,
                type: 'x',
                autoScaleYaxis: true
            },
            toolbar: {
                show: true,
                tools: {
                    download: true,
                    selection: true,
                    zoom: true,
                    zoomin: true,
                    zoomout: true,
                    pan: true,
                    reset: true
                },
                export: {
                    csv: {
                        filename: undefined, // Setting to undefined disables CSV export
                        headerCategory: 'category',
                        headerValue: 'value'
                    },
                    svg: {
                        filename: `tsp-events-${signalId}`
                    },
                    png: {
                        filename: `tsp-events-${signalId}`
                    }
                },
                autoSelected: 'zoom',
                style: {
                    backgroundColor: 'transparent',
                    color: '#a9a9a9',
                    borderRadius: '6px'
                }
            },
            background: 'transparent',
            foreColor: '#a9a9a9'
        },
        theme: {
            mode: 'dark',
            palette: 'palette1'
        },
        dataLabels: {
            enabled: false
        },
        grid: {
            xaxis: {
                lines: {
                    show: true,
                    color: '#555555' // Lighter color for x-axis grid lines
                }
            },
            yaxis: {
                lines: {
                    show: true,
                    color: '#555555' // Lighter color for y-axis grid lines
                }
            },
            borderColor: '#555555', // Lighter border color
            strokeDashArray: 0
        },
        xaxis: {
            type: 'datetime',
            labels: {
                datetimeUTC: false,
                format: 'HH:mm:ss'
            }
        },
        yaxis: {
            min: 0,
            max: 1, // Will be updated when data is loaded
            tickAmount: 1, // Will be updated when data is loaded
            labels: {
                formatter: function (val) {
                    return ''; // Will be updated when data is loaded
                },
                style: {
                    fontSize: '10px'
                }
            }
        },
        tooltip: {
            theme: 'dark',
            shared: false,
            x: {
                format: 'MMM dd, yyyy HH:mm:ss'
            },
            custom: function ({ series, seriesIndex, dataPointIndex, w }) {
                const point = w.config.series[seriesIndex].data[dataPointIndex];
                const time = new Date(point.x).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
                const eventName = point.eventName;
                const description = point.description || '';
                const parameter = point.parameter || '';

                return `
                    <div class="apexcharts-tooltip-box" style="padding: 8px; background: #222; color: #fff; border: 1px solid #444;">
                        <div style="font-weight: bold; margin-bottom: 5px;">${time}</div>
                        <div style="margin-bottom: 5px;">${eventName} idx:${point.indexFormatted}</div>
                        <div style="margin-top: 5px;">
                            <strong>${description}</strong>
                        </div>
                    </div>
                `;
            }
        },
        legend: {
            show: false // Hide the legend since we're showing labels on Y-axis
        }
    });
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        if (signalId) {
            fetchData();
        }
    }, [signalId, startDate, endDate]);

    const fetchData = async () => {
        setLoading(true);
        setError(null);

        try {
            // Convert dates to UTC format
            const formattedStartDate = convertToUTC(startDate);
            const formattedEndDate = convertToUTC(endDate);

            // Call the API to get TSP events
            const result = await ApiService.getSignalTspEvents(signalId, formattedStartDate, formattedEndDate);

            if (result.success) {
                if (!result.events || result.events.length === 0) {
                    setError('No data available for the selected time range');
                    setLoading(false);
                    return;
                }

                // Process the events data
                processEventsData(result.events);

                if (onLoadComplete) {
                    onLoadComplete();
                }
            } else {
                setError(result.errorMessage || 'Failed to fetch data');
            }
        } catch (err) {
            console.error('Error fetching TSP events data:', err);
            setError('An error occurred while fetching data');
        } finally {
            setLoading(false);
        }
    };

    // Convert local date to UTC ISO string
    const convertToUTC = (date) => {
        return date.toISOString();
    };

    // Process the events data to create the chart series
    const processEventsData = (events) => {
        // Group events by eventName + indexFormatted
        const eventGroups = {};
        const labels = [];

        events.forEach((event) => {
            const eventKey = `${event.eventName} idx:${event.indexFormatted}`;
            if (!eventGroups[eventKey]) {
                eventGroups[eventKey] = [];
                labels.push(eventKey);
            }

            eventGroups[eventKey].push({
                x: new Date(event.timestamp).getTime(),
                y: labels.indexOf(eventKey) + 1, // Y-axis position based on event type
                eventName: event.eventName,
                indexFormatted: event.indexFormatted,
                description: event.description,
                parameter: event.parameter
            });
        });

        // Save the event labels
        setEventLabels(labels);

        // Calculate dynamic height based on number of events and event types
        const baseHeight = 350;
        const heightPerEventType = 10;

        // Count unique event types and total events
        const eventTypes = new Set(events.map((event) => event.eventType)).size;
        const totalEvents = events.length;

        // Calculate height based on event types and total events
        const calculatedHeight = baseHeight + heightPerEventType * eventTypes;

        // Set minimum and maximum heights
        const minHeight = 550;
        const maxHeight = 1000;
        const finalHeight = Math.max(minHeight, Math.min(calculatedHeight, maxHeight));

        setChartHeight(finalHeight);

        // Convert to series format
        const seriesData = labels.map((eventKey) => ({
            name: eventKey,
            data: eventGroups[eventKey]
        }));

        setSeries(seriesData);

        // Update chart options with the event labels
        setOptions({
            chart: {
                height: chartHeight,
                type: 'scatter',
                zoom: {
                    enabled: true,
                    type: 'x',
                    autoScaleYaxis: true
                },
                toolbar: {
                    show: true,
                    tools: {
                        download: true,
                        selection: true,
                        zoom: true,
                        zoomin: true,
                        zoomout: true,
                        pan: true,
                        reset: true
                    },
                    autoSelected: 'zoom',
                    style: {
                        borderRadius: '6px'
                    }
                },
                background: 'transparent'
            },
            theme: {
                mode: 'dark',
                palette: 'palette1'
            },
            dataLabels: {
                enabled: false
            },
            grid: {
                xaxis: {
                    lines: {
                        show: true,
                        color: '#555555' // Lighter color for x-axis grid lines
                    }
                },
                yaxis: {
                    lines: {
                        show: true,
                        color: '#555555' // Lighter color for y-axis grid lines
                    }
                },
                borderColor: '#555555', // Lighter border color
                strokeDashArray: 0
            },
            xaxis: {
                type: 'datetime',
                labels: {
                    datetimeUTC: false,
                    format: 'HH:mm:ss'
                }
            },
            yaxis: {
                min: 0,
                max: labels.length,
                tickAmount: labels.length,
                labels: {
                    formatter: function (val) {
                        // Return the label for the nearest integer value
                        const index = Math.round(val) - 1;
                        return index >= 0 && index < labels.length ? labels[index] : '';
                    },
                    style: {
                        fontSize: '10px',
                        colors: '#a9a9a9'
                    }
                }
            },
            tooltip: {
                theme: 'dark',
                shared: false,
                x: {
                    format: 'MMM dd, yyyy HH:mm:ss'
                },
                custom: function ({ series, seriesIndex, dataPointIndex, w }) {
                    const point = w.config.series[seriesIndex].data[dataPointIndex];
                    const time = new Date(point.x).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
                    const eventName = point.eventName;
                    const description = point.description || '';
                    const parameter = point.parameter || '';

                    return `
                        <div class="apexcharts-tooltip-box" style="padding: 8px; background: #222; color: #fff; border: 1px solid #444;">
                            <div style="font-weight: bold; margin-bottom: 5px;">${time}</div>
                            <div style="margin-bottom: 5px;">${eventName} idx: ${point.indexFormatted}</div>
                            <div style="margin-top: 5px;">
                                <strong>${description}</strong>
                            </div>
                        </div>
                    `;
                }
            },
            markers: {
                size: 4,
                strokeWidth: 0,
                hover: {
                    size: 6
                }
            },
            legend: {
                show: true // Hide the legend since we're showing labels on Y-axis
            }
        });
    };

    // Format date for display
    const formatDateDisplay = (date) => {
        return date.toLocaleString('en-US', {
            month: 'short',
            day: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    // Create intersection description
    const getIntersectionDescription = () => {
        if (primaryRoad && crossRoad) {
            return `${primaryRoad} & ${crossRoad}`;
        } else if (signalName) {
            return signalName;
        } else {
            return `Signal ID: ${signalId}`;
        }
    };

    return (
        <Box sx={{ height: '100%', width: '100%', p: 2, backgroundColor: '#424242', borderRadius: 1 }}>
            {/* Header content - Added title section */}
            <Typography variant="h6" sx={{ textAlign: 'center', mb: 0.5 }}>
                Signal Controller TSP Events
            </Typography>
            <Typography variant="subtitle2" sx={{ textAlign: 'center', mb: 0.5 }}>
                {getIntersectionDescription()}
            </Typography>
            <Typography variant="caption" sx={{ display: 'block', color: '#aaa', textAlign: 'center', mb: 1 }}>
                {formatDateDisplay(startDate)} - {formatDateDisplay(endDate)}
            </Typography>

            <Box sx={{ height: `${chartHeight}px`, width: '100%' }}>
                {loading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <CircularProgress />
                    </Box>
                ) : error ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <Typography color="error">{error}</Typography>
                    </Box>
                ) : series.length > 0 ? (
                    <ApexCharts options={options} series={series} type="scatter" height="100%" width="100%" />
                ) : (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <Typography>No data available for the selected time range</Typography>
                    </Box>
                )}
            </Box>
        </Box>
    );
};

export default SignalTspEventsReport;

import React, { useEffect, useState } from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';
import ApiService from '../../../../utils/ApiService';

const TripLatencyReport = ({ routeId, headsign, startDate, endDate, routeName, onLoadComplete }) => {
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [trips, setTrips] = useState([]);
    const [stops, setStops] = useState([]);

    useEffect(() => {
        if (routeId && headsign) {
            fetchData();
        }
    }, [routeId, headsign, startDate, endDate]);

    const fetchData = async () => {
        setLoading(true);
        setError(null);

        try {
            // Convert dates to UTC format
            const formattedStartDate = startDate.toISOString();
            const formattedEndDate = endDate.toISOString();

            const result = await ApiService.getAvgLatenessByRouteIdAndHeadsign(routeId, headsign, formattedStartDate, formattedEndDate);

            if (result.success) {
                if (!result.data || !result.data.tripLatenessResults || result.data.tripLatenessResults.length === 0) {
                    setError('No data available for the selected time range');
                    setLoading(false);
                    return;
                }

                setData(result.data);
                processData(result.data);

                if (onLoadComplete) {
                    onLoadComplete();
                }
            } else {
                setError(result.errorMessage || 'Failed to fetch data');
            }
        } catch (err) {
            console.error('Error fetching trip latency data:', err);
            setError('An error occurred while fetching data');
        } finally {
            setLoading(false);
        }
    };

    const processData = (data) => {
        // Get unique tripIds
        const uniqueTrips = data.tripLatenessResults.map((trip) => trip.tripId);
        setTrips(uniqueTrips);

        // Get unique stops from stopTimes and sort by sequence
        if (data.stopTimes && data.stopTimes.length > 0) {
            const sortedStops = [...data.stopTimes].sort((a, b) => a.stopSequence - b.stopSequence);
            const uniqueStops = sortedStops.map((stop) => ({
                id: stop.stopId,
                name: stop.stopName
            }));
            setStops(uniqueStops);
        }
    };

    // Format timestamp for display
    const formatTime = (timestamp) => {
        if (!timestamp) return '';
        const date = new Date(timestamp);
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    };

    // Color scale for lateness
    const getColorForLateness = (lateness) => {
        // Create a color scale with specific thresholds
        if (lateness < -60) {
            return '#00ffff'; // Aqua for early (less than -60 seconds)
        } else if (lateness >= -60 && lateness < 120) {
            return '#4caf50'; // Green for on time to slightly late (-60 to 120 seconds)
        } else if (lateness >= 120 && lateness < 180) {
            return '#ffeb3b'; // Yellow for moderately late (120 to 180 seconds)
        } else if (lateness >= 180 && lateness < 300) {
            return '#ff9800'; // Orange for late (180 to 300 seconds)
        } else {
            return '#f44336'; // Red for very late (300+ seconds)
        }
    };

    // Calculate position based on shapeDistTraveled
    const calculateStopPosition = (stopId) => {
        if (!data || !data.stopTimes) return 0;

        const stopTime = data.stopTimes.find((st) => st.stopId === stopId);
        if (!stopTime) return 0;

        const maxDist = Math.max(...data.stopTimes.map((st) => st.shapeDistTraveled));
        return (stopTime.shapeDistTraveled / maxDist) * 100;
    };

    // Find lateness data for a specific segment
    const getLatenessForSegment = (tripData, fromStopId, toStopId) => {
        if (!tripData || !tripData.latenessBetweenStops) return null;

        return tripData.latenessBetweenStops.find((segment) => segment.fromStopId === fromStopId && segment.toStopId === toStopId);
    };

    return (
        <Box sx={{ width: '100%' }}>
            <Typography variant="h6" sx={{ textAlign: 'center', mb: 0.5 }}>
                Trip Latency Analysis
            </Typography>
            <Typography variant="subtitle2" sx={{ textAlign: 'center', mb: 0.5 }}>
                {routeName || `${routeId} - ${headsign}`}
            </Typography>
            <Typography variant="caption" sx={{ display: 'block', color: '#aaa', textAlign: 'center', mb: 1 }}>
                {startDate.toLocaleDateString()} - {endDate.toLocaleDateString()}
            </Typography>

            {/* Main chart container with padding for labels */}
            <Box
                sx={{
                    width: '100%',
                    height: '1000px',
                    overflowX: 'auto',
                    overflowY: 'auto',
                    paddingTop: '30px', // Add padding for trip time labels
                    paddingBottom: '20px' // Add padding for bottom
                }}
            >
                {loading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <CircularProgress />
                    </Box>
                ) : error ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <Typography color="error">{error}</Typography>
                    </Box>
                ) : data ? (
                    <Box sx={{ display: 'flex', flexDirection: 'row', height: 'calc(100% - 50px)' }}>
                        {/* Y-axis labels (stops) */}
                        <Box
                            sx={{
                                width: '160px',
                                borderRight: '1px solid #555',
                                display: 'flex',
                                flexDirection: 'column',
                                justifyContent: 'space-between',
                                paddingRight: '10px',
                                position: 'relative'
                            }}
                        >
                            {stops.map((stop, index) => (
                                <Typography
                                    key={stop.id}
                                    variant="caption"
                                    sx={{
                                        position: 'absolute',
                                        left: 0,
                                        top: `${calculateStopPosition(stop.id)}%`,
                                        transform: 'translateY(-50%)',
                                        textAlign: 'right',
                                        width: '150px'
                                    }}
                                >
                                    {stop.name}
                                </Typography>
                            ))}
                        </Box>

                        {/* Chart area */}
                        <Box
                            sx={{
                                flex: 1,
                                display: 'flex',
                                flexDirection: 'row',
                                overflowX: 'auto',
                                position: 'relative',
                                borderBottom: '1px solid #555'
                            }}
                        >
                            {/* Horizontal grid lines for stops */}
                            {stops.map((stop) => (
                                <div
                                    key={`grid-${stop.id}`}
                                    style={{
                                        position: 'absolute',
                                        left: 0,
                                        right: 0,
                                        top: `${calculateStopPosition(stop.id)}%`,
                                        height: '1px',
                                        backgroundColor: '#777',
                                        opacity: 0.7,
                                        zIndex: 5
                                    }}
                                />
                            ))}

                            {data.tripLatenessResults.map((tripData, tripIndex) => {
                                if (!tripData.latenessBetweenStops || tripData.latenessBetweenStops.length === 0) return null;

                                return (
                                    <Box
                                        key={tripData.tripId}
                                        sx={{
                                            width: '40px',
                                            height: '100%',
                                            marginRight: '5px',
                                            position: 'relative',
                                            backgroundColor: '#333',
                                            zIndex: 2
                                        }}
                                    >
                                        {/* Trip time label */}
                                        <Typography
                                            variant="caption"
                                            sx={{
                                                position: 'absolute',
                                                top: '-25px',
                                                left: 0,
                                                width: '100%',
                                                textAlign: 'center',
                                                transform: 'rotate(-45deg)',
                                                transformOrigin: 'left bottom',
                                                fontSize: '10px',
                                                whiteSpace: 'nowrap'
                                            }}
                                        >
                                            {formatTime(tripData.tripStartTimestamp)}
                                        </Typography>

                                        {/* Render segments with color based on lateness */}
                                        {stops.map((stop, stopIndex) => {
                                            if (stopIndex === stops.length - 1) return null;

                                            const fromStop = stop;
                                            const toStop = stops[stopIndex + 1];

                                            const segment = getLatenessForSegment(tripData, fromStop.id, toStop.id);
                                            if (!segment) return null;

                                            const fromPosition = calculateStopPosition(fromStop.id);
                                            const toPosition = calculateStopPosition(toStop.id);
                                            const segmentHeight = Math.abs(toPosition - fromPosition);

                                            return (
                                                <Box
                                                    key={`${tripData.tripId}-${fromStop.id}-${toStop.id}`}
                                                    sx={{
                                                        position: 'absolute',
                                                        left: 0,
                                                        top: `${fromPosition}%`,
                                                        width: '100%',
                                                        height: `${segmentHeight}%`,
                                                        backgroundColor: getColorForLateness(segment.averageLateness),
                                                        transition: 'all 0.3s ease'
                                                    }}
                                                    title={`From: ${fromStop.name} To: ${toStop.name}, Avg Lateness: ${Math.round(
                                                        segment.averageLateness
                                                    )}s`}
                                                />
                                            );
                                        })}
                                    </Box>
                                );
                            })}
                        </Box>
                    </Box>
                ) : (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <Typography>No data available for the selected time range</Typography>
                    </Box>
                )}
            </Box>

            {/* Legend */}
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mr: 3 }}>
                    <Box sx={{ width: 20, height: 20, backgroundColor: '#00ffff', mr: 1 }} />
                    <Typography variant="caption">Early (Less than -60s)</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mr: 3 }}>
                    <Box sx={{ width: 20, height: 20, backgroundColor: '#4caf50', mr: 1 }} />
                    <Typography variant="caption">On Time (-60s to +120s)</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mr: 3 }}>
                    <Box sx={{ width: 20, height: 20, backgroundColor: '#ffeb3b', mr: 1 }} />
                    <Typography variant="caption">Slight Delay (+120s to +180s)</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mr: 3 }}>
                    <Box sx={{ width: 20, height: 20, backgroundColor: '#ff9800', mr: 1 }} />
                    <Typography variant="caption">Moderate Delay (+180s to +300s)</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box sx={{ width: 20, height: 20, backgroundColor: '#f44336', mr: 1 }} />
                    <Typography variant="caption">Severe Delay (More than +300s)</Typography>
                </Box>
            </Box>
        </Box>
    );
};

export default TripLatencyReport;

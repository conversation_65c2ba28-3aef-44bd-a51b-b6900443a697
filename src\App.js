// project import
import ThemeCustomization from 'themes';
import ScrollTop from 'components/ScrollTop';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import MainRoutes from 'routes/MainRoutes';
import LoginRoutes from 'routes/LoginRoutes';
import { store } from 'store';
// third-party
import { Provider as ReduxProvider } from 'react-redux';

// ==============================|| APP - THEME, ROUTER, LOCAL  ||============================== //

const routes = createBrowserRouter([MainRoutes, LoginRoutes]);
const App = () => (
    <ReduxProvider store={store}>
        <ThemeCustomization>
            <RouterProvider router={routes} />
        </ThemeCustomization>
    </ReduxProvider>
);

export default App;

import React, { useState, useEffect } from 'react';
import { Box, Select, MenuItem, Button, TextField, IconButton, CircularProgress, Typography } from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import TripTravelTimeReport from './Transit/TripTravelTimeReport';
import TripLatencyReport from './Transit/TripLatencyReport';
import TripLatencyHeatmapReport from './Transit/TripLatencyHeatmapReport';
import AverageRouteSignalDelayReport from './Transit/AverageRouteSignalDelayReport';
import RouteSignalDelayReport from './Transit/RouteSignalDelayReport';
import SignalTspEventsReport from './Signal/SignalTspEventsReport';
import SignalTspEventsTableReport from './Signal/SignalTspEventsTableReport';
import CloseIcon from '@mui/icons-material/Close';

export const ReportsTab = ({ currentTab, selectedRoutes, selectedSignals }) => {
    const [selectedReport, setSelectedReport] = useState('');
    const [startDate, setStartDate] = useState(() => {
        const date = new Date();
        date.setHours(date.getHours() - 1); // Set start date to one hour before current time
        return date;
    });
    const [endDate, setEndDate] = useState(new Date());
    const [reports, setReports] = useState([]);
    const [isGenerating, setIsGenerating] = useState(false);

    // Add state to track if too many items are selected
    const [tooManySelected, setTooManySelected] = useState(false);

    // Maximum number of items allowed for report generation
    const MAX_ITEMS_ALLOWED = 10;

    // Function to handle report load completion
    const handleReportLoadComplete = (reportId) => {
        console.log(`Report ${reportId} loaded successfully`);
    };

    const reportOptions = {
        1: ['Signal Controller TSP Events', 'Signal Controller TSP Raw Events'],
        2: ['Trip Travel Time', 'Schedule Adherence Heatmap', 'Average Route Signal Delay', 'Route Signal Delay'],
        3: ['Report 1', 'Report 2'],
        4: ['Report 1', 'Report 2']
    };

    // Auto-select first report option when tab changes
    useEffect(() => {
        if (reportOptions[currentTab] && reportOptions[currentTab].length > 0) {
            setSelectedReport(reportOptions[currentTab][0]);
        } else {
            setSelectedReport('');
        }
    }, [currentTab]);

    // Check if too many items are selected
    useEffect(() => {
        if (currentTab === '1' && selectedSignals && selectedSignals.length > MAX_ITEMS_ALLOWED) {
            setTooManySelected(true);
        } else if (currentTab === '2' && selectedRoutes && selectedRoutes.length > MAX_ITEMS_ALLOWED) {
            setTooManySelected(true);
        } else {
            setTooManySelected(false);
        }
    }, [currentTab, selectedSignals, selectedRoutes]);

    const handleReportChange = (event) => {
        setSelectedReport(event.target.value);
    };

    const generateReport = () => {
        if (!selectedReport) return;

        // Additional safety check
        if (tooManySelected) return;

        setIsGenerating(true);

        // For Trip Travel Time, Schedule Adherence Heatmap, Average Route Signal Delay, or Route Signal Delay report, generate one report per selected route
        if (
            (selectedReport === 'Trip Travel Time' ||
                selectedReport === 'Schedule Adherence Heatmap' ||
                selectedReport === 'Average Route Signal Delay' ||
                selectedReport === 'Route Signal Delay') &&
            selectedRoutes &&
            selectedRoutes.length > 0
        ) {
            const newReports = selectedRoutes.map((route) => ({
                id: Date.now() + Math.random(), // Ensure unique ID
                type: selectedReport,
                startDate,
                endDate,
                routeId: route.routeId,
                routeName: `${route.routeShortName} - ${route.tripHeadsign}`,
                headsign: route.tripHeadsign // Add headsign from the selected route
            }));

            setReports([...reports, ...newReports]);
        }
        // For TSP Events or TSP Events Table report, generate one report per selected signal
        else if (
            (selectedReport === 'Signal Controller TSP Events' || selectedReport === 'Signal Controller TSP Raw Events') &&
            selectedSignals &&
            selectedSignals.length > 0
        ) {
            const newReports = selectedSignals.map((signal) => ({
                id: Date.now() + Math.random(), // Ensure unique ID
                type: selectedReport,
                startDate,
                endDate,
                signalId: signal.id,
                signalName: signal.name || `Signal ${signal.id}`,
                primaryRoad: signal.primaryRoad || '',
                crossRoad: signal.crossRoad || ''
            }));

            setReports([...reports, ...newReports]);
        } else {
            // For other report types
            const reportId = Date.now();
            const newReport = {
                id: reportId,
                type: selectedReport,
                startDate,
                endDate
            };
            setReports([...reports, newReport]);
        }

        setIsGenerating(false);
    };

    const removeReport = (reportId) => {
        setReports(reports.filter((report) => report.id !== reportId));
    };

    return (
        <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ p: 2, zIndex: 1, position: 'sticky', top: 0, backgroundColor: '#333' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, flexWrap: 'wrap', gap: 2 }}>
                    <Select value={selectedReport} onChange={handleReportChange} displayEmpty sx={{ minWidth: 150 }}>
                        <MenuItem value="">
                            <em>Select Report</em>
                        </MenuItem>
                        {reportOptions[currentTab] &&
                            reportOptions[currentTab].map((option) => (
                                <MenuItem key={option} value={option}>
                                    {option}
                                </MenuItem>
                            ))}
                    </Select>
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                            <DateTimePicker
                                label="Start Date"
                                value={startDate}
                                onChange={setStartDate}
                                slotProps={{ textField: { size: 'small' } }}
                            />
                            <DateTimePicker
                                label="End Date"
                                value={endDate}
                                onChange={setEndDate}
                                slotProps={{ textField: { size: 'small' } }}
                            />
                        </Box>
                    </LocalizationProvider>
                    <Button
                        variant="outlined"
                        onClick={generateReport}
                        disabled={isGenerating || !selectedReport || tooManySelected}
                        startIcon={isGenerating ? <CircularProgress size={20} color="inherit" /> : null}
                    >
                        Generate Report
                    </Button>
                </Box>
                {/* Error message for too many selected items */}
                {tooManySelected && (
                    <Typography
                        variant="body1"
                        color="error"
                        sx={{
                            mt: 2,
                            mb: 2,
                            textAlign: 'center',
                            fontSize: '1.1rem',
                            fontWeight: 'medium'
                        }}
                    >
                        Please select 10 or fewer items to generate reports
                    </Typography>
                )}

                {/* Existing error message for Trip Travel Time with improved styling */}
                {selectedReport === 'Trip Travel Time' && (!selectedRoutes || selectedRoutes.length === 0) && (
                    <Typography
                        variant="body1"
                        color="error"
                        sx={{
                            mt: 2,
                            mb: 2,
                            textAlign: 'center',
                            fontSize: '1.1rem',
                            fontWeight: 'medium'
                        }}
                    >
                        Please select at least one route from the Transit Routes list
                    </Typography>
                )}
            </Box>
            <Box sx={{ flex: 1, overflowY: 'auto', p: 2 }}>
                {reports.map((report, index) => (
                    <Box
                        key={report.id}
                        sx={{
                            border: '1px solid #444',
                            borderRadius: 1,
                            overflow: 'hidden',
                            height: 'auto',
                            minHeight: 500,
                            position: 'relative',
                            marginBottom: 3,
                            backgroundColor: 'rgba(0, 0, 0, 0.2)'
                        }}
                    >
                        <Box
                            sx={{
                                position: 'absolute',
                                top: 8,
                                right: 8,
                                zIndex: 10,
                                backgroundColor: 'rgba(0,0,0,0.5)',
                                borderRadius: '50%'
                            }}
                        >
                            <IconButton size="small" onClick={() => removeReport(report.id)} sx={{ color: 'white' }}>
                                <CloseIcon fontSize="small" />
                            </IconButton>
                        </Box>
                        {report.type === 'Trip Travel Time' && (
                            <TripTravelTimeReport
                                routeId={report.routeId}
                                headsign={report.headsign}
                                startDate={report.startDate}
                                endDate={report.endDate}
                                routeName={report.routeName}
                                onLoadComplete={() => console.log(`Report ${report.id} loaded`)}
                            />
                        )}
                        {report.type === 'Schedule Adherence Heatmap' && (
                            <TripLatencyHeatmapReport
                                routeId={report.routeId}
                                headsign={report.headsign}
                                startDate={report.startDate}
                                endDate={report.endDate}
                                routeName={report.routeName}
                                onLoadComplete={() => console.log(`Report ${report.id} loaded`)}
                            />
                        )}
                        {report.type === 'Average Route Signal Delay' && (
                            <AverageRouteSignalDelayReport
                                routeId={report.routeId}
                                headsign={report.headsign}
                                startDate={report.startDate}
                                endDate={report.endDate}
                                routeName={report.routeName}
                                onLoadComplete={() => console.log(`Report ${report.id} loaded`)}
                            />
                        )}
                        {report.type === 'Route Signal Delay' && (
                            <RouteSignalDelayReport
                                routeId={report.routeId}
                                headsign={report.headsign}
                                startDate={report.startDate}
                                endDate={report.endDate}
                                routeName={report.routeName}
                                onLoadComplete={() => console.log(`Report ${report.id} loaded`)}
                            />
                        )}
                        {report.type === 'Signal Controller TSP Events' && (
                            <SignalTspEventsReport
                                signalId={report.signalId}
                                signalName={report.signalName}
                                primaryRoad={report.primaryRoad}
                                crossRoad={report.crossRoad}
                                startDate={report.startDate}
                                endDate={report.endDate}
                                onLoadComplete={() => handleReportLoadComplete(report.id)}
                            />
                        )}
                        {report.type === 'Signal Controller TSP Raw Events' && (
                            <SignalTspEventsTableReport
                                signalId={report.signalId}
                                signalName={report.signalName}
                                primaryRoad={report.primaryRoad}
                                crossRoad={report.crossRoad}
                                startDate={report.startDate}
                                endDate={report.endDate}
                                onLoadComplete={() => handleReportLoadComplete(report.id)}
                            />
                        )}
                    </Box>
                ))}
                {reports.length === 0 && (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
                        <Typography variant="body1" color="#aaa">
                            No reports generated. Select a report type and time range, then click Generate Report.
                        </Typography>
                    </Box>
                )}
            </Box>
        </Box>
    );
};

export default ReportsTab;

"use strict";(self.webpackChunkmantis_free_react_admin_template=self.webpackChunkmantis_free_react_admin_template||[]).push([[339],{1339:(e,r,a)=>{a.r(r),a.d(r,{default:()=>B});var n=a(9950),s=a(96895),t=a(42617),o=a(4139),l=a(74489),i=a(35661),d=a(16491),m=a(41413),c=a(87233),u=a(52983),h=a(2046),x=a(57073),p=a(72145),g=a(37598),j=a(33174),A=a(54554);const b=e=>{let r=0;return e.length>5&&(r+=1),e.length>7&&(r+=1),(e=>new RegExp(/[0-9]/).test(e))(e)&&(r+=1),(e=>new RegExp(/[!#@$%^&*)(+=._-]/).test(e))(e)&&(r+=1),(e=>new RegExp(/[a-z]/).test(e)&&new RegExp(/[A-Z]/).test(e))(e)&&(r+=1),r};var w=a(12678),y=a(4729),v=a(11583),f=a(28429),k=a(42074),C=a(43939),S=a(60666),q=a(44414);(0,s.A)();const _=(0,s.A)({palette:{mode:"dark"}});const B=function(){(0,f.Zp)();const[e,r]=(0,n.useState)(!1),[a,s]=(0,n.useState)(),B=S.Ik().shape({username:S.Yj().max(20).required("Username is required"),email:S.Yj().email("Must be a valid email").max(50).required("Email is required"),password:S.Yj().max(255).required("Password is required")}),P=e=>{const r=b(e);var a;s((a=r)<2?{label:"Poor",color:"error.main"}:a<3?{label:"Weak",color:"warning.main"}:a<4?{label:"Normal",color:"warning.dark"}:a<5?{label:"Good",color:"success.main"}:a<6?{label:"Strong",color:"success.dark"}:{label:"Poor",color:"error.main"})};(0,n.useEffect)((()=>{P("")}),[]);const E=(0,C.Wx)({initialValues:{username:"",email:"",password:""},validationSchema:B,onSubmit:async e=>{T(""),I("");try{const r=await fetch("http://172.16.14.174:8085/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(r.ok){const e=await r.json();T(e.message)}else{const e=await r.json();I(e.message)}}catch(W){I("An error occurred. Please try again later.")}}}),[W,I]=(0,n.useState)(""),[R,T]=(0,n.useState)("");return(0,q.jsx)(t.A,{theme:_,children:(0,q.jsxs)(o.Ay,{container:!0,component:"main",sx:{height:"100vh"},children:[(0,q.jsx)(l.Ay,{}),(0,q.jsx)(o.Ay,{item:!0,xs:!1,sm:!1,md:7,sx:{backgroundImage:`url(${v})`,backgroundRepeat:"no-repeat",backgroundColor:e=>"light"===e.palette.mode?e.palette.grey[50]:e.palette.grey[900],backgroundSize:"cover",backgroundPosition:"left"}}),(0,q.jsx)(o.Ay,{item:!0,xs:12,sm:12,md:5,component:i.A,elevation:0,square:!0,sx:{backgroundColor:"#0f2139 !important"},children:(0,q.jsxs)(d.A,{sx:{my:8,mx:4,display:"flex",flexDirection:"column",alignItems:"center"},children:[(0,q.jsx)(m.A,{sx:{m:1,bgcolor:"secondary.main"},children:(0,q.jsx)(A.A,{})}),(0,q.jsx)(c.A,{component:"h1",variant:"h5",children:"Sign Up"}),(0,q.jsx)(d.A,{sx:{mt:1,width:"80%"},children:(0,q.jsxs)("form",{onSubmit:E.handleSubmit,children:[(0,q.jsx)(u.A,{margin:"normal",required:!0,fullWidth:!0,id:"username",label:"Username",name:"username",autoComplete:"username",error:E.touched.username&&Boolean(E.errors.username),helperText:E.touched.username&&E.errors.username,value:E.values.username,onChange:E.handleChange,onBlur:E.handleBlur}),(0,q.jsx)(u.A,{margin:"normal",required:!0,fullWidth:!0,id:"email",label:"Email",name:"email",autoComplete:"email",error:E.touched.email&&Boolean(E.errors.email),helperText:E.touched.email&&E.errors.email,value:E.values.email,onChange:E.handleChange,onBlur:E.handleBlur}),(0,q.jsx)(u.A,{margin:"normal",required:!0,fullWidth:!0,name:"password",label:"Password",id:"password",autoComplete:"new-password",error:E.touched.password&&Boolean(E.errors.password),helperText:E.touched.password&&E.errors.password,value:E.values.password,onChange:e=>{E.handleChange(e),P(e.target.value)},onBlur:E.handleBlur,type:e?"text":"password",InputProps:{endAdornment:(0,q.jsx)(h.A,{position:"end",children:(0,q.jsx)(x.A,{"aria-label":"toggle password visibility",onClick:()=>{r(!e)},onMouseDown:e=>{e.preventDefault()},edge:"end",size:"large",children:e?(0,q.jsx)(w.A,{}):(0,q.jsx)(y.A,{})})})}}),(0,q.jsx)(p.A,{fullWidth:!0,sx:{mt:2},children:(0,q.jsxs)(o.Ay,{container:!0,spacing:2,alignItems:"center",children:[(0,q.jsx)(o.Ay,{item:!0,children:(0,q.jsx)(d.A,{sx:{bgcolor:null===a||void 0===a?void 0:a.color,width:85,height:8,borderRadius:"7px"}})}),(0,q.jsx)(o.Ay,{item:!0,children:(0,q.jsx)(c.A,{variant:"subtitle1",fontSize:"0.75rem",children:null===a||void 0===a?void 0:a.label})})]})}),(0,q.jsx)(g.A,{type:"submit",fullWidth:!0,variant:"contained",sx:{mt:3,mb:2},disabled:!E.isValid||E.isSubmitting,children:"Create Account"}),(0,q.jsxs)(o.Ay,{container:!0,children:[(0,q.jsx)(o.Ay,{item:!0,xs:!0}),(0,q.jsx)(o.Ay,{item:!0,children:(0,q.jsx)(k.N_,{to:"/",style:{textDecoration:"none",color:"inherit"},children:"Already have an account? Sign In"})})]})]})}),R&&(0,q.jsx)(j.A,{severity:"success",sx:{mt:2},children:R}),W&&(0,q.jsx)(j.A,{severity:"error",sx:{mt:2},children:W})]})})]})})}}}]);
import React, { forwardRef } from 'react';
import { But<PERSON>, List, ListItem, ListItemText, Typography, Paper, IconButton, Tooltip } from '@mui/material';
import { styled } from '@mui/system';
import DownloadIcon from '@mui/icons-material/Download';

const StyledPaper = styled(Paper)(({ theme }) => ({
    width: '400px',
    position: 'absolute',
    top: '20%',
    left: '40%',
    backgroundColor: theme.palette.background.paper,
    boxShadow: theme.shadows[5],
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    padding: theme.spacing(2)
}));

const ScrollableList = styled(List)(({ theme }) => ({
    width: '100%',
    maxHeight: '300px',
    overflowY: 'auto',
    marginTop: theme.spacing(2)
}));

const FileList = forwardRef(({ files, onDownload, fileListHeader }, ref) => {
    return (
        <StyledPaper ref={ref}>
            <Typography variant="h4" gutterBottom>
                {fileListHeader}
            </Typography>
            <ScrollableList>
                {files.map((file) => (
                    <ListItem key={file.timestamp} divider>
                        <ListItemText
                            primary={file.fileName}
                            secondary={
                                <>
                                    {new Date(file.timestamp).toLocaleString()}
                                    {file.miscData &&
                                        file.miscData.split(';').map((data, index) => (
                                            <Typography key={index} variant="body2" component="div">
                                                {data}
                                                {index < file.miscData.split(';').length - 1 ? '\n' : ''}
                                            </Typography>
                                        ))}
                                </>
                            }
                        />
                        <Tooltip title="Download" arrow>
                            <IconButton onClick={() => onDownload(file)}>
                                <DownloadIcon />
                            </IconButton>
                        </Tooltip>
                    </ListItem>
                ))}
            </ScrollableList>
        </StyledPaper>
    );
});

export default FileList;

.mapboxgl-popup-content {
    background-color: white !important; /* White background for the popup */
    color: #333333; /* Dark gray text */
    padding: 10px !important; /* Add some padding inside the popup */
    border-radius: 5px; /* Optional: rounded corners for the popup */
}

.marker {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

.signalMarker {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    border: 2px solid white;
    background-color: #c1853c;
}

.stopMarker {
    width: 10px;
    height: 10px;
    background-color: #00008b; /* Dark blue color */
    border-radius: 50%;
    border: 2px solid white;
}

/* MapBoxComponent.module.css */

.layerControl {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: rgba(0, 0, 0, 0.6);
    padding: 10px;
    border-radius: 4px;
    z-index: 1;
    color: white;
}

.popup {
    color: #333333; /* Dark gray text for better readability */
    padding: 10px;
    background-color: white;
}

.popup h2 {
    color: #000000; /* Black text for headings */
    margin-top: 0;
    margin-bottom: 8px;
}

.popup p {
    color: #333333; /* Dark gray text for paragraphs */
    margin: 4px 0;
}

.zonePopup {
    background-color: white;
    color: #333333;
    padding: 10px;
    border-radius: 5px;
    max-width: 250px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.zonePopup h3 {
    margin-top: 0;
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #0080ff;
}

.zonePopup p {
    margin: 4px 0;
    font-size: 14px;
}

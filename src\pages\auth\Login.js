import React, { useEffect, useState } from 'react';
import Avatar from '@mui/material/Avatar';
import Button from '@mui/material/Button';
import CssBaseline from '@mui/material/CssBaseline';
import TextField from '@mui/material/TextField';
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import Link from '@mui/material/Link';
import Paper from '@mui/material/Paper';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Alert from '@mui/material/Alert';
import LockOutlinedIcon from '@mui/icons-material/LockOutlined';
import Typography from '@mui/material/Typography';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import LoginBackground from 'assets/images/auth/LoginBackground.png';
import { setAuthToken } from 'utils/auth';
import { setSessionUser } from 'utils/auth';
import { useNavigate } from '../../../node_modules/react-router-dom/dist/index';
import { Link as RouterLink } from 'react-router-dom';

function Copyright(props) {
    return (
        <Typography variant="body2" color="text.secondary" align="center" {...props}>
            {'Copyright © '}
            <Link color="inherit" href="https://www.Parsons.com/">
                Parsons
            </Link>{' '}
            {new Date().getFullYear()}
            {'.'}
        </Typography>
    );
}
const theme = createTheme();

const darkTheme = createTheme({
    palette: {
        mode: 'dark' // Switches the theme to dark mode
    }
});

function SignIn() {
    const [tokenState, setTokenState] = React.useState(false);
    const [firstLoad, setFirstLoad] = React.useState(true);
    const [error, setError] = useState('');
    // const [submitClicked, setSubmitClicked] = React.useState(false);
    //console.log('Hello  ' + tokenState + ' - ' + firstLoad);
    async function handleSubmit(event) {
        setError('');
        if (!firstLoad) {
            event.preventDefault();
            const data = new FormData(event.currentTarget);
            const body = { username: data.get('username'), password: data.get('password') };
            const response = await fetch(`${process.env.REACT_APP_API_ENDPOINT}/api/auth/signin`, {
                method: 'POST',
                mode: 'cors',
                headers: {
                    Accept: 'application/json',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(body)
            });

            const resData = await response.json();
            if (response.ok) {
                const token = resData.accessToken;
                const sessionUser = { username: resData.username, roles: resData.roles };
                setAuthToken(token);
                setSessionUser(sessionUser);
                setTokenState(true);
            } else {
                setError(resData.message);
            }
        }
    }

    const navigate = useNavigate();
    React.useEffect(() => {
        if (!firstLoad) {
            navigate('main');
        }
        setFirstLoad(false);
    }, [tokenState]);

    return (
        <ThemeProvider theme={darkTheme}>
            <Grid container component="main" sx={{ height: '100vh' }}>
                <CssBaseline />
                <Grid
                    item
                    xs={false}
                    sm={false}
                    md={7}
                    sx={{
                        backgroundImage: `url(${LoginBackground})`,
                        backgroundRepeat: 'no-repeat',
                        backgroundColor: (t) => (t.palette.mode === 'left' ? t.palette.grey[50] : t.palette.grey[900]),
                        backgroundSize: 'cover',
                        backgroundPosition: 'left',
                        backgroundColor: '#0f2139' //'#2b7bba'
                    }}
                />
                <Grid item xs={12} sm={12} md={5} component={Paper} elevation={0} square sx={{ backgroundColor: '#0f2139 !important' }}>
                    <Box
                        sx={{
                            my: 8,
                            mx: 4,
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            backgroundColor: '#0f2139',
                            color: 'aliceblue',
                            backgroundColor: '#0f2139'
                        }}
                    >
                        <Avatar sx={{ m: 1, bgcolor: 'secondary.main' }}>
                            <LockOutlinedIcon />
                        </Avatar>
                        <Typography component="h1" variant="h5">
                            Sign in
                        </Typography>
                        <Box component="form" noValidate onSubmit={handleSubmit} sx={{ mt: 1, width: '80%', color: 'aliceblue' }}>
                            <TextField
                                sx={{ color: 'aliceblue' }}
                                margin="normal"
                                required
                                fullWidth
                                id="username"
                                label="Username"
                                name="username"
                                autoComplete="username"
                            />
                            <TextField
                                margin="normal"
                                required
                                fullWidth
                                name="password"
                                label="Password"
                                type="password"
                                id="password"
                                autoComplete="current-password"
                            />
                            <FormControlLabel control={<Checkbox value="remember" color="primary" />} label="Remember me" />
                            <Button type="submit" fullWidth variant="contained" sx={{ mt: 3, mb: 2 }}>
                                Sign In
                            </Button>
                            <Grid container>
                                <Grid item xs>
                                    <RouterLink to="/forgot-password" style={{ textDecoration: 'none', color: 'inherit' }}>
                                        Forgot password?
                                    </RouterLink>
                                </Grid>
                                <Grid item>
                                    <RouterLink to="/register" style={{ textDecoration: 'none', color: 'inherit' }}>
                                        {"Don't have an account? Sign Up"}
                                    </RouterLink>
                                </Grid>
                            </Grid>
                            {error && (
                                <Alert severity="error" sx={{ mt: 2 }}>
                                    {error}
                                </Alert>
                            )}
                            <Copyright sx={{ mt: 5 }} />
                        </Box>
                    </Box>
                </Grid>
            </Grid>
        </ThemeProvider>
    );
}

export default SignIn;

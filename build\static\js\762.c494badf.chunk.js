"use strict";(self.webpackChunkmantis_free_react_admin_template=self.webpackChunkmantis_free_react_admin_template||[]).push([[762],{78162:(e,t,s)=>{s.r(t),s.d(t,{default:()=>et});var a=s(9950),r=s(16491),o=s(4139),n=s(87233),i=s(57073),l=s(93230),c=s(72145),d=s(4904),u=s(72534),p=s(27987),h=s(52983),g=s(37598),m=s(46639),x=s(84142),y=s(99963),f=s(85849),v=s(68811),A=s(41225),S=(s(31219),s(55864)),b=s.n(S),j=s(72743),w=s(29246);let T=null;const I="http://*************:8085";const C={getRealTimeVehicleData:e=>{if(!T){const t=new(b())("http://*************:8085/websocket");T=j.q.over(t),T.debug=()=>{},T.connect({},(function(t){T.subscribe("/topic/vehicleUpdate",(function(t){var s=JSON.parse(t.body);e(s)}))}),(function(e){console.log("WebSocket connection error: "+e)}))}},closeRealTimeVehicleDataConnection:()=>{T&&(T.disconnect(),T=null)},getZones:async()=>{const e=(0,A.c4)();if(!e)return console.error("No access token available."),{success:!1,errorMessage:"Authentication required"};try{const t=await w.A.get("http://*************:8085/api/zones/getAll",{headers:{"Content-Type":"application/json;charset=UTF-8",Authorization:`Bearer ${e}`}});return 200===t.status?{success:!0,zones:t.data}:{success:!1,errorMessage:"Failed to fetch zones"}}catch(t){return console.error("Error fetching the zones data:",t),{success:!1,errorMessage:t.message||"Error fetching data"}}},saveZone:async e=>{const t=`${I}/api/zones/save`,s=(0,A.c4)();if(!s)return console.error("No access token available."),{success:!1,errorMessage:"Authentication required"};try{const a=await w.A.post(t,e,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`}});return 200===a.status?{success:!0,message:a.data}:{success:!1,errorMessage:"Failed to save or update zone"}}catch(a){return console.error("Error saving or updating zone:",a),{success:!1,errorMessage:a.message||"Error occurred while saving zone"}}},getAllZoneSignalsForZone:async e=>{const t=`${I}/api/zones/${e}/zone-signals`,s=(0,A.c4)();if(!s)return console.error("No access token available."),{success:!1,errorMessage:"Authentication required"};try{const e=await w.A.get(t,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`}});return 200===e.status?{success:!0,zoneSignals:e.data}:{success:!1,errorMessage:"Failed to fetch zone signals"}}catch(a){return console.error("Error fetching zone signals:",a),{success:!1,errorMessage:a.message||"Error occurred while fetching zone signals"}}},saveZoneSignal:async e=>{const t=`${I}/api/zones/zone-signal/save`,s=(0,A.c4)();if(!s)return console.error("No access token available."),{success:!1,errorMessage:"Authentication required"};try{const a=await w.A.post(t,e,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`}});return 200===a.status?{success:!0,message:a.data}:{success:!1,errorMessage:"Failed to save or update zone signal"}}catch(a){return console.error("Error saving or updating zone signal:",a),{success:!1,errorMessage:a.message||"Error occurred while saving zone signal"}}},deleteZone:async e=>{const t=`${I}/api/zones/delete/${e}`,s=(0,A.c4)();if(!s)return console.error("No access token available."),{success:!1,errorMessage:"Authentication required"};try{const e=await w.A.delete(t,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`}});return 200===e.status?{success:!0,message:e.data}:{success:!1,errorMessage:"Failed to delete zone"}}catch(a){return console.error("Error deleting zone:",a),{success:!1,errorMessage:a.message||"Error occurred while deleting zone"}}},getZonesWithTripHeadsigns:async()=>{const e=(0,A.c4)();if(!e)return console.error("No access token available."),{success:!1,errorMessage:"Authentication required"};try{const t=await w.A.get("http://*************:8085/api/zones/getAllZoneSignals",{headers:{"Content-Type":"application/json;charset=UTF-8",Authorization:`Bearer ${e}`}});return 200===t.status?{success:!0,zones:t.data}:{success:!1,errorMessage:"Failed to fetch zones with trip headsigns"}}catch(t){return console.error("Error fetching the zones data:",t),{success:!1,errorMessage:t.message||"Error fetching data"}}},getTrafficSignals:async()=>{const e=(0,A.c4)();if(!e)return console.error("No access token available."),{success:!1,errorMessage:"Authentication required"};try{const t=await w.A.get("http://*************:8085/signals",{headers:{"Content-Type":"application/json;charset=UTF-8",Authorization:`Bearer ${e}`}});return 200===t.status?{success:!0,devices:t.data.devices}:{success:!1,errorMessage:"Failed to fetch data"}}catch(t){return console.error("Error fetching the traffic signals data:",t),{success:!1,errorMessage:t.message||"Error fetching data"}}},getGtfsRoutes:async()=>{const e=(0,A.c4)();if(!e)return console.error("No access token available."),{success:!1,errorMessage:"Authentication required"};try{const t=await w.A.get("http://*************:8085/api/gtfs/routes/getall",{headers:{"Content-Type":"application/json;charset=UTF-8",Authorization:`Bearer ${e}`}});return 200===t.status?{success:!0,routes:t.data}:{success:!1,errorMessage:"Failed to fetch GTFS routes"}}catch(t){return console.error("Error fetching the GTFS routes data:",t),{success:!1,errorMessage:t.message||"Error fetching data"}}},getGtfsTrips:async()=>{const e=(0,A.c4)();if(!e)return console.error("No access token available."),{success:!1,errorMessage:"Authentication required"};try{const t=await w.A.get("http://*************:8085/api/gtfs/trips/getall",{headers:{"Content-Type":"application/json;charset=UTF-8",Authorization:`Bearer ${e}`}});return 200===t.status?{success:!0,trips:t.data}:{success:!1,errorMessage:"Failed to fetch GTFS trips"}}catch(t){return console.error("Error fetching the GTFS trips data:",t),{success:!1,errorMessage:t.message||"Error fetching data"}}},getStopsByTripId:async e=>{const t=`http://*************:8085/api/gtfs/stops/getByTripId/${e}`,s=(0,A.c4)();if(!s)return console.error("No access token available."),{success:!1,errorMessage:"Authentication required"};try{const e=await w.A.get(t,{headers:{"Content-Type":"application/json;charset=UTF-8",Authorization:`Bearer ${s}`}});return 200===e.status?{success:!0,stops:e.data}:{success:!1,errorMessage:"Failed to fetch stops"}}catch(a){return console.error("Error fetching the stops data:",a),{success:!1,errorMessage:a.message||"Error fetching data"}}},getStopTimesByTripId:async e=>{const t=`http://*************:8085/api/gtfs/stoptimes/getByTripId/${e}`,s=(0,A.c4)();if(!s)return console.error("No access token available."),{success:!1,errorMessage:"Authentication required"};try{const e=await w.A.get(t,{headers:{"Content-Type":"application/json;charset=UTF-8",Authorization:`Bearer ${s}`}});return 200===e.status?{success:!0,stopTimes:e.data}:{success:!1,errorMessage:"Failed to fetch stop times"}}catch(a){return console.error("Error fetching the stop times data:",a),{success:!1,errorMessage:a.message||"Error fetching data"}}},getShapesByTripId:async e=>{const t=`http://*************:8085/api/gtfs/shapes/getByTripId/${e}`,s=(0,A.c4)();if(!s)return console.error("No access token available."),{success:!1,errorMessage:"Authentication required"};try{const e=await w.A.get(t,{headers:{"Content-Type":"application/json;charset=UTF-8",Authorization:`Bearer ${s}`}});return 200===e.status?{success:!0,shapes:e.data}:{success:!1,errorMessage:"Failed to fetch shapes"}}catch(a){return console.error("Error fetching the shapes data:",a),{success:!1,errorMessage:a.message||"Error fetching data"}}},getAvgSpeedAndTravelTimeForRouteBetweenTwoDates:async(e,t,s)=>{const a=`http://*************:8085/api/analysis/getAvgSpeedAndTravelTimeForRouteBetweenTwoDates?routeId=${e}&startTime=${encodeURIComponent(t)}&endTime=${encodeURIComponent(s)}`,r=(0,A.c4)();if(!r)return console.error("No access token available."),{success:!1,errorMessage:"Authentication required"};try{const e=await w.A.get(a,{headers:{"Content-Type":"application/json;charset=UTF-8",Authorization:`Bearer ${r}`}});return 200===e.status?{success:!0,data:e.data}:{success:!1,errorMessage:"Failed to fetch average speed and travel time"}}catch(o){return console.error("Error fetching the average speed and travel time data:",o),{success:!1,errorMessage:o.message||"Error fetching data"}}},getTravelTimesByHeadsign:async(e,t,s,a)=>{const r=s.endsWith("Z")?s:`${s}Z`,o=a.endsWith("Z")?a:`${a}Z`,n=`http://*************:8085/api/analysis/getTravelTimesByRouteIdAndHeadsign?routeId=${e}&headsign=${encodeURIComponent(t)}&startTime=${encodeURIComponent(r)}&endTime=${encodeURIComponent(o)}`,i=(0,A.c4)();if(!i)return console.error("No access token available."),{success:!1,errorMessage:"Authentication required"};try{const e=await w.A.get(n,{headers:{"Content-Type":"application/json;charset=UTF-8",Authorization:`Bearer ${i}`}});return 200===e.status?{success:!0,data:e.data}:{success:!1,errorMessage:"Failed to fetch travel times by headsign"}}catch(l){return console.error("Error fetching the travel times by headsign:",l),{success:!1,errorMessage:l.message||"Error fetching data"}}},getTransitRoutesWithHeadsigns:async()=>{const e=(0,A.c4)();if(!e)return console.error("No access token available."),{success:!1,errorMessage:"Authentication required"};try{const t=await w.A.get("http://*************:8085/api/routes/getalltransitRouteWithHeadsigns",{headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`}});return 200===t.status?{success:!0,routes:t.data}:{success:!1,errorMessage:"Failed to fetch transit routes with headsigns"}}catch(t){return console.error("Error fetching transit routes with headsigns:",t),{success:!1,errorMessage:t.message||"Error fetching data"}}},getTripDetailsByRouteIdAndHeadsign:async(e,t,s,a)=>{const r=s.endsWith("Z")?s:`${s}Z`,o=a.endsWith("Z")?a:`${a}Z`,n=`http://*************:8085/api/analysis/getTripDetailsByRouteIdAndHeadsign?routeId=${e}&headsign=${encodeURIComponent(t)}&startTime=${encodeURIComponent(r)}&endTime=${encodeURIComponent(o)}`,i=(0,A.c4)();if(!i)return console.error("No access token available."),{success:!1,errorMessage:"Authentication required"};try{const e=await w.A.get(n,{headers:{"Content-Type":"application/json;charset=UTF-8",Authorization:`Bearer ${i}`}});return 200===e.status?{success:!0,data:e.data}:{success:!1,errorMessage:"Failed to fetch trip details by headsign"}}catch(l){return console.error("Error fetching trip details by headsign:",l),{success:!1,errorMessage:l.message||"Error fetching data"}}},getAvgLatenessByRouteIdAndHeadsign:async(e,t,s,a)=>{const r=`http://*************:8085/api/analysis/getAvgLatenessByRouteIdAndHeadsign?routeId=${e}&headsign=${encodeURIComponent(t)}&startTime=${encodeURIComponent(s)}&endTime=${encodeURIComponent(a)}`,o=(0,A.c4)();if(!o)return console.error("No access token available."),{success:!1,errorMessage:"Authentication required"};try{const e=await fetch(r,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o}`}});if(!e.ok){const t=await e.text();return console.error("API error:",t),{success:!1,errorMessage:t||"Failed to fetch data"}}return{success:!0,data:await e.json()}}catch(n){return console.error("Error in API call:",n),{success:!1,errorMessage:n.message}}},getSignalTspEvents:async(e,t,s)=>{const a=t.endsWith("Z")?t:`${t}Z`,r=s.endsWith("Z")?s:`${s}Z`,o=`http://*************:8085/signals/device/${e}/events?startTime=${encodeURIComponent(a)}&endTime=${encodeURIComponent(r)}`,n=(0,A.c4)();if(!n)return console.error("No access token available."),{success:!1,errorMessage:"Authentication required"};try{const e=await w.A.get(o,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${n}`}});return 200===e.status?{success:!0,events:e.data}:{success:!1,errorMessage:"Failed to fetch TSP events"}}catch(i){return console.error("Error fetching TSP events:",i),{success:!1,errorMessage:i.message||"Error occurred while fetching TSP events"}}}};var $=s(6827),k=s(86710),z=s(28448),E=s(88895),D=s(96895),M=s(42617),R=s(81831),L=s(98650),P=s(95722),F=s(22580),N=s(80678),H=s(39452),B=s(44414);const Z=e=>{let{routeId:t,headsign:s,startDate:o,endDate:i,routeName:l,onLoadComplete:c}=e;const[d,u]=(0,a.useState)([]),[p,h]=(0,a.useState)({chart:{height:350,type:"area",zoom:{enabled:!0,type:"xy",autoScaleYaxis:!0},toolbar:{show:!0,tools:{download:!0,selection:!0,zoom:!0,zoomin:!0,zoomout:!0,pan:!0,reset:!0},autoSelected:"zoom",style:{backgroundColor:"transparent",color:"#a9a9a9",borderRadius:"6px"}},background:"transparent",foreColor:"#a9a9a9"},theme:{mode:"dark",palette:"palette1"},dataLabels:{enabled:!1},markers:{size:0},stroke:{curve:"smooth",width:3,lineCap:"round"},fill:{type:"gradient",gradient:{shadeIntensity:1,inverseColors:!1,opacityFrom:.5,opacityTo:.1,stops:[0,90,100]}},grid:{xaxis:{lines:{show:!0,color:"#555555"}},yaxis:{lines:{show:!0,color:"#555555"}},borderColor:"#555555",strokeDashArray:0},xaxis:{type:"datetime",labels:{style:{colors:"#a9a9a9"},datetimeUTC:!1,format:"HH:mm"}},yaxis:{title:{text:"Travel Time (seconds)",style:{color:"#a9a9a9",fontSize:"14px",fontWeight:600,fontFamily:"'Roboto', sans-serif"}},labels:{style:{colors:"#a9a9a9",fontSize:"12px",fontFamily:"'Roboto', sans-serif"},formatter:function(e){return e.toFixed(0)}},min:0,max:void 0,tickAmount:6,forceNiceScale:!0},tooltip:{theme:"dark",shared:!1,x:{format:"MMM dd, yyyy HH:mm:ss"},custom:function(e){let{series:t,seriesIndex:s,dataPointIndex:a,w:r}=e;const o=r.config.series[s].data[a],n=new Date(o.x).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),i=o.y;return`\n                    <div class="apexcharts-tooltip-box" style="padding: 8px; background: #222; color: #fff; border: 1px solid #444;">\n                        <div style="font-weight: bold; margin-bottom: 5px;">${n}</div>\n                        <div style="margin-bottom: 5px;">Trip: ${o.tripId||"Unknown"}</div>\n                        <div style="margin-top: 5px;">\n                            <strong>Travel Time: ${i.toFixed(0)} seconds</strong>\n                        </div>\n                    </div>\n                `},y:{formatter:function(e){return e.toFixed(0)+" seconds"}}},legend:{position:"top",labels:{colors:"#a9a9a9"}}}),[g,x]=(0,a.useState)(!0),[y,f]=(0,a.useState)(null);(0,a.useEffect)((()=>{t&&s&&v()}),[t,s,o,i]);const v=async()=>{x(!0),f(null);try{const e=A(o),a=A(i),r=await C.getTravelTimesByHeadsign(t,s,e,a);if(r.success){if(!r.data||0===r.data.length)return f("No data available for the selected time range"),void x(!1);r.data;const e=[{name:`${t} - ${s}`,data:r.data.map(((e,t)=>({x:new Date(e.startTime).getTime(),y:e.travelTime,tripId:e.tripId||"Unknown",tripIndex:t})))}];u(e),h((e=>({...e,tooltip:{...e.tooltip,custom:function(e){let{series:t,seriesIndex:s,dataPointIndex:a,w:r}=e;const o=r.config.series[s].data[a],n=new Date(o.x).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),i=o.y;return`\n                                <div class="apexcharts-tooltip-box" style="padding: 8px; background: #222; color: #fff; border: 1px solid #444;">\n                                    <div style="font-weight: bold; margin-bottom: 5px;">${n}</div>\n                                    <div style="margin-bottom: 5px;">Trip: ${o.tripId||"Unknown"}</div>\n                                    <div style="margin-top: 5px;">\n                                        <strong>Travel Time: ${i.toFixed(0)} seconds</strong>\n                                    </div>\n                                </div>\n                            `}}}))),c&&c()}else f(r.errorMessage||"Failed to fetch data")}catch(e){console.error("Error fetching travel time data:",e),f("An error occurred while fetching data")}finally{x(!1)}},A=e=>e.toISOString(),S=e=>e.toLocaleString("en-US",{month:"short",day:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"});return(0,B.jsxs)(r.A,{sx:{width:"100%",height:"100%",backgroundColor:"#424242",borderRadius:1},children:[(0,B.jsx)(n.A,{variant:"h6",sx:{textAlign:"center",mb:.5},children:"Trip Travel Time"}),(0,B.jsx)(n.A,{variant:"subtitle2",sx:{textAlign:"center",mb:.5},children:l||`${t} - ${s}`}),(0,B.jsxs)(n.A,{variant:"caption",sx:{display:"block",color:"#aaa",textAlign:"center",mb:1},children:[S(o)," - ",S(i)]}),(0,B.jsx)(r.A,{sx:{width:"100%",height:"410px"},children:g?(0,B.jsx)(r.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,B.jsx)(m.A,{})}):y?(0,B.jsx)(r.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,B.jsx)(n.A,{color:"error",children:y})}):d.length>0?(0,B.jsx)(H.A,{options:p,series:d,type:"area",height:"100%",width:"100%"}):(0,B.jsx)(r.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,B.jsx)(n.A,{children:"No data available for the selected time range"})})})]})},W=e=>{let{routeId:t,headsign:s,startDate:o,endDate:i,routeName:l,onLoadComplete:c}=e;const[d,u]=(0,a.useState)(null),[p,h]=(0,a.useState)(!0),[g,x]=(0,a.useState)(null),[y,f]=(0,a.useState)({}),[v,A]=(0,a.useState)([]),[S,b]=(0,a.useState)(500);(0,a.useEffect)((()=>{t&&s&&j()}),[t,s,o,i]);const j=async()=>{h(!0),x(null);try{const e=o.toISOString(),a=i.toISOString(),r=await C.getAvgLatenessByRouteIdAndHeadsign(t,s,e,a);if(r.success){if(!r.data||!r.data.tripLatenessResults||0===r.data.tripLatenessResults.length)return x("No data available for the selected time range"),void h(!1);u(r.data),w(r.data),c&&c()}else x(r.errorMessage||"Failed to fetch data")}catch(e){console.error("Error fetching trip latency data:",e),x("An error occurred while fetching data")}finally{h(!1)}},w=e=>{if(!e||!e.stopTimes||!e.tripLatenessResults)return;const t=[...e.stopTimes].sort(((e,t)=>e.stopSequence-t.stopSequence)),s=[];for(let l=0;l<t.length-1;l++){const e=t[l],a=t[l+1];s.push(`${e.stopName} \u2192 ${a.stopName}`)}const a=[...e.tripLatenessResults].sort(((e,t)=>new Date(e.tripStartTimestamp)-new Date(t.tripStartTimestamp))),r=200+Math.max(20*a.length),o=Math.max(400,Math.min(r,1200));b(o);const n=[];a.forEach((e=>{if(!e.latenessBetweenStops||0===e.latenessBetweenStops.length)return;const s=(a=e.tripStartTimestamp)?new Date(a).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):"";var a;const r=[];for(let o=0;o<t.length-1;o++){const s=t[o],a=t[o+1],n=e.latenessBetweenStops.find((e=>e.fromStopId===s.stopId&&e.toStopId===a.stopId));n?r.push({x:`${s.stopName} \u2192 ${a.stopName}`,y:Math.round(n.averageLateness)}):r.push({x:`${s.stopName} \u2192 ${a.stopName}`,y:null})}n.push({name:s,data:r})}));const i={chart:{type:"heatmap",height:800,toolbar:{show:!0,tools:{download:!0,selection:!0,zoom:!0,zoomin:!0,zoomout:!0,pan:!0,reset:!0},autoSelected:"zoom"},background:"transparent",foreColor:"#a9a9a9"},theme:{mode:"dark",palette:"palette1"},dataLabels:{enabled:!1},xaxis:{type:"category",labels:{rotate:-45,style:{colors:"#a9a9a9",fontSize:"10px"}}},yaxis:{labels:{style:{colors:"#a9a9a9"}}},plotOptions:{heatmap:{enableShades:!1,shadeIntensity:.7,radius:0,useFillColorAsStroke:!1,colorScale:{ranges:[{from:-999,to:-60,color:"#b2dfb0",name:"Early"},{from:-60,to:120,color:"#8bc34a",name:"On Time"},{from:120,to:180,color:"#ffecb3",name:"Slight Delay"},{from:180,to:300,color:"#ffccbc",name:"Moderate Delay"},{from:300,to:9999,color:"#ef5350",name:"Severe Delay"}]}}},stroke:{width:.25,colors:["#333"]},grid:{borderColor:"#555555",xaxis:{lines:{show:!0,color:"#555555"}},yaxis:{lines:{show:!0,color:"#555555"}},row:{colors:["transparent"],opacity:.5}},tooltip:{enabled:!0,enabledOnSeries:void 0,shared:!0,followCursor:!1,intersect:!1,inverseOrder:!1,custom:function(e){let{series:t,seriesIndex:s,dataPointIndex:r,w:o}=e;const n=o.config.series[s].data[r],i=null!==n.y?`${n.y}s`:"No data",l=a[s],c=l?l.tripId:"Unknown";return`\n                        <div class="apexcharts-tooltip-box" style="padding: 8px; background: #222; color: #fff; border: 1px solid #444;">\n                            <div style="font-weight: bold; margin-bottom: 5px;">${o.config.series[s].name}</div>\n                            <div style="margin-bottom: 5px;">Trip: ${c}</div>\n                            <div>Segment: ${n.x}</div>\n                            <div style="margin-top: 5px; color: ${null!==n.y?n.y>180?"#ef5350":n.y>120?"#ffecb3":"#8bc34a":"#aaa"}">\n                                <strong>Lateness: ${i}</strong>\n                            </div>\n                        </div>\n                    `}}};f(i),A(n)},T=e=>e.toLocaleString("en-US",{month:"short",day:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"});return(0,B.jsxs)(r.A,{sx:{width:"100%",height:"100%",backgroundColor:"#424242",borderRadius:1},children:[(0,B.jsxs)(r.A,{sx:{padding:"10px",textAlign:"center",mb:2},children:[(0,B.jsx)(n.A,{variant:"h6",sx:{color:"#fff",mb:.5},children:"Schedule Adherence Heatmap"}),(0,B.jsx)(n.A,{variant:"subtitle2",sx:{textAlign:"center",mb:.5},children:l||s}),(0,B.jsxs)(n.A,{variant:"caption",sx:{display:"block",color:"#aaa",textAlign:"center",mb:1},children:[T(o)," - ",T(i)]})]}),(0,B.jsx)(r.A,{sx:{width:"100%",height:`${S}px`},children:p?(0,B.jsx)(r.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,B.jsx)(m.A,{})}):g?(0,B.jsx)(r.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,B.jsx)(n.A,{color:"error",children:g})}):v.length>0?(0,B.jsx)(H.A,{options:y,series:v,type:"heatmap",height:"100%"}):(0,B.jsx)(r.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,B.jsx)(n.A,{children:"No data available for the selected time range"})})})]})},U=e=>{let{signalId:t,signalName:s,startDate:o,endDate:i,primaryRoad:l,crossRoad:c,onLoadComplete:d}=e;const[u,p]=(0,a.useState)([]),[h,g]=(0,a.useState)([]),[x,y]=(0,a.useState)(550),[f,v]=(0,a.useState)({chart:{height:550,type:"scatter",zoom:{enabled:!0,type:"x",autoScaleYaxis:!0},toolbar:{show:!0,tools:{download:!0,selection:!0,zoom:!0,zoomin:!0,zoomout:!0,pan:!0,reset:!0},export:{csv:{filename:void 0,headerCategory:"category",headerValue:"value"},svg:{filename:`tsp-events-${t}`},png:{filename:`tsp-events-${t}`}},autoSelected:"zoom",style:{backgroundColor:"transparent",color:"#a9a9a9",borderRadius:"6px"}},background:"transparent",foreColor:"#a9a9a9"},theme:{mode:"dark",palette:"palette1"},dataLabels:{enabled:!1},grid:{xaxis:{lines:{show:!0,color:"#555555"}},yaxis:{lines:{show:!0,color:"#555555"}},borderColor:"#555555",strokeDashArray:0},xaxis:{type:"datetime",labels:{datetimeUTC:!1,format:"HH:mm:ss"}},yaxis:{min:0,max:1,tickAmount:1,labels:{formatter:function(e){return""},style:{fontSize:"10px"}}},tooltip:{theme:"dark",shared:!1,x:{format:"MMM dd, yyyy HH:mm:ss"},custom:function(e){let{series:t,seriesIndex:s,dataPointIndex:a,w:r}=e;const o=r.config.series[s].data[a],n=new Date(o.x).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit",second:"2-digit"}),i=o.eventName,l=o.description||"";o.parameter;return`\n                    <div class="apexcharts-tooltip-box" style="padding: 8px; background: #222; color: #fff; border: 1px solid #444;">\n                        <div style="font-weight: bold; margin-bottom: 5px;">${n}</div>\n                        <div style="margin-bottom: 5px;">${i} idx:${o.indexFormatted}</div>\n                        <div style="margin-top: 5px;">\n                            <strong>${l}</strong>\n                        </div>\n                    </div>\n                `}},legend:{show:!1}}),[A,S]=(0,a.useState)(!0),[b,j]=(0,a.useState)(null);(0,a.useEffect)((()=>{t&&w()}),[t,o,i]);const w=async()=>{S(!0),j(null);try{const e=T(o),s=T(i),a=await C.getSignalTspEvents(t,e,s);if(a.success){if(!a.events||0===a.events.length)return j("No data available for the selected time range"),void S(!1);I(a.events),d&&d()}else j(a.errorMessage||"Failed to fetch data")}catch(e){console.error("Error fetching TSP events data:",e),j("An error occurred while fetching data")}finally{S(!1)}},T=e=>e.toISOString(),I=e=>{const t={},s=[];e.forEach((e=>{const a=`${e.eventName} idx:${e.indexFormatted}`;t[a]||(t[a]=[],s.push(a)),t[a].push({x:new Date(e.timestamp).getTime(),y:s.indexOf(a)+1,eventName:e.eventName,indexFormatted:e.indexFormatted,description:e.description,parameter:e.parameter})})),g(s);const a=new Set(e.map((e=>e.eventType))).size,r=(e.length,350+10*a),o=Math.max(550,Math.min(r,1e3));y(o);const n=s.map((e=>({name:e,data:t[e]})));p(n),v({chart:{height:x,type:"scatter",zoom:{enabled:!0,type:"x",autoScaleYaxis:!0},toolbar:{show:!0,tools:{download:!0,selection:!0,zoom:!0,zoomin:!0,zoomout:!0,pan:!0,reset:!0},autoSelected:"zoom",style:{borderRadius:"6px"}},background:"transparent"},theme:{mode:"dark",palette:"palette1"},dataLabels:{enabled:!1},grid:{xaxis:{lines:{show:!0,color:"#555555"}},yaxis:{lines:{show:!0,color:"#555555"}},borderColor:"#555555",strokeDashArray:0},xaxis:{type:"datetime",labels:{datetimeUTC:!1,format:"HH:mm:ss"}},yaxis:{min:0,max:s.length,tickAmount:s.length,labels:{formatter:function(e){const t=Math.round(e)-1;return t>=0&&t<s.length?s[t]:""},style:{fontSize:"10px",colors:"#a9a9a9"}}},tooltip:{theme:"dark",shared:!1,x:{format:"MMM dd, yyyy HH:mm:ss"},custom:function(e){let{series:t,seriesIndex:s,dataPointIndex:a,w:r}=e;const o=r.config.series[s].data[a],n=new Date(o.x).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit",second:"2-digit"}),i=o.eventName,l=o.description||"";o.parameter;return`\n                        <div class="apexcharts-tooltip-box" style="padding: 8px; background: #222; color: #fff; border: 1px solid #444;">\n                            <div style="font-weight: bold; margin-bottom: 5px;">${n}</div>\n                            <div style="margin-bottom: 5px;">${i} idx: ${o.indexFormatted}</div>\n                            <div style="margin-top: 5px;">\n                                <strong>${l}</strong>\n                            </div>\n                        </div>\n                    `}},markers:{size:4,strokeWidth:0,hover:{size:6}},legend:{show:!0}})},$=e=>e.toLocaleString("en-US",{month:"short",day:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"});return(0,B.jsxs)(r.A,{sx:{height:"100%",width:"100%",p:2,backgroundColor:"#424242",borderRadius:1},children:[(0,B.jsx)(n.A,{variant:"h6",sx:{textAlign:"center",mb:.5},children:"Signal Controller TSP Events"}),(0,B.jsx)(n.A,{variant:"subtitle2",sx:{textAlign:"center",mb:.5},children:l&&c?`${l} & ${c}`:s||`Signal ID: ${t}`}),(0,B.jsxs)(n.A,{variant:"caption",sx:{display:"block",color:"#aaa",textAlign:"center",mb:1},children:[$(o)," - ",$(i)]}),(0,B.jsx)(r.A,{sx:{height:`${x}px`,width:"100%"},children:A?(0,B.jsx)(r.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,B.jsx)(m.A,{})}):b?(0,B.jsx)(r.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,B.jsx)(n.A,{color:"error",children:b})}):u.length>0?(0,B.jsx)(H.A,{options:f,series:u,type:"scatter",height:"100%",width:"100%"}):(0,B.jsx)(r.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,B.jsx)(n.A,{children:"No data available for the selected time range"})})})]})};var O=s(35661),G=s(1320),Y=s(397),q=s(24965),V=s(10371),J=s(68605),K=s(66065),Q=s(22057),X=s(86547);const ee=e=>{let{signalId:t,signalName:s,startDate:o,endDate:i,primaryRoad:l,crossRoad:c,onLoadComplete:d}=e;const[u,p]=(0,a.useState)([]),[h,g]=(0,a.useState)(!0),[x,y]=(0,a.useState)(null),[f,v]=(0,a.useState)(0),[A,S]=(0,a.useState)(10),[b,j]=(0,a.useState)("timestamp"),[w,T]=(0,a.useState)("desc");(0,a.useEffect)((()=>{t&&I()}),[t,o,i]);const I=async()=>{g(!0),y(null);try{const e=$(o),s=$(i),a=await C.getSignalTspEvents(t,e,s);if(a.success){if(!a.events||0===a.events.length)return y("No data available for the selected time range"),void g(!1);const e=a.events.sort(((e,t)=>new Date(t.timestamp)-new Date(e.timestamp)));p(e),d&&d()}else y(a.errorMessage||"Failed to fetch data")}catch(e){console.error("Error fetching TSP events data:",e),y("An error occurred while fetching data")}finally{g(!1)}},$=e=>e.toISOString(),k=e=>e.toLocaleString("en-US",{month:"short",day:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}),z=e=>{T(b===e&&"asc"===w?"desc":"asc"),j(e)},E=a.useMemo((()=>u.length?[...u].sort(((e,t)=>{let s=0;return"timestamp"===b?s=new Date(e.timestamp)-new Date(t.timestamp):"eventName"===b?s=e.eventName.localeCompare(t.eventName):"indexFormatted"===b?s=e.indexFormatted-t.indexFormatted:"description"===b&&(s=(e.description||"").localeCompare(t.description||"")),"desc"===w?-s:s})):[]),[u,w,b]).slice(f*A,f*A+A);return(0,B.jsxs)(r.A,{sx:{height:"100%",width:"100%",p:2,backgroundColor:"#424242",borderRadius:1},children:[(0,B.jsx)(n.A,{variant:"h6",sx:{textAlign:"center",mb:.5},children:"Signal Controller TSP Raw Events"}),(0,B.jsx)(n.A,{variant:"subtitle2",sx:{textAlign:"center",mb:.5},children:l&&c?`${l} & ${c}`:s||`Signal ID: ${t}`}),(0,B.jsxs)(n.A,{variant:"caption",sx:{display:"block",color:"#aaa",textAlign:"center",mb:1},children:[k(o)," - ",k(i)]}),(0,B.jsx)(r.A,{sx:{height:"calc(100% - 80px)",width:"100%"},children:h?(0,B.jsx)(r.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,B.jsx)(m.A,{})}):x?(0,B.jsx)(r.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,B.jsx)(n.A,{color:"error",children:x})}):u.length>0?(0,B.jsxs)(O.A,{sx:{width:"100%",height:"100%",overflow:"hidden",backgroundColor:"#333"},children:[(0,B.jsx)(G.A,{sx:{maxHeight:"calc(100% - 52px)"},children:(0,B.jsxs)(Y.A,{stickyHeader:!0,"aria-label":"TSP events table",size:"small",children:[(0,B.jsx)(q.A,{children:(0,B.jsxs)(V.A,{children:[(0,B.jsx)(J.A,{sx:{backgroundColor:"#222"},sortDirection:"timestamp"===b&&w,children:(0,B.jsx)(K.A,{active:"timestamp"===b,direction:"timestamp"===b?w:"asc",onClick:()=>z("timestamp"),children:"Timestamp"})}),(0,B.jsx)(J.A,{sx:{backgroundColor:"#222"},sortDirection:"eventName"===b&&w,children:(0,B.jsx)(K.A,{active:"eventName"===b,direction:"eventName"===b?w:"asc",onClick:()=>z("eventName"),children:"Event"})}),(0,B.jsx)(J.A,{sx:{backgroundColor:"#222"},sortDirection:"indexFormatted"===b&&w,children:(0,B.jsx)(K.A,{active:"indexFormatted"===b,direction:"indexFormatted"===b?w:"asc",onClick:()=>z("indexFormatted"),children:"Index"})}),(0,B.jsx)(J.A,{sx:{backgroundColor:"#222"},sortDirection:"description"===b&&w,children:(0,B.jsx)(K.A,{active:"description"===b,direction:"description"===b?w:"asc",onClick:()=>z("description"),children:"Description"})})]})}),(0,B.jsx)(Q.A,{children:E.map(((e,t)=>{return(0,B.jsxs)(V.A,{sx:{"&:last-child td, &:last-child th":{border:0}},children:[(0,B.jsx)(J.A,{component:"th",scope:"row",children:(s=e.timestamp,new Date(s).toLocaleString("en-US",{month:"short",day:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit",second:"2-digit"}))}),(0,B.jsx)(J.A,{children:e.eventName}),(0,B.jsx)(J.A,{children:e.indexFormatted}),(0,B.jsx)(J.A,{children:e.description||"-"})]},`${e.timestamp}-${t}`);var s}))})]})}),(0,B.jsx)(X.A,{rowsPerPageOptions:[10,25,50,100],component:"div",count:u.length,rowsPerPage:A,page:f,onPageChange:(e,t)=>{v(t)},onRowsPerPageChange:e=>{S(parseInt(e.target.value,10)),v(0)},sx:{backgroundColor:"#222",color:"white",".MuiTablePagination-selectIcon":{color:"white"}}})]}):(0,B.jsx)(r.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,B.jsx)(n.A,{children:"No data available for the selected time range"})})})]})};var te=s(96319);const se=e=>{let{currentTab:t,selectedRoutes:s,selectedSignals:o}=e;const[l,c]=(0,a.useState)(""),[d,h]=(0,a.useState)((()=>{const e=new Date;return e.setHours(e.getHours()-1),e})),[x,y]=(0,a.useState)(new Date),[f,v]=(0,a.useState)([]),[A,S]=(0,a.useState)(!1),[b,j]=(0,a.useState)(!1),w=e=>{console.log(`Report ${e} loaded successfully`)},T={1:["Signal Controller TSP Events","Signal Controller TSP Raw Events"],2:["Trip Travel Time","Schedule Adherence Heatmap"],3:["Report 1","Report 2"],4:["Report 1","Report 2"]};(0,a.useEffect)((()=>{T[t]&&T[t].length>0?c(T[t][0]):c("")}),[t]),(0,a.useEffect)((()=>{"1"===t&&o&&o.length>10||"2"===t&&s&&s.length>10?j(!0):j(!1)}),[t,o,s]);return(0,B.jsxs)(r.A,{sx:{height:"100%",display:"flex",flexDirection:"column"},children:[(0,B.jsxs)(r.A,{sx:{p:2,zIndex:1,position:"sticky",top:0,backgroundColor:"#333"},children:[(0,B.jsxs)(r.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2,flexWrap:"wrap",gap:2},children:[(0,B.jsxs)(u.A,{value:l,onChange:e=>{c(e.target.value)},displayEmpty:!0,sx:{minWidth:150},children:[(0,B.jsx)(p.A,{value:"",children:(0,B.jsx)("em",{children:"Select Report"})}),T[t]&&T[t].map((e=>(0,B.jsx)(p.A,{value:e,children:e},e)))]}),(0,B.jsx)(R.$,{dateAdapter:L.h,children:(0,B.jsxs)(r.A,{sx:{display:"flex",gap:2,flexWrap:"wrap"},children:[(0,B.jsx)(N.K,{label:"Start Date",value:d,onChange:h,slotProps:{textField:{size:"small"}}}),(0,B.jsx)(N.K,{label:"End Date",value:x,onChange:y,slotProps:{textField:{size:"small"}}})]})}),(0,B.jsx)(g.A,{variant:"outlined",onClick:()=>{if(l&&!b){if(S(!0),("Trip Travel Time"===l||"Schedule Adherence Heatmap"===l)&&s&&s.length>0){const e=s.map((e=>({id:Date.now()+Math.random(),type:l,startDate:d,endDate:x,routeId:e.routeId,routeName:`${e.routeShortName} - ${e.tripHeadsign}`,headsign:e.tripHeadsign})));v([...f,...e])}else if(("Signal Controller TSP Events"===l||"Signal Controller TSP Raw Events"===l)&&o&&o.length>0){const e=o.map((e=>({id:Date.now()+Math.random(),type:l,startDate:d,endDate:x,signalId:e.id,signalName:e.name||`Signal ${e.id}`,primaryRoad:e.primaryRoad||"",crossRoad:e.crossRoad||""})));v([...f,...e])}else{const e={id:Date.now(),type:l,startDate:d,endDate:x};v([...f,e])}S(!1)}},disabled:A||!l||b,startIcon:A?(0,B.jsx)(m.A,{size:20,color:"inherit"}):null,children:"Generate Report"})]}),b&&(0,B.jsx)(n.A,{variant:"body1",color:"error",sx:{mt:2,mb:2,textAlign:"center",fontSize:"1.1rem",fontWeight:"medium"},children:"Please select 10 or fewer items to generate reports"}),"Trip Travel Time"===l&&(!s||0===s.length)&&(0,B.jsx)(n.A,{variant:"body1",color:"error",sx:{mt:2,mb:2,textAlign:"center",fontSize:"1.1rem",fontWeight:"medium"},children:"Please select at least one route from the Transit Routes list"})]}),(0,B.jsxs)(r.A,{sx:{flex:1,overflowY:"auto",p:2},children:[f.map(((e,t)=>(0,B.jsxs)(r.A,{sx:{border:"1px solid #444",borderRadius:1,overflow:"hidden",height:"auto",minHeight:500,position:"relative",marginBottom:3,backgroundColor:"rgba(0, 0, 0, 0.2)"},children:[(0,B.jsx)(r.A,{sx:{position:"absolute",top:8,right:8,zIndex:10,backgroundColor:"rgba(0,0,0,0.5)",borderRadius:"50%"},children:(0,B.jsx)(i.A,{size:"small",onClick:()=>{return t=e.id,void v(f.filter((e=>e.id!==t)));var t},sx:{color:"white"},children:(0,B.jsx)(te.default,{fontSize:"small"})})}),"Trip Travel Time"===e.type&&(0,B.jsx)(Z,{routeId:e.routeId,headsign:e.headsign,startDate:e.startDate,endDate:e.endDate,routeName:e.routeName,onLoadComplete:()=>console.log(`Report ${e.id} loaded`)}),"Schedule Adherence Heatmap"===e.type&&(0,B.jsx)(W,{routeId:e.routeId,headsign:e.headsign,startDate:e.startDate,endDate:e.endDate,routeName:e.routeName,onLoadComplete:()=>console.log(`Report ${e.id} loaded`)}),"Signal Controller TSP Events"===e.type&&(0,B.jsx)(U,{signalId:e.signalId,signalName:e.signalName,primaryRoad:e.primaryRoad,crossRoad:e.crossRoad,startDate:e.startDate,endDate:e.endDate,onLoadComplete:()=>w(e.id)}),"Signal Controller TSP Raw Events"===e.type&&(0,B.jsx)(ee,{signalId:e.signalId,signalName:e.signalName,primaryRoad:e.primaryRoad,crossRoad:e.crossRoad,startDate:e.startDate,endDate:e.endDate,onLoadComplete:()=>w(e.id)})]},e.id))),0===f.length&&(0,B.jsx)(r.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"200px"},children:(0,B.jsx)(n.A,{variant:"body1",color:"#aaa",children:"No reports generated. Select a report type and time range, then click Generate Report."})})]})]})};var ae=s(65048),re=s(62145),oe=s(80392),ne=s(1352),ie=s(40932),le=s.n(ie),ce=(s(98900),s(7233)),de=s(94832);const ue={"mapboxgl-popup-content":"MapBoxComponent_mapboxgl-popup-content__r8x4H",marker:"MapBoxComponent_marker__O9WMB",signalMarker:"MapBoxComponent_signalMarker__R5b4N",stopMarker:"MapBoxComponent_stopMarker__0K3FE",layerControl:"MapBoxComponent_layerControl__Hprnl",popup:"MapBoxComponent_popup__6nibO",zonePopup:"MapBoxComponent_zonePopup__VF8Zo"};var pe=s(57514),he=s.n(pe);s(65406);const ge="mapbox://styles/mapbox/navigation-night-v1",me="mapbox://styles/mapbox/satellite-v9",xe="mapbox://styles/mapbox/streets-v12",ye="mapbox://styles/mapbox/light-v11",fe="mapbox://styles/mapbox/outdoors-v12",ve="mapbox://styles/mapbox/satellite-streets-v12";function Ae(e){let{center:t,zoom:s,isSignalListExpanded:o,vehicles:n,signals:i,selectedSignalIds:l,stops:c,stopTimes:d,shapes:h,dataLoading:g,gtfsRoutes:m,gtfsTrips:x,selectedZone:y,selectedZones:f,zones:v,polygonGeometry:A,onGeometryChange:S,editable:b}=e;const j=(0,a.useRef)(null),[w,T]=(0,a.useState)(null),I=(0,a.useRef)(null),C=(0,a.useRef)(new Map),k=(0,a.useRef)(new Map),z=(0,a.useRef)(new Map),E=(0,a.useRef)(null),D=(0,a.useRef)([]),M=((0,a.useRef)(null),(0,a.useRef)(new Map)),[R,L]=(0,a.useState)(!0),[P,F]=(0,a.useState)(!0),[N,H]=(0,a.useState)(!0),[Z,W]=(0,a.useState)(ge),U=e=>e<60?"green":e>=60&&e<180?"yellow":e>=180&&e<300?"orange":"red",O=e=>{const t=new Date(e);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")} ${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}:${String(t.getSeconds()).padStart(2,"0")}`};(0,a.useMemo)((()=>n.map((e=>({...e,color:U(e.lateness),route:m.find((t=>t.routeId===e.routeId)),trip:x.find((t=>t.tripId===e.tripId)),formattedTimestamp:O(e.timestamp)})))),[n,m,x]);(0,a.useEffect)((()=>{const e=new(le().Map)({container:j.current,style:Z,center:[-81.007666,46.486551],zoom:11});return e.addControl(new(le().NavigationControl),"top-right"),e.on("load",(()=>T(e))),b&&(I.current=new(he())({displayControlsDefault:!1,controls:{polygon:!0,trash:!0},defaultMode:"draw_polygon"}),e.addControl(I.current)),()=>{e&&e.remove()}}),[]),(0,a.useEffect)((()=>{w&&(b?(I.current=new(he())({displayControlsDefault:!1,controls:{polygon:!0,trash:!0}}),w.addControl(I.current)):I.current&&(w.removeControl(I.current),I.current=null))}),[w,b]),(0,a.useEffect)((()=>{var e;if(w&&b&&(null===(e=I.current)||void 0===e||e.deleteAll(),A)){const e={type:"Feature",geometry:A};I.current.add(e)}}),[w,b,A]),(0,a.useEffect)((()=>{if(w&&I.current){const e=e=>{const t=e.features[0].geometry;S&&S(t)};return w.on("draw.update",e),w.on("draw.create",e),w.on("draw.delete",(()=>{S&&S(null)})),()=>{w.off("draw.update",e),w.off("draw.create",e)}}}),[w,S]);(0,a.useCallback)((e=>`hsl(${e.split("").reduce(((e,t)=>t.charCodeAt(0)+((e<<5)-e)),0)%360}, 100%, 75%)`),[]);return(0,a.useEffect)((()=>{w&&(!t||isNaN(t.latitude)||isNaN(t.longitude)||isNaN(s)?console.error("Invalid center or zoom prop:",t,s):w.flyTo({center:[t.longitude,t.latitude],zoom:s}))}),[t,s]),(0,a.useEffect)((()=>{w&&w.resize()}),[o,w]),(0,a.useEffect)((()=>{if(w&&R){n.forEach((e=>{let t=C.current.get(e.vehicleId);const s=e.lateness,a=U(s),r=m.find((t=>t.routeId===e.routeId)),o=(r&&r.routeLongName,x.find((t=>t.tripId===e.tripId))),n=o?o.tripHeadsign:"Unknown Trip Headsign",i=O(e.timestamp);if(t){t.setLngLat([e.longitude,e.latitude]);const r=`\n                        <div style="background-color: white; color: black; padding: 10px; border-radius: 5px; width: fit-content ">\n                            <h2>${e.routeId} - ${n}</h2>\n                            <p style="margin: 0;">Vehicle ID: ${e.vehicleId}</p>\n                            <p style="margin: 0;">Timestamp: ${i}</p>\n                            <p style="margin: 0;">Lat: ${e.latitude}</p>\n                            <p style="margin: 0;">Long: ${e.longitude}</p>\n                            <p style="margin: 0;">Heading: ${e.bearing}</p>\n                            <p style="margin: 0;">Speed: ${e.speed}</p>\n                            <p style="margin: 0;">Lateness: ${s} sec</p>\n                        </div>\n                    `;t.getPopup().setHTML(r),t.getElement()&&t.getElement().firstChild&&(t.getElement().firstChild.style.color=a)}else{const r=document.createElement("div");(0,ne.H)(r).render((0,B.jsx)($.A,{style:{fontSize:"30px",color:a}})),r.className=ue.marker;const o=new(le().Popup)({offset:25}).setLngLat([e.longitude,e.latitude]).setHTML(`<div style="background-color: white; color: black; padding: 10px; border-radius: 5px; width: fit-content">\n                            <h2>${e.routeId} - ${n}</h2>\n                                <p style="margin: 0;">Vehicle ID: ${e.vehicleId}</p>\n                                <p style="margin: 0;">Timestamp: ${i}</p>\n                                <p style="margin: 0;">Lat: ${e.latitude}</p>\n                                <p style="margin: 0;">Long: ${e.longitude}</p>\n                                <p style="margin: 0;">Heading: ${e.bearing}</p>\n                                <p style="margin: 0;">Speed: ${e.speed}</p>\n                            <p style="margin: 0;">Lateness: ${s} sec</p>\n                            </div>`).addTo(w);t=new(le().Marker)(r).setLngLat([e.longitude,e.latitude]).setPopup(o).addTo(w),C.current.set(e.vehicleId,t)}}));const e=new Set(n.map((e=>e.vehicleId)));C.current.forEach(((t,s)=>{e.has(s)||(t.remove(),C.current.delete(s))}))}else C.current.forEach((e=>{e.remove()})),C.current.clear()}),[w,n,U,m,x,R]),(0,a.useEffect)((()=>{if(w&&(k.current.forEach((e=>{e.remove()})),k.current.clear(),P)){let e=[];e=null!==l&&void 0!==l?l.length>0?i.filter((e=>l.includes(e.id))):[]:i,e.forEach((e=>{const t=document.createElement("div");t.className=ue.signalMarker;const s=new(le().Popup)({offset:5}).setLngLat([e.longitude,e.latitude]).setHTML(`\n                        <div class="${ue.popup}">\n                            <h2>${e.name}</h2>\n                            <p>Primary Road: ${e.primaryRoad}</p>\n                            <p>Cross Road: ${e.crossRoad}</p>\n                        </div>\n                        `),a=new(le().Marker)(t).setLngLat([e.longitude,e.latitude]).setPopup(s).addTo(w);k.current.set(e.id,a)}))}}),[w,i,P,l]),(0,a.useEffect)((()=>{if(w&&!g){(c||[]).forEach((e=>{let t=z.current.get(e.stopId);if(!t){const s=document.createElement("div");s.className=ue.stopMarker;const a=d.find((t=>t.stopId===e.stopId))||{},r=new(le().Popup)({offset:25}).setLngLat([e.stopLon,e.stopLat]).setHTML(`<div class="${ue.popup}">\n                                <h2>${e.stopName}</h2>\n                                <p>Stop ID: ${e.stopId}</p>\n                                <p>Arrival Time: ${a.arrivalTime||"N/A"}</p>\n                                <p>Lat: ${e.stopLat}</p>\n                                <p>Long: ${e.stopLon}</p>\n                                <p>Sequence: ${a.stopSequence||"N/A"}</p>\n                            </div>`).addTo(w);t=new(le().Marker)(s).setLngLat([e.stopLon,e.stopLat]).setPopup(r).addTo(w),z.current.set(e.stopId,t)}}));const e=new Set(c.map((e=>e.stopId)));z.current.forEach(((t,s)=>{e.has(s)||(t.remove(),z.current.delete(s))}))}}),[w,c,d,g]),(0,a.useEffect)((()=>{if(w&&h.length>0&&!g){E.current&&(w.getLayer("shapeLayer")&&w.removeLayer("shapeLayer"),w.getSource("shapeSource")&&w.removeSource("shapeSource"));try{const e=h.map((e=>[e.shapePtLon,e.shapePtLat]));w.addSource("shapeSource",{type:"geojson",data:{type:"Feature",geometry:{type:"LineString",coordinates:e}}}),w.addLayer({id:"shapeLayer",type:"line",source:"shapeSource",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":"aqua","line-width":6}}),E.current=!0}catch(e){console.error("Error adding shape layer:",e)}}}),[w,h,g]),(0,a.useEffect)((()=>{w&&N&&Array.isArray(f)&&(M.current.forEach(((e,t)=>{w.getLayer(`zoneLayer-${t}`)&&w.removeLayer(`zoneLayer-${t}`),w.getLayer(`zoneOutline-${t}`)&&w.removeLayer(`zoneOutline-${t}`),w.getSource(`zoneSource-${t}`)&&w.removeSource(`zoneSource-${t}`)})),M.current.clear(),f.forEach((e=>{try{const t=(0,ae.nI)(e.geometry);w.addSource(`zoneSource-${e.id}`,{type:"geojson",data:{type:"Feature",geometry:t,properties:{id:e.id,name:e.name,type:1===e.type?"TSP":2===e.type?"EVP":"Unknown"}}}),w.addLayer({id:`zoneLayer-${e.id}`,type:"fill",source:`zoneSource-${e.id}`,layout:{},paint:{"fill-color":"#0080ff","fill-opacity":.5}}),w.addLayer({id:`zoneOutline-${e.id}`,type:"line",source:`zoneSource-${e.id}`,layout:{},paint:{"line-color":"#000","line-width":3}}),w.on("click",`zoneLayer-${e.id}`,(t=>{const s=w.queryRenderedFeatures(t.point,{layers:[`zoneLayer-${e.id}`]});if(s.length>0){const{properties:a}=s[0],r=`\n                                <div class="${ue.popup}">\n                                    <h2>${a.name||`Zone ${a.id}`}</h2>\n                                    <p>Zone ID: ${a.id}</p>\n                                    <p>Type: ${a.type}</p>\n                                    ${e.description?`<p>Description: ${e.description}</p>`:""}\n                                </div>\n                            `,o=new(le().Popup)({offset:[0,-5],className:ue.mapboxglPopupContent}).setLngLat(t.lngLat).setHTML(r).addTo(w);D.current.push(o)}})),w.on("mouseenter",`zoneLayer-${e.id}`,(()=>{w.getCanvas().style.cursor="pointer"})),w.on("mouseleave",`zoneLayer-${e.id}`,(()=>{w.getCanvas().style.cursor=""})),M.current.set(e.id,!0)}catch(t){console.error("Error adding zone to map:",t)}})))}),[w,f,N]),(0,a.useEffect)((()=>{w&&!N&&M.current&&(M.current.forEach(((e,t)=>{w.getLayer(`zoneLayer-${t}`)&&w.removeLayer(`zoneLayer-${t}`),w.getLayer(`zoneOutline-${t}`)&&w.removeLayer(`zoneOutline-${t}`),w.getSource(`zoneSource-${t}`)&&w.removeSource(`zoneSource-${t}`)})),M.current.clear())}),[w,N]),(0,a.useEffect)((()=>()=>{D.current.forEach((e=>{e&&e.remove()})),D.current=[]}),[f]),(0,B.jsxs)("div",{style:{position:"relative",width:"100%",height:"100%"},children:[(0,B.jsx)("div",{ref:j,style:{width:"100%",height:"100%"}}),(0,B.jsx)("div",{className:ue.layerControl,children:(0,B.jsxs)(r.A,{sx:{display:"flex",flexDirection:"column",gap:1},children:[(0,B.jsxs)(u.A,{value:Z,onChange:e=>{const t=e.target.value;w&&(w.setStyle(t),w.once("style.load",(()=>{C.current.forEach((e=>e.remove())),C.current.clear(),k.current.forEach((e=>e.remove())),k.current.clear(),z.current.forEach((e=>e.remove())),z.current.clear(),E.current&&(E.current=!1),M.current.clear(),P&&(F(!1),setTimeout((()=>F(!0)),50)),R&&(L(!1),setTimeout((()=>L(!0)),50)),N&&(H(!1),setTimeout((()=>H(!0)),50))}))),W(t)},size:"small",sx:{bgcolor:"rgba(0, 0, 0, 0.6)",color:"white",width:"180px",mb:1,"& .MuiSelect-icon":{color:"white"}},children:[(0,B.jsx)(p.A,{value:ge,children:"Dark Map"}),(0,B.jsx)(p.A,{value:ye,children:"Light Map"}),(0,B.jsx)(p.A,{value:xe,children:"Streets"}),(0,B.jsx)(p.A,{value:fe,children:"Outdoors"}),(0,B.jsx)(p.A,{value:me,children:"Satellite"}),(0,B.jsx)(p.A,{value:ve,children:"Satellite Streets"})]}),(0,B.jsx)(ce.A,{control:(0,B.jsx)(de.A,{checked:R,onChange:e=>L(e.target.checked),color:"primary"}),label:"Vehicles"}),(0,B.jsx)(ce.A,{control:(0,B.jsx)(de.A,{checked:P,onChange:e=>F(e.target.checked),color:"primary"}),label:"Traffic Signals"}),(0,B.jsx)(ce.A,{control:(0,B.jsx)(de.A,{checked:N,onChange:e=>H(e.target.checked),color:"primary"}),label:"Zones"})]})})]})}le().accessToken="pk.eyJ1IjoiZWhzYW5iYWdoZXJpIiwiYSI6ImNrazk3NGI5azA5ZmsycG9jNzh5bmZ0eHYifQ.IQMxU9VDOG8Q5nCMh6Owyw";const Se=a.memo(Ae);var be=s(55515),je=s(33344),we=s(249),Te=s(70146);(0,Te.A)(O.A)((e=>{let{theme:t}=e;return{width:"400px",position:"absolute",top:"20%",left:"40%",backgroundColor:t.palette.background.paper,boxShadow:t.shadows[5],display:"flex",flexDirection:"column",alignItems:"center",padding:t.spacing(2)}})),(0,Te.A)(we.A)((e=>{let{theme:t}=e;return{width:"100%",maxHeight:"300px",overflowY:"auto",marginTop:t.spacing(2)}}));var Ie=s(62685),Ce=s(53065);const $e=function(e){let{onDeviceSelect:t,signals:s,onSignalSelectionChange:r,selectedSignalIds:o=[]}=e;const[n,l]=(0,a.useState)("vertical"),[c,d]=(0,a.useState)("auto"),[u,p]=(0,a.useState)("100%"),[h,g]=(0,a.useState)(300),[m,x]=(0,a.useState)("multiple"),[y,f]=(0,a.useState)(!1),[v,A]=(0,a.useState)([]),[S,b]=(0,a.useState)(""),[j,w]=(0,a.useState)(null),[T,I]=(0,a.useState)(null),[C,$]=(0,a.useState)(!1),k=o.map((e=>s.findIndex((t=>t.id===e)))).filter((e=>-1!==e));(0,a.useEffect)((()=>{if(!C&&s.length>0&&0===o.length){const e=s.map((e=>e.id));Array.from({length:s.length},((e,t)=>t));r&&r(e,s),$(!0),console.log("Initially selected all signals:",e.length)}}),[s,C,o,r]);const z=()=>{const e=window.innerWidth;return e>1920?"86vh":e<=1920&&e>1024?"84.5vh":"70vh"};(0,a.useEffect)((()=>{const e=()=>{p(z())};return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)}),[]);const E=[{name:"name",label:"Name",options:{filter:!0,sort:!0,setCellProps:()=>({style:{padding:"2px 0px 2px 10px",fontSize:"0.875rem"}})}},{name:"primaryRoad",label:"Main St.",options:{filter:!0,sort:!0,setCellProps:()=>({style:{padding:"2px",fontSize:"0.875rem"}})}},{name:"crossRoad",label:"Side St.",options:{filter:!0,sort:!0,setCellProps:()=>({style:{padding:"2px",fontSize:"0.875rem"}})}}],D={...{filter:!0,search:!0,download:!1,print:!1,viewColumns:!1,searchOpen:!1,searchAlwaysOpen:!1,filterType:"dropdown",responsive:n,fixedHeader:!0,tableBodyMaxHeight:"100%",scrollMaxHeight:"auto",scroll:"auto",tableBodyHeight:z(),pagination:!1,draggableColumns:{enabled:!0,transitionTime:h},responsive:"standard",selectableRows:m,setRowProps:(e,t)=>t===T?{style:{backgroundColor:"gray",height:"30px"}}:{style:{height:"30px"}},setTableProps:()=>({size:"small",padding:"none"}),onRowClick:(e,t)=>{f(!0),I(t.dataIndex)},customToolbar:()=>(0,B.jsx)("div",{style:{display:"inline-flex"},children:(0,B.jsx)(Ce.A,{title:"Locate Device",arrow:!0,children:(0,B.jsx)("span",{children:(0,B.jsx)(i.A,{disabled:!y,onClick:()=>{const e=s[T];t(e)},children:(0,B.jsx)(Ie.A,{})})})})})},tableBodyMaxHeight:u,selectableRows:"multiple",rowsSelected:k,onRowSelectionChange:(e,t)=>{console.log("Selection changed:",e,t);const a=t.map((e=>e.dataIndex)).map((e=>s[e])),o=a.map((e=>e.id));console.log("Selected signal IDs:",o),r&&r(o,a)},selectToolbarPlacement:"none"};return(0,B.jsx)(a.Fragment,{children:(0,B.jsx)(je.Ay,{title:"Intersections",data:s,columns:E,options:D})})};var ke=s(70367);const ze=e=>{let{routes:t,onRouteSelectionChange:s,initiallyLoaded:o,setInitiallyLoaded:n,selectedIndices:i=[]}=e;const[l,c]=(0,a.useState)("100%"),[d,u]=(0,a.useState)(i);(0,a.useEffect)((()=>{u(i)}),[i]);const p=()=>{const e=window.innerWidth;return e>1920?"86vh":e<=1920&&e>1024?"84.5vh":"70vh"};(0,a.useEffect)((()=>{const e=()=>{c(p())};return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)}),[]),(0,a.useEffect)((()=>{if(!o&&t.length>0){const e=Array.from({length:t.length},((e,t)=>t));u(e);const a=t.map((e=>e));s(a,e),n(!0)}}),[t,o,s,n]);const h=[{name:"routeShortName",label:"Name",options:{setCellProps:()=>({style:{padding:"4px"}})}},{name:"tripHeadsign",label:"Direction",options:{setCellProps:()=>({style:{padding:"4px"}})}}],g={filter:!0,search:!0,download:!1,print:!1,viewColumns:!1,responsive:"standard",fixedHeader:!0,pagination:!1,tableBodyMaxHeight:l,scrollMaxHeight:"auto",scroll:"auto",tableBodyHeight:p(),selectableRows:"multiple",selectableRowsOnClick:!0,rowsSelected:d,onRowSelectionChange:(e,a)=>{const r=a.map((e=>e.dataIndex));u(r);const o=null===a||void 0===a?void 0:a.map((e=>t[e.dataIndex])).filter((e=>e));s(o,r),console.log("Selected Routes from Table:",o)},setTableProps:()=>({size:"small"}),selectToolbarPlacement:"none"};return(0,B.jsx)(r.A,{children:(0,B.jsx)(ke.Ay,{title:"Transit Routes",data:t,columns:h,options:g})})};var Ee=s(55858),De=s(99532);const Me=e=>{let{zones:t,onZoneSelectionChange:s,onZoneEdit:o,onAddZone:n,initiallyLoaded:l,setInitiallyLoaded:c,selectedIndices:d=[]}=e;const[u,p]=(0,a.useState)("100%"),[h,g]=(0,a.useState)(d);(0,a.useEffect)((()=>{d&&d.length>0&&g(d)}),[d]);(0,a.useEffect)((()=>{if(!l&&t.length>0){const e=Array.from({length:t.length},((e,t)=>t));g(e);const a=t.map((e=>e));s(a,e),c(!0)}}),[t,l,s,c]);const m=()=>{n()},x=()=>{const e=window.innerWidth;return e>1920?"86vh":e<=1920&&e>1024?"84.5vh":"70vh"};(0,a.useEffect)((()=>{const e=()=>{p(x())};return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)}),[]);const y="4px",f=[{name:"name",label:"Zone Name",setCellProps:()=>({style:{padding:y}})},{name:"type",label:"Type",setCellProps:()=>({style:{padding:y}}),options:{customBodyRender:e=>1===e?"TSP":2===e?"EVP":"Unknown"}},{name:"actions",label:"Actions",setCellProps:()=>({style:{padding:y}}),options:{customBodyRender:(e,s)=>{const a=s.rowIndex,r=t[a];return(0,B.jsx)(i.A,{onClick:()=>o(r),color:"primary",children:(0,B.jsx)(Ee.A,{})})}}}],v={filter:!0,search:!0,download:!1,print:!1,viewColumns:!1,responsive:"standard",fixedHeader:!0,pagination:!1,tableBodyMaxHeight:u,scrollMaxHeight:"auto",scroll:"auto",tableBodyHeight:x(),selectToolbarPlacement:"none",selectableRows:"multiple",selectableRowsOnClick:!0,rowsSelected:h,onRowSelectionChange:(e,a)=>{((e,a)=>{const r=a.map((e=>e.dataIndex));g(r);const o=null===a||void 0===a?void 0:a.map((e=>t[e.dataIndex])).filter((e=>e));s(o,r),console.log("Selected Zones from Table:",o)})(0,a)},setTableProps:()=>({size:"small"}),customToolbar:()=>(0,B.jsx)("div",{style:{display:"inline-flex"},children:(0,B.jsx)(Ce.A,{title:"Add New Zone",arrow:!0,children:(0,B.jsx)("span",{children:(0,B.jsx)(i.A,{onClick:m,color:"primary",title:"Add New Zone",children:(0,B.jsx)(De.A,{})})})})})};return(0,B.jsx)(r.A,{children:(0,B.jsx)(ke.Ay,{title:"Zones",data:t,columns:f,options:v})})};var Re=s(69019),Le=s(7999),Pe=s(92098),Fe=s(35607),Ne=s(42209),He=s(74745);const Be=e=>{let{initialScheduleMatrix:t,handleScheduleUpdate:s,editable:i}=e;const l=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],c=Array.from({length:96},((e,t)=>({time:`${String(Math.floor(t/4)).padStart(2,"0")}:${String(t%4*15).padStart(2,"0")}`,showDivider:t%4===3}))),d=(e,s)=>t[e][s];return(0,B.jsx)(r.A,{sx:{maxHeight:"19vh",overflowY:"auto",width:"40vh"},children:(0,B.jsxs)(o.Ay,{container:!0,sx:{width:"auto",minWidth:"100%",boxSizing:"border-box"},children:[(0,B.jsx)(o.Ay,{item:!0,xs:1,children:" "}),l.map(((e,t)=>(0,B.jsx)(o.Ay,{item:!0,xs:1.5,children:(0,B.jsx)(n.A,{align:"center",sx:{fontWeight:"bold",fontSize:"0.75rem"},children:e})},t))),c.map(((e,c)=>(0,B.jsx)(a.Fragment,{children:(0,B.jsxs)(o.Ay,{container:!0,item:!0,xs:12,spacing:1,style:{paddingTop:0},children:[(0,B.jsx)(o.Ay,{item:!0,xs:1,children:(0,B.jsx)(n.A,{sx:{fontSize:"0.75rem",padding:"2px 0"},children:e.time})}),l.map(((e,a)=>(0,B.jsx)(o.Ay,{item:!0,xs:1.5,style:{paddingTop:8},children:(0,B.jsx)(r.A,{sx:{width:"100%",height:"15px",backgroundColor:d(a,c)?"green":"grey",margin:"0px",padding:"0px",cursor:i?"pointer":"default",borderRight:a<6?"1px solid #ccc":"none"},onClick:()=>((e,a)=>{if(!i)return;const r=t.map(((t,s)=>t.map(((t,r)=>s===e&&r===a?!t:t))));s(r)})(a,c)})},a))),e.showDivider&&(0,B.jsx)(o.Ay,{item:!0,xs:12,style:{paddingTop:0},children:(0,B.jsx)(He.A,{sx:{marginY:.5}})})]})},c)))]})})},Ze=()=>Array.from({length:7},(()=>Array(96).fill(!1))),We=e=>`${String(Math.floor(e/4)).padStart(2,"0")}:${String(e%4*15).padStart(2,"0")}:00`,Ue=(e,t)=>{const s=[];return e.forEach(((e,a)=>{let r=!1,o=null;e.forEach(((e,n)=>{e&&!r?(r=!0,o=n):!e&&r&&(s.push(Oe(a+1,o,n,t)),r=!1)})),r&&s.push(Oe(a+1,o,e.length,t))})),s},Oe=(e,t,s,a)=>({signalId:a,dayOfWeek:e,startTime:We(t),endTime:We(s)}),Ge=e=>{let{zone:t,onZoneUpdate:s,onEditGeometry:l,updatedGeometry:c,onZoneDelete:d}=e;const[g,m]=(0,a.useState)([]),[x,y]=(0,a.useState)([]),[f,v]=(0,a.useState)([]),[A,S]=(0,a.useState)([]),[b,j]=(0,a.useState)(null),[w,T]=(0,a.useState)({}),[I,$]=(0,a.useState)((()=>Ze())),[k,z]=(0,a.useState)({}),[E,D]=(0,a.useState)(t),[M,R]=(0,a.useState)(!t.id),L=""!==E.name.trim()&&null!==E.geometry;(0,a.useEffect)((()=>{F(),N(),H()}),[]),(0,a.useEffect)((()=>{c&&D((e=>({...e,geometry:c})))}),[c]),(0,a.useEffect)((()=>{console.log("Zone ID is "+t.id),t&&t.id?(D(t),P(t.id)):(D({id:null,name:"",type:1,geometry:null}),m([]),j(null),R(!0),l(t))}),[t]);const P=async e=>{try{const t=await C.getAllZoneSignalsForZone(e);t.success?(m(t.zoneSignals),j(t.zoneSignals.length?t.zoneSignals[0].id:null),U(t.zoneSignals)):console.error("Failed to fetch zone signals:",t.errorMessage)}catch(t){console.error("Error fetching zone signals:",t)}},F=async()=>{try{const e=await C.getTrafficSignals();e.success?y(e.devices):console.error("Failed to fetch traffic signals:",e.errorMessage)}catch(e){console.error("Error fetching traffic signals:",e)}},N=async()=>{try{const e=await C.getGtfsRoutes();e.success?v(e.routes):console.error("Failed to fetch GTFS routes:",e.errorMessage)}catch(e){console.error("Error fetching GTFS routes:",e)}},H=async()=>{try{const e=await C.getTransitRoutesWithHeadsigns();e.success?S(e.routes):console.error("Failed to fetch transit routes with headsigns:",e.errorMessage)}catch(e){console.error("Error fetching transit routes with headsigns:",e)}},Z=e=>{const t=g.find((t=>t.id===e));if(t&&t.schedules){const s=W(t.schedules);z((t=>({...t,[e]:s})))}else z((t=>({...t,[e]:Ze()})))},W=e=>{const t=Array.from({length:7},(()=>Array(96).fill(!1)));return e.forEach((e=>{const s=e.dayOfWeek-1,a=G(e.startTime),r=G(e.endTime)-1;for(let o=a;o<=r;o++)t[s][o]=!0})),t},U=e=>{const t={};e&&Array.isArray(e)&&e.forEach((e=>{e&&e.id&&(t[e.id]=e.schedules?O(e.schedules):Ze())})),T(t)},O=e=>{const t=Ze();return e.forEach((e=>{const s=e.dayOfWeek-1,a=G(e.startTime),r=G(e.endTime)-1;for(let o=a;o<=r;o++)t[s][o]=!0})),t},G=e=>{const[t,s]=e.split(":").map(Number);return 4*t+Math.floor(s/15)},Y=e=>{const{value:t}=e.target;m((e=>e.map((e=>e.id===b?{...e,signalId:t}:e))))},_=e=>{const{value:t}=e.target;m((e=>e.map((e=>e.id===b?{...e,routeId:t,tripHeadsign:""}:e))))},q=e=>{const{value:t}=e.target;m((e=>e.map((e=>e.id===b?{...e,tripHeadsign:t}:e))))},V=e=>{const{name:t,value:s}=e.target;m((e=>e.map((e=>e.id===b?{...e,[t]:s}:e))))};return(0,B.jsxs)(r.A,{display:"flex",flexDirection:"row",justifyContent:"space-between",sx:{overflowY:"auto"},children:[(0,B.jsxs)(r.A,{flex:1,padding:2,children:[(0,B.jsxs)(r.A,{display:"flex",flexDirection:"row",justifyContent:"space-between",children:[(0,B.jsx)(h.A,{label:"Zone Name",name:"name",value:E.name||"",onChange:e=>{const{name:t,value:s}=e.target;D((e=>({...e,[t]:s})))},fullWidth:!0,InputProps:{readOnly:!M}}),M?(0,B.jsxs)(B.Fragment,{children:[(0,B.jsx)(i.A,{color:"primary",onClick:async()=>{const e=E.geometry?(0,ae.B4)(E.geometry):null,t={...E,geometry:e},a=await C.saveZone(t);if(a.success){const e=a.message.id,t=g.map((async t=>{const s=Ue(w[t.id]||Ze(),t.id),a={...t,zone:{id:e},schedules:s};return await C.saveZoneSignal(a)}));try{(await Promise.all(t)).forEach((e=>{if(!e.success)throw new Error(`Failed to save zone signal: ${e.errorMessage}`)})),await P(e),R(!1),D((t=>({...t,id:e}))),s()}catch(r){console.error("Error saving zone signals:",r)}}else console.error("Failed to save zone:",a.errorMessage)},disabled:!L,children:(0,B.jsx)(Pe.A,{})}),(0,B.jsx)(i.A,{color:"default",onClick:()=>{R(!1),D(t),s()},children:(0,B.jsx)(Ne.A,{})})]}):(0,B.jsx)(i.A,{color:"primary",onClick:()=>{R(!0),l&&l(E)},children:(0,B.jsx)(Le.A,{})}),(0,B.jsx)(i.A,{color:"secondary",onClick:async()=>{if(!E.id)return;const e=await C.deleteZone(E.id);e.success?d():console.error("Failed to delete zone:",e.errorMessage)},disabled:!M||!t.id,children:(0,B.jsx)(Fe.default,{})})]}),(0,B.jsx)(n.A,{variant:"h6",sx:{marginTop:2},children:"Zone Assignments"}),(0,B.jsxs)(r.A,{display:"flex",alignItems:"center",gap:1,children:[(0,B.jsxs)(u.A,{value:b||"",onChange:e=>{const t=e.target.value;console.log("Selected Signal ID:",t),j(t),Z(t)},fullWidth:!0,children:[0===g.length&&(0,B.jsx)(p.A,{value:"",children:"No Signals"}),g.map((e=>(0,B.jsx)(p.A,{value:e.id,children:e.tripHeadsign||`Signal ${e.id}`},e.id)))]}),(0,B.jsx)(i.A,{onClick:()=>{const e={id:-1,signalId:x.length>0?x[0].id:"",routeId:f.length>0?f[0].routeId:"",tripHeadsign:A.length>0?A[0].tripHeadsign:"",eta:"",priorityInput:"",routePriority:"",latenessThreshold:"",headingRange:""};m((t=>[...t,e])),j(e.id),z((t=>({...t,[e.id]:Ze()})))},color:"primary",title:"Add New Zone Signal",disabled:!M,children:(0,B.jsx)(De.A,{})})]}),(()=>{const e=g.find((e=>e.id===b));if(!e)return null;const t=A.filter((t=>t.routeId===e.routeId));return(0,B.jsxs)(r.A,{children:[(0,B.jsxs)(o.Ay,{container:!0,spacing:1,sx:{paddingTop:"20px"},children:[(0,B.jsx)(o.Ay,{item:!0,xs:12,sm:6,md:2,children:(0,B.jsxs)(u.A,{label:"Active State",value:e.active?"Yes":"No",onChange:e=>(e=>{const t="Yes"===e;m((e=>e.map((e=>e.id===b?{...e,active:t}:e))))})(e.target.value),fullWidth:!0,disabled:!M,children:[(0,B.jsx)(p.A,{value:"Yes",children:"Yes"}),(0,B.jsx)(p.A,{value:"No",children:"No"})]})}),(0,B.jsx)(o.Ay,{item:!0,xs:12,sm:6,md:2,children:(0,B.jsx)(u.A,{label:"Route ID",value:e.routeId||"",onChange:_,fullWidth:!0,disabled:!M,children:f.map((e=>(0,B.jsx)(p.A,{value:e.routeId,children:e.routeShortName},e.routeId)))})}),(0,B.jsx)(o.Ay,{item:!0,xs:12,sm:6,md:4,children:(0,B.jsx)(u.A,{label:"Trip Headsign",value:e.tripHeadsign||"",onChange:q,fullWidth:!0,disabled:!M,children:t.map(((e,t)=>(0,B.jsx)(p.A,{value:e.tripHeadsign,children:e.tripHeadsign},t)))})}),(0,B.jsx)(o.Ay,{item:!0,xs:12,sm:6,md:3,children:(0,B.jsx)(u.A,{label:"Signal ID",value:e.signalId||"",onChange:Y,fullWidth:!0,disabled:!M,children:x.map((e=>(0,B.jsx)(p.A,{value:e.id,children:e.name},e.id)))})})]}),(0,B.jsxs)(o.Ay,{container:!0,spacing:1,sx:{paddingTop:"20px"},children:[(0,B.jsx)(o.Ay,{item:!0,xs:12,sm:6,md:1.5,children:(0,B.jsx)(h.A,{label:"ETA",name:"eta",type:"number",value:e.eta||"",onChange:V,inputProps:{min:1,max:255},fullWidth:!0,disabled:!M})}),(0,B.jsx)(o.Ay,{item:!0,xs:12,sm:6,md:2,children:(0,B.jsx)(h.A,{label:"Priority Input",name:"priorityInput",type:"number",value:e.priorityInput||"",onChange:V,inputProps:{min:1,max:255},fullWidth:!0,disabled:!M})}),(0,B.jsx)(o.Ay,{item:!0,xs:12,sm:6,md:2,children:(0,B.jsx)(h.A,{label:"Route Priority",name:"routePriority",type:"number",value:e.routePriority||"",onChange:V,inputProps:{min:1,max:255},fullWidth:!0,disabled:!M})}),(0,B.jsx)(o.Ay,{item:!0,xs:12,sm:6,md:2,children:(0,B.jsx)(h.A,{label:"Direction Priority",name:"headsignPriority",type:"number",value:e.routePriority||"",onChange:V,inputProps:{min:1,max:255},fullWidth:!0,disabled:!M})}),(0,B.jsx)(o.Ay,{item:!0,xs:12,sm:6,md:2,children:(0,B.jsx)(h.A,{label:"Lateness Threshold",name:"latenessThreshold",type:"number",value:e.latenessThreshold||"",onChange:V,inputProps:{min:1,max:60},fullWidth:!0,disabled:!M})}),2===E.type&&(0,B.jsxs)(B.Fragment,{children:[(0,B.jsx)(o.Ay,{item:!0,xs:12,sm:6,md:4,children:(0,B.jsx)(u.A,{label:"Signal ID",value:e.signalId||"",onChange:Y,fullWidth:!0,disabled:!M,children:x.map((e=>(0,B.jsx)(p.A,{value:e.id,children:e.name},e.id)))})}),(0,B.jsx)(o.Ay,{item:!0,xs:12,sm:6,md:4,children:(0,B.jsx)(h.A,{label:"Heading Range",name:"headingRange",value:e.headingRange||"",onChange:V,fullWidth:!0,disabled:!M,inputProps:{min:1,max:60}})}),(0,B.jsx)(o.Ay,{item:!0,xs:12,sm:6,md:4,children:(0,B.jsx)(h.A,{label:"Priority Input",name:"priorityInput",type:"number",value:e.priorityInput||"",onChange:V,inputProps:{min:1,max:255},fullWidth:!0})})]})]})]})})()]}),(0,B.jsxs)(r.A,{flex:.5,padding:2,sx:{border:"dotted"},children:[(0,B.jsx)(n.A,{variant:"h6",sx:{margin:"5px 0"},children:"Schedules"}),(0,B.jsx)(Be,{initialScheduleMatrix:w[b]||Ze(),handleScheduleUpdate:e=>((e,t)=>{T((s=>({...s,[e]:t})))})(b,e),editable:M})]})]})},Ye=e=>{const{children:t,value:s,index:a,...o}=e;return(0,B.jsx)("div",{role:"tabpanel",hidden:s!==a,...o,children:s===a&&(0,B.jsx)(r.A,{sx:{p:0},children:t})})},_e=e=>{const t=new Date(e),s=new Date(t.toLocaleString());return`${s.getFullYear()}-${String(s.getMonth()+1).padStart(2,"0")}-${String(s.getDate()).padStart(2,"0")} ${String(s.getHours()).padStart(2,"0")}:${String(s.getMinutes()).padStart(2,"0")}:${String(s.getSeconds()).padStart(2,"0")}`},qe=e=>e<60?"green":e>=60&&e<180?"yellow":e>=180&&e<300?"orange":"red",Ve=e=>{let{vehicles:t,onVehicleSelect:s,selectedZone:o,onEditGeometry:i,onGeometryChange:l,defaultTab:c,updatedGeometry:d,onZoneUpdate:u,onZoneDelete:p}=e;const[h,g]=(0,a.useState)(c||0),[m,y]=(0,a.useState)(null),[f,v]=(0,a.useState)(null);(0,a.useEffect)((()=>{o&&v(o)}),[o]),(0,a.useEffect)((()=>{void 0!==c&&g(c)}),[c]);const A=t.slice().sort(((e,t)=>t.lateness-e.lateness)),S="4px",b=[{name:"vehicleId",label:"Vehicle ID",options:{setCellProps:()=>({style:{padding:"4px 0px 4px 20px"}})}},{name:"routeId",label:"Route ID",options:{setCellProps:()=>({style:{padding:S}})}},{name:"tripId",label:"Trip ID",options:{setCellProps:()=>({style:{padding:S}})}},{name:"timestamp",label:"Timestamp",options:{customBodyRender:e=>_e(e),setCellProps:()=>({style:{padding:S}})}},{name:"lateness",label:"Lateness (sec)",options:{customBodyRender:e=>(0,B.jsxs)(r.A,{sx:{display:"flex",alignItems:"center"},children:[(0,B.jsx)(r.A,{sx:{width:16,height:16,backgroundColor:qe(e),marginRight:1}}),e," sec"]}),setCellProps:()=>({style:{padding:S}})}},{name:"speed",label:"Speed",options:{setCellProps:()=>({style:{padding:S}})}},{name:"bearing",label:"Heading",options:{setCellProps:()=>({style:{padding:S}})}}],j=A.map((e=>({vehicleId:e.vehicleId,routeId:e.routeId,tripId:e.tripId,timestamp:_e(e.timestamp),lateness:e.lateness,speed:e.speed,bearing:e.bearing}))),w={filter:!0,download:!1,print:!1,viewColumns:!1,responsive:"standard",fixedHeader:!0,tableBodyMaxHeight:"430px",tableBodyHeight:"300px",selectableRows:"none",setRowProps:(e,t)=>({style:{backgroundColor:e[0]===m?"#5d5d5d":"inherit",cursor:"pointer"}}),onRowClick:e=>{const t=e[0];y(t),s(t)}},T={...w};return(0,B.jsxs)(r.A,{sx:{width:"100%"},children:[(0,B.jsxs)(Re.A,{value:h,onChange:(e,t)=>{g(t)},"aria-label":"Vehicle List Tabs",children:[(0,B.jsx)(x.A,{label:"Vehicle List"}),(0,B.jsx)(x.A,{label:"Properties"})]}),(0,B.jsx)(Ye,{value:h,index:0,children:(0,B.jsx)(r.A,{sx:{height:"430px",overflowY:"auto"},children:(0,B.jsx)(ke.Ay,{data:j,columns:b,options:T})})}),(0,B.jsx)(Ye,{value:h,index:1,children:(0,B.jsx)(r.A,{sx:{height:"25vh",overflowY:"auto"},children:o?(0,B.jsx)(Ge,{zone:o,onEditGeometry:i,onGeometryChange:l,updatedGeometry:d,onZoneUpdate:u,onZoneDelete:p}):(0,B.jsx)(r.A,{children:(0,B.jsx)(n.A,{variant:"h6",sx:{marginTop:2},children:"No Item is Selected"})})})})]})},Je=a.memo((e=>{let{data:t,compareOption:s,compareToOption:a}=e;const r=t.map((e=>e.name||"Unknown")),o=t.map((e=>{var t;const s=null!==(t=e[a])&&void 0!==t?t:0;return"number"===typeof s?s:0}));console.log("Travel Time Data:",o);const n={chart:{type:"bar",height:250},plotOptions:{bar:{horizontal:!1,columnWidth:"55%",endingShape:"rounded"}},dataLabels:{enabled:!1},stroke:{show:!0,width:2,colors:["transparent"]},xaxis:{categories:r,labels:{style:{colors:"#a9a9a9"}},axisTicks:{show:!0,color:"#a9a9a9"}},yaxis:{labels:{style:{colors:"#a9a9a9"}},title:{text:"Time (minutes)",style:{color:"#a9a9a9"},labels:{style:{colors:"#a9a9a9"}}},axisBorder:{show:!0,color:"#a9a9a9"},axisTicks:{show:!0,color:"#a9a9a9"}},fill:{opacity:1},grid:{borderColor:"#a9a9a9",strokeDashArray:3},legend:{labels:{colors:"#a9a9a9"}},tooltip:{theme:"dark",style:{fontSize:"12px",fontFamily:void 0,colors:["#a9a9a9"]},y:{formatter:e=>`${e} minutes`}}},i=[{name:s,data:t.map((e=>e[s]||0)),color:"#71d3c8"},{name:a,data:t.map((e=>e[a]||0)),color:"#dbaa32"}];return(0,B.jsx)(be.A,{title:"Average Travel Time",children:(0,B.jsx)(H.A,{options:n,series:i,type:"bar",height:250})})})),Ke=e=>{let{data:t,compareOption:s,compareToOption:a}=e;const r={chart:{type:"bar",height:250},plotOptions:{bar:{horizontal:!1,columnWidth:"55%",endingShape:"rounded"}},dataLabels:{enabled:!1},stroke:{show:!0,width:2,colors:["transparent"]},xaxis:{categories:t.map((e=>e.name||"Unknown")),labels:{style:{colors:"#a9a9a9"}},axisTicks:{show:!0,color:"#a9a9a9"}},yaxis:{labels:{style:{colors:"#a9a9a9"}},title:{text:"Speed (km/h)",style:{color:"#a9a9a9"},labels:{style:{colors:"#a9a9a9"}}},axisBorder:{show:!0,color:"#a9a9a9"},axisTicks:{show:!0,color:"#a9a9a9"}},fill:{opacity:1},grid:{borderColor:"#a9a9a9",strokeDashArray:3},legend:{labels:{colors:"#a9a9a9"}},tooltip:{theme:"dark",style:{fontSize:"12px",fontFamily:void 0,colors:["#a9a9a9"]},y:{formatter:e=>`${e} km/h`}}},o=[{name:s,data:t.map((e=>e[s]||0)),color:"#71d3c8"},{name:a,data:t.map((e=>e[a]||0)),color:"#dbaa32"}];return(0,B.jsx)(be.A,{title:"Average Speed",children:(0,B.jsx)(H.A,{options:r,series:o,type:"bar",height:250})})},Qe=a.memo((e=>{let{data:t,compareOption:s,compareToOption:a}=e;const r=t.map((e=>e.name||"Unknown")),o=t.map((e=>{var t;const s=null!==(t=e[a])&&void 0!==t?t:0;return"number"===typeof s?s:0}));console.log("Travel Time Data:",o);const n={chart:{type:"bar",height:250},plotOptions:{bar:{horizontal:!1,columnWidth:"55%",endingShape:"rounded"}},dataLabels:{enabled:!1},stroke:{show:!0,width:2,colors:["transparent"]},xaxis:{categories:r,labels:{style:{colors:"#a9a9a9"}},axisTicks:{show:!0,color:"#a9a9a9"}},yaxis:{labels:{style:{colors:"#a9a9a9"}},title:{text:"Time (minutes)",style:{color:"#a9a9a9"},labels:{style:{colors:"#a9a9a9"}}},axisBorder:{show:!0,color:"#a9a9a9"},axisTicks:{show:!0,color:"#a9a9a9"}},fill:{opacity:1},grid:{borderColor:"#a9a9a9",strokeDashArray:3},legend:{labels:{colors:"#a9a9a9"}},tooltip:{theme:"dark",style:{fontSize:"12px",fontFamily:void 0,colors:["#a9a9a9"]},y:{formatter:e=>`${e} minutes`}}},i=[{name:s,data:t.map((e=>e[s]||0)),color:"#71d3c8"},{name:a,data:t.map((e=>e[a]||0)),color:"#dbaa32"}];return(0,B.jsx)(be.A,{title:"Travel Time Reliability",children:(0,B.jsx)(H.A,{options:n,series:i,type:"bar",height:250})})})),Xe=(0,D.A)({palette:{mode:"dark"}}),et=()=>{const[e,t]=(0,a.useState)([]),[s,A]=(0,a.useState)([]),[S,b]=(0,a.useState)([]),[j,w]=(0,a.useState)([]),[T,I]=(0,a.useState)([]),[D,N]=(0,a.useState)([]),[H,Z]=(0,a.useState)([]),[W,U]=(0,a.useState)([]),[O,G]=(0,a.useState)(!0),[Y,q]=(0,a.useState)({latitude:37.7577,longitude:-122.4376}),[V,J]=(0,a.useState)(9),[K,Q]=(0,a.useState)(!0),[X,ee]=(0,a.useState)([]),[te,ne]=(0,a.useState)(!0),[ie,le]=(0,a.useState)(!1),[ce,de]=(0,a.useState)(!1),[ue,pe]=(0,a.useState)(""),[he,ge]=(0,a.useState)("1"),[me,xe]=(0,a.useState)(null),[ye,fe]=(0,a.useState)(null),[ve,Ae]=(0,a.useState)("Realtime"),[je,we]=(0,a.useState)("Yesterday Same Time"),[Te,Ie]=(0,a.useState)(new Date),[Ce,ke]=(0,a.useState)(new Date),[Ee,De]=(0,a.useState)(new Date),[Re,Le]=(0,a.useState)(new Date),[Pe,Fe]=(0,a.useState)([]),[Ne,He]=(0,a.useState)([]),[Be,Ze]=(0,a.useState)(null),[We,Ue]=(0,a.useState)([]),[Oe,Ge]=(0,a.useState)(!1),[Ye,_e]=(0,a.useState)([]),[qe,et]=(0,a.useState)([]),[tt,st]=(0,a.useState)([]),[at,rt]=(0,a.useState)(0),[ot,nt]=(0,a.useState)(null),[it,lt]=(0,a.useState)(!1),[ct,dt]=(0,a.useState)(!1),[ut,pt]=(0,a.useState)([]),[ht,gt]=(0,a.useState)([]),[mt,xt]=(0,a.useState)([]),[yt,ft]=(0,a.useState)([]),[vt,At]=(0,a.useState)(!0),St=e=>{nt(e)};(0,a.useEffect)((()=>{G(!0);const e=_.debounce((e=>{requestAnimationFrame((()=>{const s=e.filter((e=>e&&e.routeId&&""!==e.routeId.trim()));t(s),G(!1)}))}),100);return C.getRealTimeVehicleData((t=>{e(t)})),()=>{e.cancel(),C.closeRealTimeVehicleDataConnection()}}),[]),(0,a.useEffect)((()=>{(async()=>{Q(!0);const e=await C.getTrafficSignals();e.success?(ee(e.devices),Q(!1)):(pe(e.errorMessage),Q(!1))})()}),[]),(0,a.useEffect)((()=>{(async()=>{const e=await C.getTransitRoutesWithHeadsigns();e.success?b(e.routes):pe(e.errorMessage)})(),(async()=>{const e=await C.getGtfsRoutes();e.success?A(e.routes):pe(e.errorMessage)})(),(async()=>{const e=await C.getGtfsTrips();e.success?w(e.trips):pe(e.errorMessage)})(),(async()=>{const e=await C.getZones();e.success?U(e.zones):console.error("Error fetching zones:",e.errorMessage)})()}),[]);const bt=async()=>{lt(!1);try{const e=await C.getZones();e.success?U(e.zones):console.error("Failed to fetch zones:",e.errorMessage)}catch(ue){console.error("Error fetching zones:",ue)}},jt=(t,s)=>{_e(t),pt(s);let a=[];t.forEach((t=>{const s=e.filter((e=>{const s=j.find((t=>t.tripId===e.tripId));return e.routeId===t.routeId&&s&&t.tripHeadsign===s.tripHeadsign})).map((e=>`vehicle-${e.vehicleId}-${e.routeId}-${e.tripId}`));a=[...a,...s]})),st(a)},wt=(e,t)=>{Ue(e),t&&gt(t),console.log("Zones selected:",e),console.log("Zone indices selected:",t)};(0,a.useEffect)((()=>{let t=[...tt];t=t.filter((t=>e.some((e=>`vehicle-${e.vehicleId}-${e.routeId}-${e.tripId}`===t)))),Ye.forEach((s=>{e.filter((e=>{const t=j.find((t=>t.tripId===e.tripId));return e.routeId===s.routeId&&t&&s.tripHeadsign===t.tripHeadsign})).map((e=>`vehicle-${e.vehicleId}-${e.routeId}-${e.tripId}`)).forEach((e=>{t.includes(e)||t.push(e)}))})),t.length!==tt.length&&st(t);const s=e.filter((e=>{const s=`vehicle-${e.vehicleId}-${e.routeId}-${e.tripId}`;return t.includes(s)}));et(s)}),[e,Ye,tt,j]);const[Tt,It]=(0,a.useState)("1");(0,a.useEffect)((()=>{S.length>0&&0===Ye.length&&jt(S)}),[S]),(0,a.useEffect)((()=>{if(W.length>0&&0===We.length&&!Oe){const e=W.map((e=>e)),t=Array.from({length:W.length},((e,t)=>t));wt(e,t),Ge(!0)}}),[W,Oe]);return(0,B.jsx)(M.A,{theme:Xe,children:(0,B.jsx)(R.$,{dateAdapter:L.h,children:(0,B.jsx)(r.A,{sx:{display:"flex",flexDirection:"column",height:"91vh",overflow:"hidden"},children:(0,B.jsxs)(o.Ay,{container:!0,spacing:3,sx:{flex:1,overflow:"hidden",height:"100%"},children:[(0,B.jsx)(o.Ay,{item:!0,xs:12,md:2,children:(0,B.jsxs)(y.Ay,{value:he,children:[(0,B.jsx)(r.A,{sx:{borderBottom:1,borderColor:"divider"},children:(0,B.jsxs)(f.A,{onChange:(e,t)=>{const s=he;ge(t),console.log(`Tab switched from ${s} to ${t}`)},"aria-label":"lab API tabs example",children:[(0,B.jsx)(x.A,{icon:(0,B.jsx)(k.A,{}),value:"1",title:"Signals"}),(0,B.jsx)(x.A,{icon:(0,B.jsx)($.A,{}),value:"2",title:"Transit"}),(0,B.jsx)(x.A,{icon:(0,B.jsx)(z.A,{}),value:"3",title:"Emergency"}),(0,B.jsx)(x.A,{icon:(0,B.jsx)(E.A,{}),value:"4",title:"Zones"})]})}),(0,B.jsx)(v.A,{value:"1",sx:{padding:0},children:!O&&(0,B.jsx)(r.A,{sx:{display:"flex",flexDirection:"row",height:"100%",width:"100%"},children:(0,B.jsx)(be.A,{content:!1,sx:{flex:1,boxSizing:"border-box",width:"100%"},children:(0,B.jsx)($e,{onDeviceSelect:e=>{q({latitude:e.latitude,longitude:e.longitude}),J(16)},signals:X,onSignalSelectionChange:(e,t)=>{console.log("Signal selection changed:",e,t),xt(e||[]),ft(t||[])},selectedSignalIds:mt,sx:{height:"100%"}})})})}),(0,B.jsx)(v.A,{value:"2",sx:{padding:0},children:(0,B.jsx)(ze,{routes:S,onRouteSelectionChange:jt,initiallyLoaded:ct,setInitiallyLoaded:dt,selectedIndices:ut})}),(0,B.jsx)(v.A,{value:"3",sx:{padding:0},children:(0,B.jsx)(n.A,{variant:"h6",sx:{marginTop:2},children:"No Emergency Vehicles"})}),(0,B.jsx)(v.A,{value:"4",sx:{padding:0},children:(0,B.jsx)("div",{children:(0,B.jsx)(Me,{zones:W,onZoneSelectionChange:wt,onZoneEdit:e=>{Ze(e),rt(1)},onAddZone:()=>{Ze({id:null,name:"",type:1,geometry:null}),rt(1)},initiallyLoaded:Oe,setInitiallyLoaded:Ge,selectedIndices:ht})})})]})}),!O&&(0,B.jsx)(o.Ay,{item:!0,xs:12,md:K?3.9:vt?8:9.5,sx:{height:{xs:"50%",md:"100%"},transition:"all 0.3s ease"},children:(0,B.jsxs)(y.Ay,{value:Tt,children:[(0,B.jsx)(r.A,{sx:{borderBottom:1,borderColor:"divider"},children:(0,B.jsxs)(f.A,{onChange:(e,t)=>{It(t)},children:[(0,B.jsx)(x.A,{label:"Map",value:"1"}),(0,B.jsx)(x.A,{label:"Report",value:"2"})]})}),(0,B.jsxs)(r.A,{sx:{position:"relative",height:"100%",width:"100%"},children:[(0,B.jsx)(r.A,{sx:{position:"absolute",top:0,left:0,width:"100%",height:"100%",display:"1"===Tt?"flex":"none",flexDirection:"column"},children:(0,B.jsxs)(r.A,{sx:{width:"100%",display:"flex",flexDirection:"column",height:"100%"},children:[(0,B.jsx)(r.A,{sx:{flexGrow:1},children:(0,B.jsx)(be.A,{content:!1,sx:{width:"100%",height:"100%"},children:(0,B.jsx)(Se,{center:Y,zoom:V,isSignalListExpanded:K,vehicles:qe,signals:X,selectedSignalIds:mt,stops:T,stopTimes:D,shapes:H,dataLoading:ie,gtfsRoutes:s,gtfsTrips:j,selectedZone:Be,selectedZones:We,zones:W,polygonGeometry:ot,onGeometryChange:St,editable:it,sx:{width:"100%"}})})}),(0,B.jsx)(r.A,{sx:{flexShrink:0,height:"30vh",mt:2},children:(0,B.jsx)(Ve,{vehicles:qe,onVehicleSelect:t=>{const s=e.find((e=>e.vehicleId===t));if(s){q({latitude:s.latitude,longitude:s.longitude}),J(16),le(!0);const e=String(s.tripId);Promise.all([C.getStopsByTripId(e),C.getStopTimesByTripId(e),C.getShapesByTripId(e)]).then((e=>{let[t,s,a]=e;I(t.success?t.stops:[]),N(s.success?s.stopTimes:[]),Z(a.success?a.shapes:[])})).catch((e=>{console.error("Error fetching vehicle data:",e),I([]),N([]),Z([])})).finally((()=>{le(!1)}))}else console.error("Vehicle not found:",t)},selectedZone:Be,onEditGeometry:e=>{if(console.log("Current geometry type:",typeof e.geometry),console.log("Current geometry value:",e.geometry),!e.geometry)return nt(null),void lt(!0);Ze(e),e.geometry&&"object"===typeof e.geometry&&e.geometry.type?nt(e.geometry):nt((0,ae.nI)(e.geometry)),lt(!0)},onGeometryChange:St,updatedGeometry:ot,onZoneUpdate:bt,defaultTab:at,onZoneDelete:()=>{Ze(null),bt()}})})]})}),(0,B.jsx)(r.A,{sx:{position:"absolute",top:0,left:0,width:"100%",height:"100%",display:"2"===Tt?"flex":"none",flexDirection:"column",overflow:"auto"},children:(0,B.jsx)(se,{currentTab:he,selectedRoutes:Ye,selectedSignals:yt})})]})]})}),(0,B.jsx)(o.Ay,{item:!0,xs:12,md:vt?2:.3,sx:{transition:"all 0.3s ease",overflow:"hidden",display:"flex",flexDirection:"column"},children:(0,B.jsx)(be.A,{title:(0,B.jsxs)(r.A,{sx:{display:"flex",alignItems:"center",justifyContent:"flex-start",minWidth:vt?"auto":"40px",maxWidth:vt?"auto":"10px"},children:[(0,B.jsx)(i.A,{onClick:()=>{At(!vt),setTimeout((()=>{window.dispatchEvent(new Event("resize"))}),300)},size:"small",sx:{mr:vt?1:0,transition:"transform 0.3s ease"},"aria-label":vt?"Collapse metrics panel":"Expand metrics panel",children:vt?(0,B.jsx)(oe.A,{}):(0,B.jsx)(re.A,{})}),(0,B.jsx)(n.A,{variant:"h6",component:"div",sx:{opacity:vt?1:0,transition:"opacity 0.3s ease",whiteSpace:"nowrap"},children:"Performance Metrics"})]}),sx:{overflow:"hidden",height:"100%","& .MuiCardContent-root":{display:vt?"block":"none",transition:"all 0.3s ease"}},children:vt&&(0,B.jsxs)(l.A,{spacing:3,sx:{paddingRight:"15px"},children:[(0,B.jsxs)(o.Ay,{container:!0,spacing:2,children:[(0,B.jsx)(o.Ay,{item:!0,xs:6,children:(0,B.jsxs)(c.A,{fullWidth:!0,children:[(0,B.jsx)(d.A,{id:"compare-label",children:"Compare"}),(0,B.jsxs)(u.A,{labelId:"compare-label",value:ve,label:"Compare",onChange:e=>{Ae(e.target.value);let t="";"Realtime"===e.target.value?t="Yesterday Same Time":"Yesterday"===e.target.value?t="Last week Same Day":"Period-1"===e.target.value&&(t="Period-2"),we(t)},children:[(0,B.jsx)(p.A,{value:"Realtime",children:"Last Hour"}),(0,B.jsx)(p.A,{value:"Yesterday",children:"Yesterday"}),(0,B.jsx)(p.A,{value:"Period-1",children:"Period-1"})]})]})}),(0,B.jsx)(o.Ay,{item:!0,xs:6,children:(0,B.jsxs)(c.A,{fullWidth:!0,children:[(0,B.jsx)(d.A,{id:"compare-to-label",children:"to"}),(0,B.jsx)(u.A,{labelId:"compare-to-label",value:je,label:"to",onChange:e=>{we(e.target.value)},children:"Realtime"===ve?[(0,B.jsx)(p.A,{value:"Yesterday Same Time",children:"Yesterday Same Time"},"Yesterday Same Time"),(0,B.jsx)(p.A,{value:"Last week Same Day",children:"Last week Same Day"},"Last week Same Day")]:"Yesterday"===ve?(0,B.jsx)(p.A,{value:"Last week Same Day",children:"Last week Same Day"}):"Period-1"===ve?(0,B.jsx)(p.A,{value:"Period-2",children:"Period-2"}):null})]})})]}),"Period-1"===ve&&(0,B.jsxs)(o.Ay,{container:!0,spacing:2,sx:{paddingRight:"15px"},children:[(0,B.jsx)(o.Ay,{item:!0,xs:6,children:(0,B.jsx)(P.l,{label:"Period-1",value:Te,onChange:e=>Ie(e),renderInput:e=>(0,B.jsx)(h.A,{...e})})}),(0,B.jsx)(o.Ay,{item:!0,xs:6,children:(0,B.jsx)(P.l,{label:"Period-2",value:Ce,onChange:e=>ke(e),renderInput:e=>(0,B.jsx)(h.A,{...e})})})]}),"Period-1"===ve&&(0,B.jsxs)(o.Ay,{container:!0,spacing:2,sx:{paddingRight:"15px"},children:[(0,B.jsx)(o.Ay,{item:!0,xs:6,children:(0,B.jsx)(F.A,{label:"Start Time",value:Ee,onChange:e=>De(e),renderInput:e=>(0,B.jsx)(h.A,{...e})})}),(0,B.jsx)(o.Ay,{item:!0,xs:6,children:(0,B.jsx)(F.A,{label:"End Time",value:Re,onChange:e=>Le(e),renderInput:e=>(0,B.jsx)(h.A,{...e})})})]}),(0,B.jsx)(g.A,{variant:"outlined",color:"primary",onClick:()=>{"Realtime"!==ve&&Re<=Ee?alert("End time must be later than start time"):me&&(async()=>{de(!0);const e=(e,t)=>new Date(e.getTime()-60*t*60*1e3);let t,s,a,r;const o=new Date;"Realtime"===ve?(s=new Date,t=new Date(s.getTime()-36e5),"Yesterday Same Time"===je?(a=new Date(t.getTime()-864e5),r=new Date(s.getTime()-864e5)):"Last week Same Day"===je&&(a=new Date(t.getTime()-6048e5),r=new Date(s.getTime()-6048e5))):"Yesterday"===ve?(t=new Date(o.getTime()-864e5),s=new Date,"Last week Same Day"===je&&(a=new Date(o.getTime()-6048e5),r=new Date(o.getTime()-5184e5))):"Period-1"===ve&&(t=new Date(Te),s=new Date(Te),a=new Date(Ce),r=new Date(Ce),t.setHours(Ee.getHours(),Ee.getMinutes(),Ee.getSeconds()),s.setHours(Re.getHours(),Re.getMinutes(),Re.getSeconds()),a.setHours(Ee.getHours(),Ee.getMinutes(),Ee.getSeconds()),r.setHours(Re.getHours(),Re.getMinutes(),Re.getSeconds())),t=e(t,4),s=e(s,4),a=e(a,4),r=e(r,4);try{const[e,o]=await Promise.all([C.getAvgSpeedAndTravelTimeForRouteBetweenTwoDates(me,t.toISOString(),s.toISOString()),C.getAvgSpeedAndTravelTimeForRouteBetweenTwoDates(me,a.toISOString(),r.toISOString())]);if(e.success&&o.success){const t=(e.data.averageTravelTime/60).toFixed(1),s=(o.data.averageTravelTime/60).toFixed(1),a=e.data.averageSpeed.toFixed(1),r=o.data.averageSpeed.toFixed(1);Fe([{name:"Avg. Travel Time",[ve]:parseFloat(t),[je]:parseFloat(s)}]),He([{name:"Avg. Speed",[ve]:parseFloat(a),[je]:parseFloat(r)}])}else console.error("API response not successful:",e,o)}catch(ue){console.error("Error fetching comparison data:",ue)}de(!1)})()},children:"Apply"}),ce?(0,B.jsx)(r.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"350px"},children:(0,B.jsx)(m.A,{})}):(0,B.jsxs)(B.Fragment,{children:[(0,B.jsx)(Je,{data:Pe,compareOption:ve,compareToOption:je}),(0,B.jsx)(Ke,{data:Ne,compareOption:ve,compareToOption:je}),(0,B.jsx)(Qe,{data:Ne,compareOption:ve,compareToOption:je})]})]})})})]})})})})}}}]);